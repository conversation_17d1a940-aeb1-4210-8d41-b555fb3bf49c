#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关键点双维度分析工具
从连续时间序列和每一帧关键点两个维度分析数据
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
# import seaborn as sns
import json
import os
import argparse
from datetime import datetime
from typing import List, Dict, Tuple, Optional
from scipy import signal
from scipy.spatial.distance import pdist, squareform
from scipy.stats import pearsonr, spearmanr
import warnings
warnings.filterwarnings('ignore')

class KeypointDualDimensionAnalyzer:
    """关键点双维度分析器"""
    
    def __init__(self, fps: float = 30.0):
        """
        初始化分析器
        
        Args:
            fps: 视频帧率
        """
        self.fps = fps
        self.keypoints_sequence = []
        self.timestamps = []
        self.frame_ids = []
        self.analysis_results = {}
        
        # 设置中文字体支持 - 基于系统检测结果
        available_fonts = ['Noto Sans CJK JP', 'AR PL UMing CN', 'AR PL UKai CN']  # 已确认可用
        plt.rcParams['font.sans-serif'] = available_fonts + ['DejaVu Sans', 'sans-serif']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 确保字体正确加载
        try:
            import matplotlib.font_manager as fm
            # 清除字体缓存（兼容不同matplotlib版本）
            try:
                fm.fontManager._rebuild()  # 新版本
            except:
                try:
                    fm._rebuild()  # 旧版本
                except:
                    pass  # 忽略缓存重建错误
            
            print(f"使用中文字体: {available_fonts[0]}")
        except Exception as e:
            print(f"字体配置警告: {e}")
    
    def load_data_from_json(self, json_file: str) -> bool:
        """
        从JSON文件加载关键点数据
        
        Args:
            json_file: JSON文件路径
            
        Returns:
            是否加载成功
        """
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 解析数据格式
            if isinstance(data, list):
                # 格式：[{frame_id, timestamp, keypoints}, ...]
                for frame_data in data:
                    if 'keypoints' in frame_data:
                        self.keypoints_sequence.append(np.array(frame_data['keypoints']))
                        self.timestamps.append(frame_data.get('timestamp', len(self.timestamps)))
                        self.frame_ids.append(frame_data.get('frame_id', len(self.frame_ids)))
            elif isinstance(data, dict):
                # 格式：{frame_id: {keypoints, timestamp}, ...}
                for frame_id, frame_data in sorted(data.items(), key=lambda x: int(x[0])):
                    if 'keypoints' in frame_data:
                        self.keypoints_sequence.append(np.array(frame_data['keypoints']))
                        self.timestamps.append(frame_data.get('timestamp', len(self.timestamps)))
                        self.frame_ids.append(int(frame_id))
            
            print(f"成功加载 {len(self.keypoints_sequence)} 帧关键点数据")
            return True
            
        except Exception as e:
            print(f"加载JSON文件失败: {e}")
            return False
    
    def load_data_from_csv(self, csv_file: str) -> bool:
        """
        从CSV文件加载关键点数据
        
        Args:
            csv_file: CSV文件路径
            
        Returns:
            是否加载成功
        """
        try:
            df = pd.read_csv(csv_file)
            
            print(f"CSV文件列名: {list(df.columns)}")
            
            # 首先检查是否是逐关键点格式
            required_cols = ['frame_id', 'keypoint_id', 'x', 'y']
            if all(col in df.columns for col in required_cols):
                print("检测到逐关键点格式，转换处理...")
                return self.load_data_from_keypoint_csv(csv_file)
            
            # 智能识别列名格式
            frame_id_col = None
            timestamp_col = None
            
            # 尝试找到帧ID列
            for col in df.columns:
                if 'frame' in col.lower() or 'id' in col.lower():
                    frame_id_col = col
                    break
            
            # 尝试找到时间戳列
            for col in df.columns:
                if 'time' in col.lower() or 'timestamp' in col.lower():
                    timestamp_col = col
                    break
            
            # 如果没有找到标准列名，使用第一列作为帧ID
            if frame_id_col is None:
                if len(df.columns) > 0:
                    frame_id_col = df.columns[0]
                    print(f"使用第一列作为帧ID: {frame_id_col}")
            
            # 提取关键点坐标列
            coordinate_cols = []
            
            # 方法1：查找x0,y0,x1,y1格式
            i = 0
            while f'x{i}' in df.columns and f'y{i}' in df.columns:
                coordinate_cols.extend([f'x{i}', f'y{i}'])
                i += 1
            
            # 方法2：如果没有找到x0,y0格式，查找所有数值列（但排除已知的非坐标列）
            if not coordinate_cols:
                numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
                # 排除已知的非坐标列
                exclude_cols = [frame_id_col, timestamp_col, 'confidence', 'std_x', 'std_y', 
                              'velocity', 'acceleration', 'instability_score', 'is_unstable']
                for col in exclude_cols:
                    if col in numeric_cols:
                        numeric_cols.remove(col)
                coordinate_cols = numeric_cols
                print(f"使用数值列作为坐标: {coordinate_cols}")
            
            if not coordinate_cols:
                print("错误：未找到坐标列")
                return False
            
            # 确保坐标列数量为偶数（x,y成对）
            if len(coordinate_cols) % 2 != 0:
                coordinate_cols = coordinate_cols[:-1]  # 去掉最后一个，保证成对
            
            print(f"检测到 {len(coordinate_cols)//2} 个关键点")
            
            # 解析数据
            for idx, row in df.iterrows():
                # 提取帧ID
                if frame_id_col:
                    try:
                        frame_id = int(row[frame_id_col])
                    except:
                        frame_id = idx
                else:
                    frame_id = idx
                
                # 提取时间戳
                if timestamp_col and timestamp_col in df.columns:
                    try:
                        timestamp = float(row[timestamp_col])
                    except:
                        timestamp = idx / self.fps
                else:
                    timestamp = idx / self.fps
                
                # 提取关键点坐标
                keypoint_data = []
                for i in range(0, len(coordinate_cols), 2):
                    try:
                        x = float(row[coordinate_cols[i]])
                        y = float(row[coordinate_cols[i+1]])
                        keypoint_data.append([x, y])
                    except:
                        continue
                
                if keypoint_data:
                    self.keypoints_sequence.append(np.array(keypoint_data))
                    self.timestamps.append(timestamp)
                    self.frame_ids.append(frame_id)
            
            print(f"成功加载 {len(self.keypoints_sequence)} 帧关键点数据")
            return True
            
        except Exception as e:
            print(f"加载CSV文件失败: {e}")
            print("尝试其他CSV格式...")
            
            # 尝试加载逐关键点格式的CSV
            return self.load_data_from_keypoint_csv(csv_file)
    
    def load_data_from_keypoint_csv(self, csv_file: str) -> bool:
        """
        从逐关键点格式的CSV文件加载数据
        格式：frame_id,keypoint_id,x,y,confidence,std_x,std_y,velocity,acceleration,instability_score,is_unstable
        
        Args:
            csv_file: CSV文件路径
            
        Returns:
            是否加载成功
        """
        try:
            df = pd.read_csv(csv_file)
            
            print(f"CSV文件列名: {list(df.columns)}")
            print(f"数据样本:\n{df.head()}")
            
            # 检查是否是逐关键点格式
            required_cols = ['frame_id', 'keypoint_id', 'x', 'y']
            if not all(col in df.columns for col in required_cols):
                print("错误：不是逐关键点格式的CSV文件")
                return False
            
            # 按frame_id分组整理数据
            frames = df['frame_id'].unique()
            frames.sort()
            
            print(f"检测到 {len(frames)} 帧数据")
            
            for frame_id in frames:
                frame_data = df[df['frame_id'] == frame_id].copy()
                frame_data = frame_data.sort_values('keypoint_id')  # 按关键点ID排序
                
                # 提取关键点坐标
                keypoints = []
                for _, row in frame_data.iterrows():
                    x = float(row['x'])
                    y = float(row['y'])
                    keypoints.append([x, y])
                
                if keypoints:
                    self.keypoints_sequence.append(np.array(keypoints))
                    
                    # 生成时间戳（如果有timestamp列则使用，否则按帧率计算）
                    if 'timestamp' in df.columns:
                        timestamp = frame_data['timestamp'].iloc[0]
                    else:
                        timestamp = frame_id / self.fps
                    
                    self.timestamps.append(timestamp)
                    self.frame_ids.append(frame_id)
            
            print(f"成功加载 {len(self.keypoints_sequence)} 帧关键点数据")
            if self.keypoints_sequence:
                print(f"每帧关键点数: {len(self.keypoints_sequence[0])}")
            
            return True
            
        except Exception as e:
            print(f"加载逐关键点格式CSV失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def temporal_analysis(self) -> Dict[str, any]:
        """
        时间序列维度分析
        
        Returns:
            时间序列分析结果
        """
        if len(self.keypoints_sequence) < 2:
            return {}
        
        results = {}
        
        # 1. 时间序列稳定性分析
        print("正在进行时间序列稳定性分析...")
        
        # 计算每个关键点的时间序列轨迹
        num_keypoints = len(self.keypoints_sequence[0])
        keypoint_trajectories = []
        
        for kp_idx in range(num_keypoints):
            trajectory = []
            for frame_keypoints in self.keypoints_sequence:
                if kp_idx < len(frame_keypoints):
                    trajectory.append(frame_keypoints[kp_idx])
            keypoint_trajectories.append(np.array(trajectory))
        
        # 计算轨迹统计指标
        trajectory_stats = []
        for kp_idx, trajectory in enumerate(keypoint_trajectories):
            if len(trajectory) > 1:
                # 位置方差
                pos_variance = np.var(trajectory, axis=0)
                
                # 速度统计
                velocities = np.diff(trajectory, axis=0)
                vel_magnitude = np.linalg.norm(velocities, axis=1)
                
                # 加速度统计
                accelerations = np.diff(velocities, axis=0)
                acc_magnitude = np.linalg.norm(accelerations, axis=1)
                
                stats = {
                    'keypoint_id': kp_idx,
                    'position_variance_x': pos_variance[0],
                    'position_variance_y': pos_variance[1],
                    'mean_velocity': np.mean(vel_magnitude),
                    'std_velocity': np.std(vel_magnitude),
                    'max_velocity': np.max(vel_magnitude),
                    'mean_acceleration': np.mean(acc_magnitude),
                    'std_acceleration': np.std(acc_magnitude),
                    'max_acceleration': np.max(acc_magnitude)
                }
                trajectory_stats.append(stats)
        
        results['trajectory_stats'] = trajectory_stats
        
        # 2. 时间序列相关性分析
        print("正在进行时间序列相关性分析...")
        
        # 关键点之间的运动相关性
        correlation_matrix = np.zeros((num_keypoints, num_keypoints))
        
        for i in range(num_keypoints):
            for j in range(num_keypoints):
                if i != j and len(keypoint_trajectories[i]) > 1 and len(keypoint_trajectories[j]) > 1:
                    # 计算运动相关性（基于速度）
                    vel_i = np.linalg.norm(np.diff(keypoint_trajectories[i], axis=0), axis=1)
                    vel_j = np.linalg.norm(np.diff(keypoint_trajectories[j], axis=0), axis=1)
                    
                    if len(vel_i) == len(vel_j) and len(vel_i) > 1:
                        corr, _ = pearsonr(vel_i, vel_j)
                        correlation_matrix[i, j] = corr if not np.isnan(corr) else 0
                else:
                    correlation_matrix[i, j] = 1.0 if i == j else 0.0
        
        results['motion_correlation_matrix'] = correlation_matrix
        
        # 3. 频域分析
        print("正在进行频域分析...")
        
        frequency_analysis = []
        for kp_idx, trajectory in enumerate(keypoint_trajectories):
            if len(trajectory) > 10:
                # 分别分析X和Y方向
                for dim, dim_name in enumerate(['x', 'y']):
                    signal_data = trajectory[:, dim]
                    
                    # 计算功率谱密度
                    freqs, psd = signal.welch(signal_data, fs=self.fps, 
                                            nperseg=min(64, len(signal_data)//2))
                    
                    # 找到主要频率成分
                    dominant_freq_idx = np.argmax(psd[1:]) + 1  # 排除DC成分
                    dominant_freq = freqs[dominant_freq_idx]
                    
                    # 计算频率分布
                    low_freq_power = np.sum(psd[(freqs >= 0) & (freqs < 2)])
                    mid_freq_power = np.sum(psd[(freqs >= 2) & (freqs < 8)])
                    high_freq_power = np.sum(psd[freqs >= 8])
                    total_power = np.sum(psd)
                    
                    freq_stats = {
                        'keypoint_id': kp_idx,
                        'dimension': dim_name,
                        'dominant_frequency': dominant_freq,
                        'low_freq_ratio': low_freq_power / total_power if total_power > 0 else 0,
                        'mid_freq_ratio': mid_freq_power / total_power if total_power > 0 else 0,
                        'high_freq_ratio': high_freq_power / total_power if total_power > 0 else 0,
                        'total_power': total_power
                    }
                    frequency_analysis.append(freq_stats)
        
        results['frequency_analysis'] = frequency_analysis
        
        return results
    
    def spatial_analysis(self) -> Dict[str, any]:
        """
        空间关键点维度分析
        
        Returns:
            空间分析结果
        """
        if len(self.keypoints_sequence) == 0:
            return {}
        
        results = {}
        
        # 1. 空间分布分析
        print("正在进行空间分布分析...")
        
        spatial_stats = []
        for frame_idx, keypoints in enumerate(self.keypoints_sequence):
            if len(keypoints) > 1:
                # 计算关键点的几何特征
                center = np.mean(keypoints, axis=0)
                
                # 计算散布度
                distances_to_center = np.linalg.norm(keypoints - center, axis=1)
                spread = np.std(distances_to_center)
                
                # 计算凸包面积（如果点数足够）
                if len(keypoints) >= 3:
                    from scipy.spatial import ConvexHull
                    try:
                        hull = ConvexHull(keypoints)
                        hull_area = hull.volume  # 2D中volume就是面积
                    except:
                        hull_area = 0
                else:
                    hull_area = 0
                
                # 计算点间距离统计
                pairwise_distances = pdist(keypoints)
                
                stats = {
                    'frame_id': frame_idx,
                    'center_x': center[0],
                    'center_y': center[1],
                    'spread': spread,
                    'hull_area': hull_area,
                    'mean_pairwise_distance': np.mean(pairwise_distances),
                    'std_pairwise_distance': np.std(pairwise_distances),
                    'min_pairwise_distance': np.min(pairwise_distances),
                    'max_pairwise_distance': np.max(pairwise_distances)
                }
                spatial_stats.append(stats)
        
        results['spatial_stats'] = spatial_stats
        
        # 2. 空间一致性分析
        print("正在进行空间一致性分析...")
        
        if len(self.keypoints_sequence) > 1:
            consistency_metrics = []
            
            for i in range(1, len(self.keypoints_sequence)):
                prev_keypoints = self.keypoints_sequence[i-1]
                curr_keypoints = self.keypoints_sequence[i]
                
                if len(prev_keypoints) == len(curr_keypoints) and len(prev_keypoints) > 1:
                    # 计算形状变化
                    prev_distances = pdist(prev_keypoints)
                    curr_distances = pdist(curr_keypoints)
                    
                    # 归一化处理尺度变化
                    prev_normalized = prev_distances / np.mean(prev_distances) if np.mean(prev_distances) > 0 else prev_distances
                    curr_normalized = curr_distances / np.mean(curr_distances) if np.mean(curr_distances) > 0 else curr_distances
                    
                    # 计算形状一致性
                    shape_consistency = 1 - np.mean(np.abs(prev_normalized - curr_normalized))
                    
                    # 计算位置变化
                    position_changes = np.linalg.norm(curr_keypoints - prev_keypoints, axis=1)
                    mean_position_change = np.mean(position_changes)
                    
                    consistency_metrics.append({
                        'frame_id': i,
                        'shape_consistency': max(0, shape_consistency),
                        'mean_position_change': mean_position_change,
                        'max_position_change': np.max(position_changes)
                    })
            
            results['consistency_metrics'] = consistency_metrics
        
        # 3. 关键点质量评估
        print("正在进行关键点质量评估...")
        
        quality_metrics = []
        for frame_idx, keypoints in enumerate(self.keypoints_sequence):
            # 计算关键点分布的质量指标
            if len(keypoints) > 2:
                # 计算点的分散程度
                center = np.mean(keypoints, axis=0)
                distances = np.linalg.norm(keypoints - center, axis=1)
                
                # 均匀性评估（基于最近邻距离）
                pairwise_dist = squareform(pdist(keypoints))
                np.fill_diagonal(pairwise_dist, np.inf)
                nearest_distances = np.min(pairwise_dist, axis=1)
                uniformity = 1 - (np.std(nearest_distances) / np.mean(nearest_distances)) if np.mean(nearest_distances) > 0 else 0
                
                quality_metrics.append({
                    'frame_id': frame_idx,
                    'dispersion': np.std(distances),
                    'uniformity': max(0, uniformity),
                    'compactness': np.mean(distances) / np.max(distances) if np.max(distances) > 0 else 0
                })
        
        results['quality_metrics'] = quality_metrics
        
        return results
    
    def integrated_analysis(self) -> Dict[str, any]:
        """
        综合分析：结合时间和空间维度
        
        Returns:
            综合分析结果
        """
        if len(self.keypoints_sequence) < 2:
            return {}
        
        results = {}
        
        print("正在进行综合分析...")
        
        # 1. 时空耦合分析
        # 分析空间变化与时间的关系
        temporal_spatial_coupling = []
        
        for i in range(1, len(self.keypoints_sequence)):
            if len(self.keypoints_sequence[i]) == len(self.keypoints_sequence[i-1]):
                # 时间间隔
                time_interval = self.timestamps[i] - self.timestamps[i-1] if i < len(self.timestamps) else 1/self.fps
                
                # 空间变化
                spatial_change = np.linalg.norm(self.keypoints_sequence[i] - self.keypoints_sequence[i-1])
                
                # 形状变化
                if len(self.keypoints_sequence[i]) > 1:
                    curr_shape = pdist(self.keypoints_sequence[i])
                    prev_shape = pdist(self.keypoints_sequence[i-1])
                    shape_change = np.linalg.norm(curr_shape - prev_shape)
                else:
                    shape_change = 0
                
                temporal_spatial_coupling.append({
                    'frame_id': i,
                    'time_interval': time_interval,
                    'spatial_change': spatial_change,
                    'shape_change': shape_change,
                    'spatial_velocity': spatial_change / time_interval if time_interval > 0 else 0
                })
        
        results['temporal_spatial_coupling'] = temporal_spatial_coupling
        
        # 2. 异常检测
        # 基于时间和空间的联合异常检测
        anomaly_scores = []
        
        if len(temporal_spatial_coupling) > 0:
            # 计算空间速度的统计分布
            spatial_velocities = [item['spatial_velocity'] for item in temporal_spatial_coupling]
            vel_mean = np.mean(spatial_velocities)
            vel_std = np.std(spatial_velocities)
            
            # 计算形状变化的统计分布
            shape_changes = [item['shape_change'] for item in temporal_spatial_coupling]
            shape_mean = np.mean(shape_changes)
            shape_std = np.std(shape_changes)
            
            for item in temporal_spatial_coupling:
                # Z-score标准化
                vel_zscore = abs(item['spatial_velocity'] - vel_mean) / vel_std if vel_std > 0 else 0
                shape_zscore = abs(item['shape_change'] - shape_mean) / shape_std if shape_std > 0 else 0
                
                # 综合异常分数
                anomaly_score = max(vel_zscore, shape_zscore)
                
                anomaly_scores.append({
                    'frame_id': item['frame_id'],
                    'anomaly_score': anomaly_score,
                    'is_anomaly': anomaly_score > 2.5,  # 2.5个标准差外认为是异常
                    'velocity_zscore': vel_zscore,
                    'shape_zscore': shape_zscore
                })
        
        results['anomaly_detection'] = anomaly_scores
        
        return results
    
    def generate_comprehensive_report(self, output_dir: str = "./") -> str:
        """
        生成综合分析报告
        
        Args:
            output_dir: 输出目录
            
        Returns:
            报告文件路径
        """
        # 执行所有分析
        temporal_results = self.temporal_analysis()
        spatial_results = self.spatial_analysis()
        integrated_results = self.integrated_analysis()
        
        # 保存分析结果
        self.analysis_results = {
            'temporal': temporal_results,
            'spatial': spatial_results,
            'integrated': integrated_results
        }
        
        # 生成报告
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = os.path.join(output_dir, f"keypoint_analysis_report_{timestamp}.txt")
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("关键点双维度分析报告\n")
            f.write("=" * 60 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"数据帧数: {len(self.keypoints_sequence)}\n")
            f.write(f"关键点数量: {len(self.keypoints_sequence[0]) if self.keypoints_sequence else 0}\n")
            f.write(f"时间范围: {self.timestamps[0]:.3f}s - {self.timestamps[-1]:.3f}s\n" if self.timestamps else "")
            f.write("\n")
            
            # 时间序列分析摘要
            if temporal_results:
                f.write("时间序列分析摘要\n")
                f.write("-" * 30 + "\n")
                
                if 'trajectory_stats' in temporal_results:
                    stats = temporal_results['trajectory_stats']
                    avg_vel = np.mean([s['mean_velocity'] for s in stats])
                    avg_acc = np.mean([s['mean_acceleration'] for s in stats])
                    f.write(f"平均速度: {avg_vel:.3f} 像素/帧\n")
                    f.write(f"平均加速度: {avg_acc:.3f} 像素/帧²\n")
                
                if 'frequency_analysis' in temporal_results:
                    freq_stats = temporal_results['frequency_analysis']
                    dominant_freqs = [s['dominant_frequency'] for s in freq_stats]
                    f.write(f"主导频率范围: {np.min(dominant_freqs):.2f} - {np.max(dominant_freqs):.2f} Hz\n")
                
                f.write("\n")
            
            # 空间分析摘要
            if spatial_results:
                f.write("空间分析摘要\n")
                f.write("-" * 30 + "\n")
                
                if 'spatial_stats' in spatial_results:
                    stats = spatial_results['spatial_stats']
                    avg_spread = np.mean([s['spread'] for s in stats])
                    avg_area = np.mean([s['hull_area'] for s in stats if s['hull_area'] > 0])
                    f.write(f"平均散布度: {avg_spread:.3f} 像素\n")
                    f.write(f"平均凸包面积: {avg_area:.3f} 像素²\n" if avg_area > 0 else "")
                
                if 'consistency_metrics' in spatial_results:
                    consistency = spatial_results['consistency_metrics']
                    avg_consistency = np.mean([c['shape_consistency'] for c in consistency])
                    f.write(f"平均形状一致性: {avg_consistency:.3f}\n")
                
                f.write("\n")
            
            # 综合分析摘要
            if integrated_results:
                f.write("综合分析摘要\n")
                f.write("-" * 30 + "\n")
                
                if 'anomaly_detection' in integrated_results:
                    anomalies = integrated_results['anomaly_detection']
                    anomaly_count = sum(1 for a in anomalies if a['is_anomaly'])
                    f.write(f"检测到异常帧数: {anomaly_count} / {len(anomalies)}\n")
                    f.write(f"异常率: {anomaly_count/len(anomalies)*100:.1f}%\n" if anomalies else "")
                
                f.write("\n")
            
            # 结论和建议
            f.write("分析结论\n")
            f.write("-" * 30 + "\n")
            f.write("基于双维度分析的结果:\n")
            
            # 根据分析结果给出建议
            if temporal_results and 'trajectory_stats' in temporal_results:
                stats = temporal_results['trajectory_stats']
                max_vel = max([s['max_velocity'] for s in stats])
                if max_vel > 50:  # 假设阈值
                    f.write("- 检测到高速运动，建议增加滤波强度\n")
                else:
                    f.write("- 运动速度正常，当前滤波参数合适\n")
            
            if integrated_results and 'anomaly_detection' in integrated_results:
                anomalies = integrated_results['anomaly_detection']
                if anomalies:
                    anomaly_rate = sum(1 for a in anomalies if a['is_anomaly']) / len(anomalies)
                    if anomaly_rate > 0.1:
                        f.write("- 异常率较高，建议检查输入数据质量\n")
                    else:
                        f.write("- 数据质量良好，异常率在正常范围内\n")
        
        print(f"分析报告已生成: {report_file}")
        return report_file
    
    def create_visualizations(self, output_dir: str = "./"):
        """
        创建可视化图表
        
        Args:
            output_dir: 输出目录
        """
        if not self.analysis_results:
            print("请先执行分析")
            return
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 1. 时间序列可视化
        if self.analysis_results.get('temporal'):
            self._plot_temporal_analysis(output_dir, timestamp)
        
        # 2. 空间分析可视化
        if self.analysis_results.get('spatial'):
            self._plot_spatial_analysis(output_dir, timestamp)
        
        # 3. 综合分析可视化
        if self.analysis_results.get('integrated'):
            self._plot_integrated_analysis(output_dir, timestamp)
        
        print(f"可视化图表已保存到: {output_dir}")
    
    def _plot_temporal_analysis(self, output_dir: str, timestamp: str):
        """绘制时间序列分析图表"""
        temporal_data = self.analysis_results['temporal']
        
        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('时间序列分析', fontsize=16)
        
        # 1. 轨迹统计
        if 'trajectory_stats' in temporal_data:
            stats = temporal_data['trajectory_stats']
            keypoint_ids = [s['keypoint_id'] for s in stats]
            mean_velocities = [s['mean_velocity'] for s in stats]
            mean_accelerations = [s['mean_acceleration'] for s in stats]
            
            axes[0, 0].bar(keypoint_ids, mean_velocities)
            axes[0, 0].set_title('各关键点平均速度')
            axes[0, 0].set_xlabel('关键点ID')
            axes[0, 0].set_ylabel('速度 (像素/帧)')
            
            axes[0, 1].bar(keypoint_ids, mean_accelerations)
            axes[0, 1].set_title('各关键点平均加速度')
            axes[0, 1].set_xlabel('关键点ID')
            axes[0, 1].set_ylabel('加速度 (像素/帧²)')
        
        # 2. 运动相关性热图
        if 'motion_correlation_matrix' in temporal_data:
            corr_matrix = temporal_data['motion_correlation_matrix']
            im = axes[1, 0].imshow(corr_matrix, cmap='coolwarm', vmin=-1, vmax=1)
            axes[1, 0].set_title('关键点运动相关性')
            axes[1, 0].set_xlabel('关键点ID')
            axes[1, 0].set_ylabel('关键点ID')
            plt.colorbar(im, ax=axes[1, 0])
        
        # 3. 频率分析
        if 'frequency_analysis' in temporal_data:
            freq_data = temporal_data['frequency_analysis']
            dominant_freqs = [f['dominant_frequency'] for f in freq_data]
            
            axes[1, 1].hist(dominant_freqs, bins=20, alpha=0.7)
            axes[1, 1].set_title('主导频率分布')
            axes[1, 1].set_xlabel('频率 (Hz)')
            axes[1, 1].set_ylabel('频次')
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, f'temporal_analysis_{timestamp}.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_spatial_analysis(self, output_dir: str, timestamp: str):
        """绘制空间分析图表"""
        spatial_data = self.analysis_results['spatial']
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('空间分析', fontsize=16)
        
        # 1. 空间分布变化
        if 'spatial_stats' in spatial_data:
            stats = spatial_data['spatial_stats']
            frame_ids = [s['frame_id'] for s in stats]
            spreads = [s['spread'] for s in stats]
            hull_areas = [s['hull_area'] for s in stats if s['hull_area'] > 0]
            
            axes[0, 0].plot(frame_ids, spreads)
            axes[0, 0].set_title('关键点散布度变化')
            axes[0, 0].set_xlabel('帧ID')
            axes[0, 0].set_ylabel('散布度 (像素)')
            
            if hull_areas:
                axes[0, 1].plot(frame_ids[:len(hull_areas)], hull_areas)
                axes[0, 1].set_title('凸包面积变化')
                axes[0, 1].set_xlabel('帧ID')
                axes[0, 1].set_ylabel('面积 (像素²)')
        
        # 2. 空间一致性
        if 'consistency_metrics' in spatial_data:
            consistency = spatial_data['consistency_metrics']
            frame_ids = [c['frame_id'] for c in consistency]
            shape_consistency = [c['shape_consistency'] for c in consistency]
            position_changes = [c['mean_position_change'] for c in consistency]
            
            axes[1, 0].plot(frame_ids, shape_consistency)
            axes[1, 0].set_title('形状一致性')
            axes[1, 0].set_xlabel('帧ID')
            axes[1, 0].set_ylabel('一致性分数')
            
            axes[1, 1].plot(frame_ids, position_changes)
            axes[1, 1].set_title('位置变化')
            axes[1, 1].set_xlabel('帧ID')
            axes[1, 1].set_ylabel('变化量 (像素)')
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, f'spatial_analysis_{timestamp}.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_integrated_analysis(self, output_dir: str, timestamp: str):
        """绘制综合分析图表"""
        integrated_data = self.analysis_results['integrated']
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('综合分析', fontsize=16)
        
        # 1. 时空耦合分析
        if 'temporal_spatial_coupling' in integrated_data:
            coupling = integrated_data['temporal_spatial_coupling']
            frame_ids = [c['frame_id'] for c in coupling]
            spatial_velocities = [c['spatial_velocity'] for c in coupling]
            shape_changes = [c['shape_change'] for c in coupling]
            
            axes[0, 0].plot(frame_ids, spatial_velocities)
            axes[0, 0].set_title('空间速度')
            axes[0, 0].set_xlabel('帧ID')
            axes[0, 0].set_ylabel('速度 (像素/秒)')
            
            axes[0, 1].plot(frame_ids, shape_changes)
            axes[0, 1].set_title('形状变化')
            axes[0, 1].set_xlabel('帧ID')
            axes[0, 1].set_ylabel('变化量')
        
        # 2. 异常检测
        if 'anomaly_detection' in integrated_data:
            anomalies = integrated_data['anomaly_detection']
            frame_ids = [a['frame_id'] for a in anomalies]
            anomaly_scores = [a['anomaly_score'] for a in anomalies]
            is_anomaly = [a['is_anomaly'] for a in anomalies]
            
            # 异常分数图
            colors = ['red' if is_anom else 'blue' for is_anom in is_anomaly]
            axes[1, 0].scatter(frame_ids, anomaly_scores, c=colors, alpha=0.6)
            axes[1, 0].axhline(y=2.5, color='red', linestyle='--', label='异常阈值')
            axes[1, 0].set_title('异常检测')
            axes[1, 0].set_xlabel('帧ID')
            axes[1, 0].set_ylabel('异常分数')
            axes[1, 0].legend()
            
            # 异常分布饼图
            anomaly_count = sum(is_anomaly)
            normal_count = len(is_anomaly) - anomaly_count
            
            axes[1, 1].pie([normal_count, anomaly_count], 
                          labels=['正常', '异常'], 
                          autopct='%1.1f%%',
                          colors=['lightblue', 'lightcoral'])
            axes[1, 1].set_title('异常分布')
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, f'integrated_analysis_{timestamp}.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='关键点双维度分析工具')
    parser.add_argument('-i', '--input', required=True, help='输入数据文件路径（JSON或CSV格式）')
    parser.add_argument('-o', '--output', default='./', help='输出目录')
    parser.add_argument('--fps', type=float, default=10.0, help='视频帧率')
    parser.add_argument('--format', choices=['json', 'csv'], help='数据格式（自动检测）')
    parser.add_argument('--no-viz', action='store_true', help='不生成可视化图表')
    
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output, exist_ok=True)
    
    # 初始化分析器
    analyzer = KeypointDualDimensionAnalyzer(fps=args.fps)
    
    # 加载数据
    input_file = args.input
    if args.format == 'json' or input_file.endswith('.json'):
        success = analyzer.load_data_from_json(input_file)
    elif args.format == 'csv' or input_file.endswith('.csv'):
        success = analyzer.load_data_from_csv(input_file)
    else:
        print("无法确定文件格式，请指定--format参数")
        return
    
    if not success:
        print("数据加载失败")
        return
    
    # 生成分析报告
    report_file = analyzer.generate_comprehensive_report(args.output)
    
    # 生成可视化图表
    if not args.no_viz:
        analyzer.create_visualizations(args.output)
    
    print(f"分析完成！报告文件: {report_file}")


if __name__ == "__main__":
    main() 