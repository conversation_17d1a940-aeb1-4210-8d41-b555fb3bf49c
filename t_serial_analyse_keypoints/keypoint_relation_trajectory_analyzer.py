#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关键点关系与轨迹分析工具
专门分析：
1. 连续帧间不同关键点的相对关系变化
2. 单一关键点与其历史轨迹的对比分析
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import os
import argparse
from datetime import datetime
from typing import List, Dict, Tuple, Optional, Set
from scipy.spatial.distance import pdist, squareform
from scipy.stats import pearsonr
import warnings
warnings.filterwarnings('ignore')

class KeypointRelationTrajectoryAnalyzer:
    """关键点关系与轨迹分析器"""
    
    def __init__(self, fps: float = 10.0, history_window: int = 10):
        """
        初始化分析器
        
        Args:
            fps: 视频帧率
            history_window: 历史轨迹分析窗口大小
        """
        self.fps = fps
        self.history_window = history_window
        self.data = None
        self.frames_data = {}  # {frame_id: DataFrame}
        self.keypoint_trajectories = {}  # {keypoint_id: [(frame_id, x, y, ...), ...]}
        
        # 设置中文字体支持
        available_fonts = ['Noto Sans CJK JP', 'AR PL UMing CN', 'AR PL UKai CN']
        plt.rcParams['font.sans-serif'] = available_fonts + ['DejaVu Sans', 'sans-serif']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 定义重要的关键点对（基于面部关键点的常见拓扑）
        self.important_pairs = [
            # 眼部关键点对
            (0, 4),   # 左眼外角到左眼内角
            (5, 9),   # 右眼内角到右眼外角
            (0, 5),   # 左右眼外角
            (4, 9),   # 左右眼内角
            
            # 眉毛关键点对
            (12, 14), # 左眉到右眉
            (13, 15), # 左眉中到右眉中
            
            # 鼻部关键点对
            (10, 11), # 鼻部中心线
            
            # 嘴部关键点对
            (16, 17), # 嘴角对
            
            # 面部轮廓对
            (18, 22), # 面部左右轮廓
        ]
        
        print(f"关键点关系与轨迹分析器初始化完成")
        print(f"帧率: {fps} FPS")
        print(f"历史窗口: {history_window} 帧")
        print(f"预定义重要关键点对: {len(self.important_pairs)} 对")
    
    def load_data(self, csv_file: str) -> bool:
        """
        加载关键点数据
        
        Args:
            csv_file: CSV文件路径
            
        Returns:
            是否加载成功
        """
        try:
            print(f"正在加载数据文件: {csv_file}")
            self.data = pd.read_csv(csv_file)
            
            print(f"数据加载成功:")
            print(f"  总记录数: {len(self.data)}")
            print(f"  帧数范围: {self.data['frame_id'].min()} - {self.data['frame_id'].max()}")
            print(f"  关键点ID范围: {self.data['keypoint_id'].min()} - {self.data['keypoint_id'].max()}")
            
            # 按帧分组数据
            self._organize_data_by_frame()
            
            # 按关键点分组轨迹数据
            self._organize_trajectory_data()
            
            return True
            
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    def _organize_data_by_frame(self):
        """按帧组织数据"""
        print("正在按帧组织数据...")
        
        for frame_id in self.data['frame_id'].unique():
            frame_data = self.data[self.data['frame_id'] == frame_id].copy()
            frame_data = frame_data.sort_values('keypoint_id')
            self.frames_data[frame_id] = frame_data
        
        print(f"  按帧组织完成，共 {len(self.frames_data)} 帧")
    
    def _organize_trajectory_data(self):
        """按关键点组织轨迹数据"""
        print("正在按关键点组织轨迹数据...")
        
        for keypoint_id in self.data['keypoint_id'].unique():
            trajectory = self.data[self.data['keypoint_id'] == keypoint_id].copy()
            trajectory = trajectory.sort_values('frame_id')
            
            # 转换为轨迹点列表
            trajectory_points = []
            for _, row in trajectory.iterrows():
                trajectory_points.append((
                    row['frame_id'], row['x'], row['y'], 
                    row['confidence'], row['velocity'], row['acceleration']
                ))
            
            self.keypoint_trajectories[keypoint_id] = trajectory_points
        
        print(f"  轨迹组织完成，共 {len(self.keypoint_trajectories)} 个关键点")
    
    def analyze_inter_keypoint_relations(self, target_pairs: Optional[List[Tuple[int, int]]] = None) -> Dict:
        """
        分析连续帧间关键点对比关系
        
        Args:
            target_pairs: 要分析的关键点对列表，默认使用预定义的重要对
            
        Returns:
            关键点关系分析结果
        """
        print("开始分析连续帧间关键点关系...")
        
        if target_pairs is None:
            target_pairs = self.important_pairs
        
        results = {
            'distance_changes': {},
            'angle_changes': {},
            'relative_motion': {},
            'shape_stability': {}
        }
        
        frame_ids = sorted(self.frames_data.keys())
        
        for pair in target_pairs:
            kp1_id, kp2_id = pair
            pair_name = f"KP{kp1_id}-KP{kp2_id}"
            
            print(f"  分析关键点对: {pair_name}")
            
            # 初始化该对的分析结果
            results['distance_changes'][pair_name] = []
            results['angle_changes'][pair_name] = []
            results['relative_motion'][pair_name] = []
            
            # 分析连续帧间的变化
            for i in range(len(frame_ids) - 1):
                curr_frame_id = frame_ids[i]
                next_frame_id = frame_ids[i + 1]
                
                # 获取当前帧和下一帧的关键点数据
                curr_frame = self.frames_data[curr_frame_id]
                next_frame = self.frames_data[next_frame_id]
                
                # 提取关键点坐标
                try:
                    curr_kp1 = curr_frame[curr_frame['keypoint_id'] == kp1_id].iloc[0]
                    curr_kp2 = curr_frame[curr_frame['keypoint_id'] == kp2_id].iloc[0]
                    next_kp1 = next_frame[next_frame['keypoint_id'] == kp1_id].iloc[0]
                    next_kp2 = next_frame[next_frame['keypoint_id'] == kp2_id].iloc[0]
                except IndexError:
                    continue  # 跳过缺失关键点的帧
                
                # 计算距离变化
                curr_distance = np.sqrt((curr_kp1['x'] - curr_kp2['x'])**2 + 
                                      (curr_kp1['y'] - curr_kp2['y'])**2)
                next_distance = np.sqrt((next_kp1['x'] - next_kp2['x'])**2 + 
                                      (next_kp1['y'] - next_kp2['y'])**2)
                distance_change = next_distance - curr_distance
                distance_change_ratio = distance_change / curr_distance if curr_distance > 0 else 0
                
                # 计算角度变化
                curr_angle = np.arctan2(curr_kp2['y'] - curr_kp1['y'], 
                                       curr_kp2['x'] - curr_kp1['x'])
                next_angle = np.arctan2(next_kp2['y'] - next_kp1['y'], 
                                       next_kp2['x'] - next_kp1['x'])
                angle_change = np.degrees(next_angle - curr_angle)
                
                # 计算相对运动
                kp1_motion = np.array([next_kp1['x'] - curr_kp1['x'], 
                                     next_kp1['y'] - curr_kp1['y']])
                kp2_motion = np.array([next_kp2['x'] - curr_kp2['x'], 
                                     next_kp2['y'] - curr_kp2['y']])
                relative_motion = np.linalg.norm(kp2_motion - kp1_motion)
                
                # 记录结果
                results['distance_changes'][pair_name].append({
                    'frame_id': curr_frame_id,
                    'distance_change': distance_change,
                    'distance_change_ratio': distance_change_ratio,
                    'curr_distance': curr_distance,
                    'next_distance': next_distance
                })
                
                results['angle_changes'][pair_name].append({
                    'frame_id': curr_frame_id,
                    'angle_change': angle_change,
                    'curr_angle': np.degrees(curr_angle),
                    'next_angle': np.degrees(next_angle)
                })
                
                results['relative_motion'][pair_name].append({
                    'frame_id': curr_frame_id,
                    'relative_motion': relative_motion,
                    'kp1_motion_magnitude': np.linalg.norm(kp1_motion),
                    'kp2_motion_magnitude': np.linalg.norm(kp2_motion)
                })
        
        # 计算形状稳定性
        results['shape_stability'] = self._analyze_shape_stability(target_pairs)
        
        print(f"关键点关系分析完成，分析了 {len(target_pairs)} 个关键点对")
        return results
    
    def _analyze_shape_stability(self, target_pairs: List[Tuple[int, int]]) -> Dict:
        """分析形状稳定性"""
        print("  计算形状稳定性...")
        
        shape_stability = {}
        frame_ids = sorted(self.frames_data.keys())
        
        for i in range(len(frame_ids) - 1):
            curr_frame_id = frame_ids[i]
            next_frame_id = frame_ids[i + 1]
            
            curr_frame = self.frames_data[curr_frame_id]
            next_frame = self.frames_data[next_frame_id]
            
            # 提取所有关键点对的距离
            curr_distances = []
            next_distances = []
            
            for kp1_id, kp2_id in target_pairs:
                try:
                    curr_kp1 = curr_frame[curr_frame['keypoint_id'] == kp1_id].iloc[0]
                    curr_kp2 = curr_frame[curr_frame['keypoint_id'] == kp2_id].iloc[0]
                    next_kp1 = next_frame[next_frame['keypoint_id'] == kp1_id].iloc[0]
                    next_kp2 = next_frame[next_frame['keypoint_id'] == kp2_id].iloc[0]
                    
                    curr_dist = np.sqrt((curr_kp1['x'] - curr_kp2['x'])**2 + 
                                       (curr_kp1['y'] - curr_kp2['y'])**2)
                    next_dist = np.sqrt((next_kp1['x'] - next_kp2['x'])**2 + 
                                       (next_kp1['y'] - next_kp2['y'])**2)
                    
                    curr_distances.append(curr_dist)
                    next_distances.append(next_dist)
                except IndexError:
                    continue
            
            if curr_distances and next_distances:
                # 归一化距离
                curr_norm = np.array(curr_distances) / np.mean(curr_distances)
                next_norm = np.array(next_distances) / np.mean(next_distances)
                
                # 计算形状相似度
                shape_similarity = 1 - np.mean(np.abs(curr_norm - next_norm))
                
                shape_stability[curr_frame_id] = {
                    'shape_similarity': max(0, shape_similarity),
                    'mean_distance_change': np.mean(np.abs(np.array(next_distances) - 
                                                          np.array(curr_distances))),
                    'max_distance_change': np.max(np.abs(np.array(next_distances) - 
                                                        np.array(curr_distances)))
                }
        
        return shape_stability 
    
    def analyze_keypoint_trajectories(self, target_keypoints: Optional[List[int]] = None) -> Dict:
        """
        分析单一关键点的历史轨迹
        
        Args:
            target_keypoints: 要分析的关键点ID列表，默认分析所有关键点
            
        Returns:
            关键点轨迹分析结果
        """
        print("开始分析关键点历史轨迹...")
        
        if target_keypoints is None:
            target_keypoints = list(self.keypoint_trajectories.keys())
        
        results = {
            'trajectory_smoothness': {},
            'motion_prediction': {},
            'periodic_patterns': {},
            'anomaly_detection': {},
            'velocity_analysis': {},
            'acceleration_analysis': {}
        }
        
        for kp_id in target_keypoints:
            print(f"  分析关键点 {kp_id} 的轨迹...")
            
            trajectory = self.keypoint_trajectories[kp_id]
            
            # 提取轨迹数据
            frames = [point[0] for point in trajectory]
            x_coords = [point[1] for point in trajectory]
            y_coords = [point[2] for point in trajectory]
            confidences = [point[3] for point in trajectory]
            velocities = [point[4] for point in trajectory]
            accelerations = [point[5] for point in trajectory]
            
            # 1. 轨迹平滑性分析
            results['trajectory_smoothness'][kp_id] = self._analyze_trajectory_smoothness(
                x_coords, y_coords, frames)
            
            # 2. 运动预测分析
            results['motion_prediction'][kp_id] = self._analyze_motion_prediction(
                x_coords, y_coords, frames)
            
            # 3. 周期性模式检测
            results['periodic_patterns'][kp_id] = self._detect_periodic_patterns(
                x_coords, y_coords, velocities, frames)
            
            # 4. 异常检测
            results['anomaly_detection'][kp_id] = self._detect_trajectory_anomalies(
                x_coords, y_coords, velocities, accelerations, frames)
            
            # 5. 速度分析
            results['velocity_analysis'][kp_id] = self._analyze_velocity_patterns(
                velocities, frames)
            
            # 6. 加速度分析
            results['acceleration_analysis'][kp_id] = self._analyze_acceleration_patterns(
                accelerations, frames)
        
        print(f"关键点轨迹分析完成，分析了 {len(target_keypoints)} 个关键点")
        return results
    
    def _analyze_trajectory_smoothness(self, x_coords: List[float], y_coords: List[float], 
                                     frames: List[int]) -> Dict:
        """分析轨迹平滑性"""
        if len(x_coords) < 3:
            return {'smoothness_score': 0, 'jerk_magnitude': 0}
        
        # 计算一阶导数（速度）
        dx = np.diff(x_coords)
        dy = np.diff(y_coords)
        dt = np.diff(frames)
        dt[dt == 0] = 1  # 避免除零
        
        vx = dx / dt
        vy = dy / dt
        
        # 计算二阶导数（加速度）
        if len(vx) > 1:
            dvx = np.diff(vx)
            dvy = np.diff(vy)
            dt2 = dt[1:]
            dt2[dt2 == 0] = 1
            
            ax = dvx / dt2
            ay = dvy / dt2
            
            # 计算三阶导数（急动度/jerk）
            if len(ax) > 1:
                dax = np.diff(ax)
                day = np.diff(ay)
                dt3 = dt2[1:]
                dt3[dt3 == 0] = 1
                
                jx = dax / dt3
                jy = day / dt3
                
                jerk_magnitude = np.mean(np.sqrt(jx**2 + jy**2))
            else:
                jerk_magnitude = 0
        else:
            jerk_magnitude = 0
        
        # 计算平滑度分数（基于急动度的倒数）
        smoothness_score = 1 / (1 + jerk_magnitude) if jerk_magnitude > 0 else 1
        
        return {
            'smoothness_score': smoothness_score,
            'jerk_magnitude': jerk_magnitude,
            'mean_velocity': np.mean(np.sqrt(vx**2 + vy**2)) if len(vx) > 0 else 0,
            'velocity_variance': np.var(np.sqrt(vx**2 + vy**2)) if len(vx) > 0 else 0
        }
    
    def _analyze_motion_prediction(self, x_coords: List[float], y_coords: List[float], 
                                 frames: List[int]) -> Dict:
        """分析运动预测性"""
        if len(x_coords) < self.history_window + 1:
            return {'prediction_accuracy': 0, 'prediction_errors': []}
        
        prediction_errors = []
        
        # 滑动窗口预测
        for i in range(self.history_window, len(x_coords)):
            # 使用历史窗口数据进行线性预测
            hist_x = x_coords[i-self.history_window:i]
            hist_y = y_coords[i-self.history_window:i]
            hist_frames = frames[i-self.history_window:i]
            
            # 简单线性预测（基于最近两点的趋势）
            if len(hist_x) >= 2:
                dx = hist_x[-1] - hist_x[-2]
                dy = hist_y[-1] - hist_y[-2]
                
                predicted_x = hist_x[-1] + dx
                predicted_y = hist_y[-1] + dy
                
                actual_x = x_coords[i]
                actual_y = y_coords[i]
                
                error = np.sqrt((predicted_x - actual_x)**2 + (predicted_y - actual_y)**2)
                prediction_errors.append({
                    'frame_id': frames[i],
                    'predicted_x': predicted_x,
                    'predicted_y': predicted_y,
                    'actual_x': actual_x,
                    'actual_y': actual_y,
                    'error': error
                })
        
        mean_error = np.mean([e['error'] for e in prediction_errors]) if prediction_errors else 0
        prediction_accuracy = 1 / (1 + mean_error) if mean_error > 0 else 1
        
        return {
            'prediction_accuracy': prediction_accuracy,
            'mean_prediction_error': mean_error,
            'prediction_errors': prediction_errors
        }
    
    def _detect_periodic_patterns(self, x_coords: List[float], y_coords: List[float], 
                                velocities: List[float], frames: List[int]) -> Dict:
        """检测周期性模式"""
        if len(velocities) < 20:  # 需要足够的数据点
            return {'has_periodicity': False, 'dominant_period': 0}
        
        # 使用FFT检测周期性
        from scipy.fft import fft, fftfreq
        
        # 分析速度的周期性
        vel_signal = np.array(velocities)
        
        # 去除DC分量
        vel_signal = vel_signal - np.mean(vel_signal)
        
        # FFT变换
        n = len(vel_signal)
        fft_vals = fft(vel_signal)
        freqs = fftfreq(n, d=1.0/self.fps)
        
        # 只考虑正频率
        pos_freqs = freqs[:n//2]
        pos_fft = np.abs(fft_vals[:n//2])
        
        # 找到主导频率
        if len(pos_fft) > 1:
            # 排除DC分量
            dominant_freq_idx = np.argmax(pos_fft[1:]) + 1
            dominant_freq = pos_freqs[dominant_freq_idx]
            dominant_period = 1.0 / dominant_freq if dominant_freq > 0 else 0
            
            # 判断是否有明显的周期性（主导频率的功率是否足够强）
            total_power = np.sum(pos_fft**2)
            dominant_power = pos_fft[dominant_freq_idx]**2
            periodicity_strength = dominant_power / total_power if total_power > 0 else 0
            
            has_periodicity = periodicity_strength > 0.1  # 阈值可调整
        else:
            dominant_freq = 0
            dominant_period = 0
            has_periodicity = False
            periodicity_strength = 0
        
        return {
            'has_periodicity': has_periodicity,
            'dominant_frequency': dominant_freq,
            'dominant_period': dominant_period,
            'periodicity_strength': periodicity_strength
        }
    
    def _detect_trajectory_anomalies(self, x_coords: List[float], y_coords: List[float], 
                                   velocities: List[float], accelerations: List[float], 
                                   frames: List[int]) -> Dict:
        """检测轨迹异常"""
        anomalies = []
        
        if len(velocities) < 5:
            return {'anomalies': anomalies, 'anomaly_rate': 0}
        
        # 基于速度的异常检测
        vel_mean = np.mean(velocities)
        vel_std = np.std(velocities)
        
        # 基于加速度的异常检测
        acc_mean = np.mean(accelerations) if accelerations else 0
        acc_std = np.std(accelerations) if accelerations else 0
        
        for i, (frame_id, vel, acc) in enumerate(zip(frames, velocities, accelerations)):
            is_anomaly = False
            anomaly_reasons = []
            
            # 速度异常（超过3个标准差）
            if vel_std > 0:
                vel_zscore = abs(vel - vel_mean) / vel_std
                if vel_zscore > 3:
                    is_anomaly = True
                    anomaly_reasons.append(f"速度异常(Z={vel_zscore:.2f})")
            
            # 加速度异常（超过3个标准差）
            if acc_std > 0:
                acc_zscore = abs(acc - acc_mean) / acc_std
                if acc_zscore > 3:
                    is_anomaly = True
                    anomaly_reasons.append(f"加速度异常(Z={acc_zscore:.2f})")
            
            # 位置跳变检测（相对于历史位置的突然变化）
            if i > 0:
                pos_change = np.sqrt((x_coords[i] - x_coords[i-1])**2 + 
                                   (y_coords[i] - y_coords[i-1])**2)
                if pos_change > vel_mean + 3 * vel_std:
                    is_anomaly = True
                    anomaly_reasons.append(f"位置跳变({pos_change:.2f})")
            
            if is_anomaly:
                anomalies.append({
                    'frame_id': frame_id,
                    'x': x_coords[i],
                    'y': y_coords[i],
                    'velocity': vel,
                    'acceleration': acc,
                    'reasons': anomaly_reasons
                })
        
        anomaly_rate = len(anomalies) / len(frames) if frames else 0
        
        return {
            'anomalies': anomalies,
            'anomaly_rate': anomaly_rate,
            'total_anomalies': len(anomalies)
        }
    
    def _analyze_velocity_patterns(self, velocities: List[float], frames: List[int]) -> Dict:
        """分析速度模式"""
        if not velocities:
            return {'mean_velocity': 0, 'velocity_variance': 0}
        
        velocities = np.array(velocities)
        
        return {
            'mean_velocity': np.mean(velocities),
            'median_velocity': np.median(velocities),
            'velocity_variance': np.var(velocities),
            'velocity_std': np.std(velocities),
            'max_velocity': np.max(velocities),
            'min_velocity': np.min(velocities),
            'velocity_range': np.max(velocities) - np.min(velocities)
        }
    
    def _analyze_acceleration_patterns(self, accelerations: List[float], frames: List[int]) -> Dict:
        """分析加速度模式"""
        if not accelerations:
            return {'mean_acceleration': 0, 'acceleration_variance': 0}
        
        accelerations = np.array(accelerations)
        
        return {
            'mean_acceleration': np.mean(accelerations),
            'median_acceleration': np.median(accelerations),
            'acceleration_variance': np.var(accelerations),
            'acceleration_std': np.std(accelerations),
            'max_acceleration': np.max(accelerations),
            'min_acceleration': np.min(accelerations),
            'acceleration_range': np.max(accelerations) - np.min(accelerations)
        }
    
    def generate_comprehensive_analysis(self, output_dir: str = "./", 
                                      relation_pairs: Optional[List[Tuple[int, int]]] = None,
                                      trajectory_keypoints: Optional[List[int]] = None) -> str:
        """
        生成综合分析报告
        
        Args:
            output_dir: 输出目录
            relation_pairs: 要分析的关键点对
            trajectory_keypoints: 要分析轨迹的关键点
            
        Returns:
            报告文件路径
        """
        print("开始生成综合分析...")
        
        # 执行关键点关系分析
        relation_results = self.analyze_inter_keypoint_relations(relation_pairs)
        
        # 执行轨迹分析（默认只分析前10个关键点以避免过多输出）
        if trajectory_keypoints is None:
            trajectory_keypoints = list(range(min(10, len(self.keypoint_trajectories))))
        trajectory_results = self.analyze_keypoint_trajectories(trajectory_keypoints)
        
        # 生成报告
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = os.path.join(output_dir, f"keypoint_relation_trajectory_report_{timestamp}.txt")
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("关键点关系与轨迹分析报告\n")
            f.write("=" * 60 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"数据帧数: {len(self.frames_data)}\n")
            f.write(f"关键点数量: {len(self.keypoint_trajectories)}\n")
            f.write(f"帧率: {self.fps} FPS\n")
            f.write(f"历史窗口: {self.history_window} 帧\n\n")
            
            # 关键点关系分析摘要
            f.write("一、连续帧间关键点关系分析\n")
            f.write("-" * 40 + "\n")
            
            # 距离变化分析
            if 'distance_changes' in relation_results:
                f.write("1. 关键点对距离变化分析:\n")
                for pair_name, changes in relation_results['distance_changes'].items():
                    if changes:
                        distance_changes = [c['distance_change'] for c in changes]
                        mean_change = np.mean(np.abs(distance_changes))
                        max_change = np.max(np.abs(distance_changes))
                        f.write(f"   {pair_name}: 平均变化={mean_change:.2f}像素, 最大变化={max_change:.2f}像素\n")
                f.write("\n")
            
            # 形状稳定性分析
            if 'shape_stability' in relation_results:
                stability_scores = [s['shape_similarity'] for s in relation_results['shape_stability'].values()]
                if stability_scores:
                    mean_stability = np.mean(stability_scores)
                    f.write(f"2. 整体形状稳定性: {mean_stability:.3f} (越接近1越稳定)\n\n")
            
            # 轨迹分析摘要
            f.write("二、关键点历史轨迹分析\n")
            f.write("-" * 40 + "\n")
            
            # 轨迹平滑性
            if 'trajectory_smoothness' in trajectory_results:
                f.write("1. 轨迹平滑性分析:\n")
                for kp_id, smoothness in trajectory_results['trajectory_smoothness'].items():
                    f.write(f"   关键点{kp_id}: 平滑度={smoothness['smoothness_score']:.3f}, "
                           f"急动度={smoothness['jerk_magnitude']:.3f}\n")
                f.write("\n")
            
            # 运动预测性
            if 'motion_prediction' in trajectory_results:
                f.write("2. 运动预测性分析:\n")
                for kp_id, prediction in trajectory_results['motion_prediction'].items():
                    f.write(f"   关键点{kp_id}: 预测准确度={prediction['prediction_accuracy']:.3f}, "
                           f"平均误差={prediction['mean_prediction_error']:.2f}像素\n")
                f.write("\n")
            
            # 周期性模式
            if 'periodic_patterns' in trajectory_results:
                f.write("3. 周期性模式检测:\n")
                for kp_id, pattern in trajectory_results['periodic_patterns'].items():
                    if pattern['has_periodicity']:
                        f.write(f"   关键点{kp_id}: 检测到周期性, 主导周期={pattern['dominant_period']:.2f}秒, "
                               f"强度={pattern['periodicity_strength']:.3f}\n")
                    else:
                        f.write(f"   关键点{kp_id}: 未检测到明显周期性\n")
                f.write("\n")
            
            # 异常检测
            if 'anomaly_detection' in trajectory_results:
                f.write("4. 轨迹异常检测:\n")
                total_anomalies = 0
                for kp_id, anomaly in trajectory_results['anomaly_detection'].items():
                    anomaly_count = anomaly['total_anomalies']
                    anomaly_rate = anomaly['anomaly_rate']
                    total_anomalies += anomaly_count
                    f.write(f"   关键点{kp_id}: 异常帧数={anomaly_count}, 异常率={anomaly_rate:.2%}\n")
                f.write(f"   总异常数: {total_anomalies}\n\n")
            
            # 分析结论
            f.write("三、分析结论与建议\n")
            f.write("-" * 40 + "\n")
            
            # 根据分析结果给出结论
            if 'shape_stability' in relation_results:
                stability_scores = [s['shape_similarity'] for s in relation_results['shape_stability'].values()]
                if stability_scores:
                    mean_stability = np.mean(stability_scores)
                    if mean_stability > 0.9:
                        f.write("• 关键点间关系非常稳定，面部形状保持良好\n")
                    elif mean_stability > 0.8:
                        f.write("• 关键点间关系较稳定，有轻微形变\n")
                    else:
                        f.write("• 关键点间关系不够稳定，建议检查检测算法\n")
            
            if 'trajectory_smoothness' in trajectory_results:
                smoothness_scores = [s['smoothness_score'] for s in trajectory_results['trajectory_smoothness'].values()]
                mean_smoothness = np.mean(smoothness_scores)
                if mean_smoothness > 0.8:
                    f.write("• 关键点轨迹平滑，运动连续性良好\n")
                elif mean_smoothness > 0.6:
                    f.write("• 关键点轨迹基本平滑，有少量抖动\n")
                else:
                    f.write("• 关键点轨迹不够平滑，建议增加滤波处理\n")
            
            if 'anomaly_detection' in trajectory_results:
                total_anomaly_rate = np.mean([a['anomaly_rate'] for a in trajectory_results['anomaly_detection'].values()])
                if total_anomaly_rate > 0.1:
                    f.write("• 检测到较多异常运动，建议检查数据质量\n")
                elif total_anomaly_rate > 0.05:
                    f.write("• 检测到少量异常运动，整体质量良好\n")
                else:
                    f.write("• 关键点运动正常，数据质量优秀\n")
        
        print(f"综合分析报告已生成: {report_file}")
        return report_file
    
    def create_visualizations(self, output_dir: str = "./",
                            relation_pairs: Optional[List[Tuple[int, int]]] = None,
                            trajectory_keypoints: Optional[List[int]] = None):
        """
        创建可视化图表
        
        Args:
            output_dir: 输出目录
            relation_pairs: 要可视化的关键点对
            trajectory_keypoints: 要可视化轨迹的关键点
        """
        print("开始生成可视化图表...")
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 执行分析（如果还没有执行）
        if relation_pairs is None:
            relation_pairs = self.important_pairs[:5]  # 只显示前5对避免图表过于复杂
        if trajectory_keypoints is None:
            trajectory_keypoints = list(range(min(5, len(self.keypoint_trajectories))))
        
        relation_results = self.analyze_inter_keypoint_relations(relation_pairs)
        trajectory_results = self.analyze_keypoint_trajectories(trajectory_keypoints)
        
        # 1. 关键点关系可视化
        self._plot_keypoint_relations(relation_results, output_dir, timestamp)
        
        # 2. 轨迹分析可视化
        self._plot_trajectory_analysis(trajectory_results, trajectory_keypoints, output_dir, timestamp)
        
        print(f"可视化图表已保存到: {output_dir}")
    
    def _plot_keypoint_relations(self, relation_results: Dict, output_dir: str, timestamp: str):
        """绘制关键点关系分析图表"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('关键点关系分析', fontsize=16)
        
        # 1. 距离变化图
        ax1 = axes[0, 0]
        for pair_name, changes in relation_results['distance_changes'].items():
            if changes:
                frames = [c['frame_id'] for c in changes]
                distances = [c['curr_distance'] for c in changes]
                ax1.plot(frames, distances, label=pair_name, alpha=0.7)
        ax1.set_title('关键点对距离变化')
        ax1.set_xlabel('帧ID')
        ax1.set_ylabel('距离 (像素)')
        ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax1.grid(True, alpha=0.3)
        
        # 2. 距离变化率分布
        ax2 = axes[0, 1]
        all_change_ratios = []
        for changes in relation_results['distance_changes'].values():
            if changes:
                change_ratios = [c['distance_change_ratio'] for c in changes]
                all_change_ratios.extend(change_ratios)
        
        if all_change_ratios:
            ax2.hist(all_change_ratios, bins=30, alpha=0.7, edgecolor='black')
        ax2.set_title('距离变化率分布')
        ax2.set_xlabel('距离变化率')
        ax2.set_ylabel('频次')
        ax2.grid(True, alpha=0.3)
        
        # 3. 形状稳定性时序图
        ax3 = axes[1, 0]
        if 'shape_stability' in relation_results:
            frames = list(relation_results['shape_stability'].keys())
            similarities = [relation_results['shape_stability'][f]['shape_similarity'] for f in frames]
            ax3.plot(frames, similarities, 'b-', linewidth=2)
            ax3.axhline(y=0.9, color='g', linestyle='--', label='优秀阈值')
            ax3.axhline(y=0.8, color='y', linestyle='--', label='良好阈值')
        ax3.set_title('形状稳定性')
        ax3.set_xlabel('帧ID')
        ax3.set_ylabel('稳定性分数')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. 相对运动幅度
        ax4 = axes[1, 1]
        for pair_name, motions in relation_results['relative_motion'].items():
            if motions:
                frames = [m['frame_id'] for m in motions]
                rel_motions = [m['relative_motion'] for m in motions]
                ax4.plot(frames, rel_motions, label=pair_name, alpha=0.7)
        ax4.set_title('关键点对相对运动')
        ax4.set_xlabel('帧ID')
        ax4.set_ylabel('相对运动幅度 (像素)')
        ax4.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, f'keypoint_relations_{timestamp}.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_trajectory_analysis(self, trajectory_results: Dict, keypoints: List[int], 
                                output_dir: str, timestamp: str):
        """绘制轨迹分析图表"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('关键点轨迹分析', fontsize=16)
        
        # 1. 轨迹平滑性
        ax1 = axes[0, 0]
        if 'trajectory_smoothness' in trajectory_results:
            kp_ids = list(trajectory_results['trajectory_smoothness'].keys())
            smoothness = [trajectory_results['trajectory_smoothness'][kp]['smoothness_score'] for kp in kp_ids]
            ax1.bar(range(len(kp_ids)), smoothness)
            ax1.set_title('轨迹平滑性')
            ax1.set_xlabel('关键点ID')
            ax1.set_ylabel('平滑度分数')
            ax1.set_xticks(range(len(kp_ids)))
            ax1.set_xticklabels([f'KP{kp}' for kp in kp_ids])
        
        # 2. 预测准确度
        ax2 = axes[0, 1]
        if 'motion_prediction' in trajectory_results:
            kp_ids = list(trajectory_results['motion_prediction'].keys())
            accuracy = [trajectory_results['motion_prediction'][kp]['prediction_accuracy'] for kp in kp_ids]
            ax2.bar(range(len(kp_ids)), accuracy)
            ax2.set_title('运动预测准确度')
            ax2.set_xlabel('关键点ID')
            ax2.set_ylabel('预测准确度')
            ax2.set_xticks(range(len(kp_ids)))
            ax2.set_xticklabels([f'KP{kp}' for kp in kp_ids])
        
        # 3. 速度分布
        ax3 = axes[0, 2]
        if 'velocity_analysis' in trajectory_results:
            all_velocities = []
            for kp_id in keypoints:
                if kp_id in self.keypoint_trajectories:
                    velocities = [point[4] for point in self.keypoint_trajectories[kp_id]]
                    all_velocities.extend(velocities)
            
            if all_velocities:
                ax3.hist(all_velocities, bins=30, alpha=0.7, edgecolor='black')
            ax3.set_title('速度分布')
            ax3.set_xlabel('速度 (像素/帧)')
            ax3.set_ylabel('频次')
        
        # 4. 异常率
        ax4 = axes[1, 0]
        if 'anomaly_detection' in trajectory_results:
            kp_ids = list(trajectory_results['anomaly_detection'].keys())
            anomaly_rates = [trajectory_results['anomaly_detection'][kp]['anomaly_rate'] for kp in kp_ids]
            colors = ['red' if rate > 0.1 else 'orange' if rate > 0.05 else 'green' for rate in anomaly_rates]
            ax4.bar(range(len(kp_ids)), anomaly_rates, color=colors)
            ax4.set_title('轨迹异常率')
            ax4.set_xlabel('关键点ID')
            ax4.set_ylabel('异常率')
            ax4.set_xticks(range(len(kp_ids)))
            ax4.set_xticklabels([f'KP{kp}' for kp in kp_ids])
        
        # 5. 周期性强度
        ax5 = axes[1, 1]
        if 'periodic_patterns' in trajectory_results:
            kp_ids = list(trajectory_results['periodic_patterns'].keys())
            periodicity = [trajectory_results['periodic_patterns'][kp]['periodicity_strength'] for kp in kp_ids]
            ax5.bar(range(len(kp_ids)), periodicity)
            ax5.set_title('周期性强度')
            ax5.set_xlabel('关键点ID')
            ax5.set_ylabel('周期性强度')
            ax5.set_xticks(range(len(kp_ids)))
            ax5.set_xticklabels([f'KP{kp}' for kp in kp_ids])
        
        # 6. 示例轨迹图
        ax6 = axes[1, 2]
        if keypoints and keypoints[0] in self.keypoint_trajectories:
            trajectory = self.keypoint_trajectories[keypoints[0]]
            x_coords = [point[1] for point in trajectory]
            y_coords = [point[2] for point in trajectory]
            
            # 绘制轨迹线
            ax6.plot(x_coords, y_coords, 'b-', alpha=0.7, linewidth=1)
            # 标记起始点
            ax6.plot(x_coords[0], y_coords[0], 'go', markersize=8, label='起始点')
            ax6.plot(x_coords[-1], y_coords[-1], 'ro', markersize=8, label='结束点')
            
            ax6.set_title(f'关键点{keypoints[0]}轨迹示例')
            ax6.set_xlabel('X坐标 (像素)')
            ax6.set_ylabel('Y坐标 (像素)')
            ax6.legend()
            ax6.grid(True, alpha=0.3)
            ax6.axis('equal')
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, f'trajectory_analysis_{timestamp}.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='关键点关系与轨迹分析工具')
    parser.add_argument('-i', '--input', required=True, help='输入CSV数据文件路径')
    parser.add_argument('-o', '--output', default='./', help='输出目录')
    parser.add_argument('--fps', type=float, default=10.0, help='视频帧率')
    parser.add_argument('--history-window', type=int, default=10, help='历史轨迹分析窗口大小')
    parser.add_argument('--relation-pairs', help='关键点对，格式：0,1;2,3;4,5 （可选）')
    parser.add_argument('--trajectory-keypoints', help='要分析轨迹的关键点ID，格式：0,1,2,3 （可选）')
    parser.add_argument('--no-viz', action='store_true', help='不生成可视化图表')
    
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output, exist_ok=True)
    
    # 解析关键点对
    relation_pairs = None
    if args.relation_pairs:
        try:
            pairs = args.relation_pairs.split(';')
            relation_pairs = []
            for pair_str in pairs:
                kp1, kp2 = map(int, pair_str.split(','))
                relation_pairs.append((kp1, kp2))
        except:
            print("关键点对格式错误，使用默认设置")
    
    # 解析轨迹关键点
    trajectory_keypoints = None
    if args.trajectory_keypoints:
        try:
            trajectory_keypoints = list(map(int, args.trajectory_keypoints.split(',')))
        except:
            print("轨迹关键点格式错误，使用默认设置")
    
    # 初始化分析器
    analyzer = KeypointRelationTrajectoryAnalyzer(fps=args.fps, history_window=args.history_window)
    
    # 加载数据
    if not analyzer.load_data(args.input):
        print("数据加载失败")
        return
    
    # 生成综合分析报告
    report_file = analyzer.generate_comprehensive_analysis(
        args.output, relation_pairs, trajectory_keypoints)
    
    # 生成可视化图表
    if not args.no_viz:
        analyzer.create_visualizations(args.output, relation_pairs, trajectory_keypoints)
    
    print(f"分析完成！报告文件: {report_file}")


if __name__ == "__main__":
    main() 