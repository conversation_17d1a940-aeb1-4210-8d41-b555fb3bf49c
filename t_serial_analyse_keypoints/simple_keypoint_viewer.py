#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版关键点查看器
专注于在1280x800画幅上显示关键点，右侧显示关键点间关系
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.widgets import Slider
import os

class SimpleKeypointViewer:
    """简化版关键点查看器"""
    
    def __init__(self, csv_file: str):
        self.csv_file = csv_file
        self.data = None
        self.frames_data = {}
        self.current_frame = 0
        
        # 画布设置
        self.canvas_width = 1280
        self.canvas_height = 800
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['Noto Sans CJK JP', 'AR PL UMing CN', 'AR PL UKai CN']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 关键点对定义（面部重要特征）
        self.key_pairs = [
            (0, 4, "左眼"),
            (5, 9, "右眼"), 
            (10, 11, "鼻部"),
            (16, 17, "嘴角"),
            (0, 5, "眼间距"),
            (12, 14, "眉毛"),
        ]
        
        print("简化版关键点查看器初始化完成")
    
    def load_data(self):
        """加载数据"""
        try:
            print(f"加载数据: {self.csv_file}")
            self.data = pd.read_csv(self.csv_file)
            
            # 按帧组织数据
            for frame_id in self.data['frame_id'].unique():
                frame_data = self.data[self.data['frame_id'] == frame_id].copy()
                frame_data = frame_data.sort_values('keypoint_id')
                self.frames_data[frame_id] = frame_data
            
            print(f"加载完成: {len(self.frames_data)} 帧, {len(self.data['keypoint_id'].unique())} 个关键点")
            return True
            
        except Exception as e:
            print(f"加载失败: {e}")
            return False
    
    def show(self):
        """显示可视化界面"""
        if self.data is None:
            print("请先加载数据")
            return
        
        # 创建图形
        self.fig, (self.ax_main, self.ax_info) = plt.subplots(1, 2, figsize=(16, 8))
        
        # 设置主显示区域
        self.ax_main.set_xlim(0, self.canvas_width)
        self.ax_main.set_ylim(0, self.canvas_height)
        self.ax_main.set_aspect('equal')
        self.ax_main.invert_yaxis()
        self.ax_main.set_title('关键点可视化 (1280x800)')
        self.ax_main.grid(True, alpha=0.3)
        
        # 设置信息区域
        self.ax_info.axis('off')
        self.ax_info.set_title('关键点关系信息')
        
        # 创建帧控制滑块
        plt.subplots_adjust(bottom=0.15)
        ax_slider = plt.axes([0.1, 0.05, 0.8, 0.03])
        
        frame_ids = sorted(self.frames_data.keys())
        self.slider = Slider(ax_slider, '帧数', frame_ids[0], frame_ids[-1], 
                           valinit=frame_ids[0], valfmt='%d')
        self.slider.on_changed(self.update_frame)
        
        # 初始显示
        self.current_frame = frame_ids[0]
        self.update_display()
        
        plt.tight_layout()
        plt.show()
    
    def update_frame(self, val):
        """更新帧"""
        self.current_frame = int(self.slider.val)
        self.update_display()
    
    def update_display(self):
        """更新显示"""
        if self.current_frame not in self.frames_data:
            return
        
        frame_data = self.frames_data[self.current_frame]
        
        # 清除之前的内容
        self.ax_main.clear()
        self.ax_info.clear()
        
        # 重新设置主区域
        self.ax_main.set_xlim(0, self.canvas_width)
        self.ax_main.set_ylim(0, self.canvas_height)
        self.ax_main.set_aspect('equal')
        self.ax_main.invert_yaxis()
        self.ax_main.set_title(f'关键点可视化 - 帧 {self.current_frame}')
        self.ax_main.grid(True, alpha=0.3)
        
        # 绘制关键点
        keypoint_pos = {}
        colors = plt.cm.Set3(np.linspace(0, 1, 12))
        
        for _, row in frame_data.iterrows():
            kp_id = int(row['keypoint_id'])
            x, y = row['x'], row['y']
            confidence = row['confidence']
            
            keypoint_pos[kp_id] = (x, y)
            
            # 绘制关键点
            size = 30 + confidence * 70
            alpha = 0.5 + confidence * 0.5
            color = colors[kp_id % len(colors)]
            
            self.ax_main.scatter(x, y, s=size, c=[color], alpha=alpha, 
                               edgecolors='black', linewidth=1)
            self.ax_main.text(x+10, y-10, str(kp_id), fontsize=8, weight='bold')
        
        # 绘制连接线和计算关系
        info_text = f"帧 {self.current_frame} 关键点关系:\n\n"
        
        line_colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown']
        
        for i, (kp1, kp2, name) in enumerate(self.key_pairs):
            if kp1 in keypoint_pos and kp2 in keypoint_pos:
                x1, y1 = keypoint_pos[kp1]
                x2, y2 = keypoint_pos[kp2]
                
                # 计算距离和角度
                distance = np.sqrt((x2-x1)**2 + (y2-y1)**2)
                angle = np.degrees(np.arctan2(y2-y1, x2-x1))
                
                # 绘制连接线
                color = line_colors[i % len(line_colors)]
                self.ax_main.plot([x1, x2], [y1, y2], color=color, 
                                linewidth=2, alpha=0.7)
                
                # 标注距离
                mid_x, mid_y = (x1+x2)/2, (y1+y2)/2
                self.ax_main.text(mid_x, mid_y, f'{distance:.1f}', 
                                fontsize=8, ha='center', va='center',
                                bbox=dict(boxstyle='round,pad=0.2', 
                                        facecolor='white', alpha=0.8))
                
                # 添加到信息文本
                info_text += f"{name} (KP{kp1}-KP{kp2}):\n"
                info_text += f"  距离: {distance:.1f} 像素\n"
                info_text += f"  角度: {angle:.1f}°\n\n"
        
        # 添加统计信息
        confidences = frame_data['confidence'].values
        velocities = frame_data['velocity'].values if 'velocity' in frame_data.columns else None
        
        info_text += f"统计信息:\n"
        info_text += f"关键点数: {len(frame_data)}\n"
        info_text += f"平均置信度: {np.mean(confidences):.3f}\n"
        if velocities is not None:
            info_text += f"平均速度: {np.mean(velocities):.2f}\n"
        
        # 显示信息
        self.ax_info.text(0.05, 0.95, info_text, transform=self.ax_info.transAxes,
                         fontsize=10, verticalalignment='top',
                         bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
        
        # 刷新显示
        self.fig.canvas.draw()


def main():
    """主函数"""
    # 默认使用您提供的数据文件
    csv_file = "data/face_keypoints_temporal_stability.csv"
    
    if not os.path.exists(csv_file):
        print(f"数据文件不存在: {csv_file}")
        return
    
    # 创建查看器
    viewer = SimpleKeypointViewer(csv_file)
    
    # 加载数据
    if not viewer.load_data():
        return
    
    print("\n启动可视化界面...")
    print("使用说明:")
    print("- 拖动底部滑块切换帧")
    print("- 左侧显示1280x800画幅上的关键点")
    print("- 右侧显示关键点间关系信息")
    print("- 关键点大小反映置信度")
    print("- 连接线显示重要关键点对的关系")
    
    # 显示界面
    viewer.show()


if __name__ == "__main__":
    main() 