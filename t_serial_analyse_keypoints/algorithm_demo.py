#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关键点分析核心算法演示
展示双维度分析的具体实现过程
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
from scipy.spatial.distance import pdist, squareform
from scipy.spatial import ConvexHull
import pandas as pd

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Noto Sans CJK JP', 'AR PL UMing CN']
plt.rcParams['axes.unicode_minus'] = False

class AlgorithmDemo:
    """算法演示类"""
    
    def __init__(self):
        """初始化演示数据"""
        print("🔬 关键点分析算法原理演示")
        print("=" * 50)
        
        # 生成示例数据：模拟面部关键点运动
        self.fps = 30
        self.duration = 5  # 5秒
        self.num_frames = self.fps * self.duration
        self.num_keypoints = 5
        
        # 生成带有不同运动模式的关键点序列
        self.generate_sample_data()
    
    def generate_sample_data(self):
        """生成示例关键点数据"""
        print("\n📊 生成示例数据...")
        
        t = np.linspace(0, self.duration, self.num_frames)
        self.time_sequence = t
        
        # 基础位置（面部中心）
        center_x, center_y = 400, 300
        
        # 生成5个关键点的运动轨迹
        self.keypoints_sequence = []
        
        for frame_idx in range(self.num_frames):
            frame_keypoints = []
            
            for kp_idx in range(self.num_keypoints):
                # 不同关键点有不同的运动模式
                if kp_idx == 0:  # 中心点 - 呼吸运动
                    x = center_x + 5 * np.sin(2 * np.pi * 0.3 * t[frame_idx])
                    y = center_y + 3 * np.cos(2 * np.pi * 0.3 * t[frame_idx])
                    
                elif kp_idx == 1:  # 左眼 - 眨眼运动
                    x = center_x - 50 + 2 * np.sin(2 * np.pi * 1.5 * t[frame_idx])
                    y = center_y - 30 + 8 * np.sin(2 * np.pi * 1.5 * t[frame_idx])
                    
                elif kp_idx == 2:  # 右眼 - 眨眼运动（稍有延迟）
                    x = center_x + 50 + 2 * np.sin(2 * np.pi * 1.5 * t[frame_idx] - 0.1)
                    y = center_y - 30 + 8 * np.sin(2 * np.pi * 1.5 * t[frame_idx] - 0.1)
                    
                elif kp_idx == 3:  # 嘴角左 - 说话运动
                    x = center_x - 30 + 15 * np.sin(2 * np.pi * 3 * t[frame_idx])
                    y = center_y + 50 + 5 * np.cos(2 * np.pi * 3 * t[frame_idx])
                    
                else:  # 嘴角右 - 说话运动
                    x = center_x + 30 + 15 * np.sin(2 * np.pi * 3 * t[frame_idx])
                    y = center_y + 50 + 5 * np.cos(2 * np.pi * 3 * t[frame_idx])
                
                # 添加一些随机噪声
                x += np.random.normal(0, 0.5)
                y += np.random.normal(0, 0.5)
                
                frame_keypoints.append([x, y])
            
            self.keypoints_sequence.append(np.array(frame_keypoints))
        
        print(f"✅ 生成了 {self.num_frames} 帧，{self.num_keypoints} 个关键点的数据")
    
    def demo_velocity_calculation(self):
        """演示速度计算算法"""
        print("\n🏃 【算法演示1】速度计算 (一阶差分)")
        print("-" * 30)
        
        # 选择第0个关键点进行演示
        keypoint_id = 0
        positions = np.array([frame[keypoint_id] for frame in self.keypoints_sequence])
        
        print("数学公式: v_t = √[(x_{t+1} - x_t)² + (y_{t+1} - y_t)²]")
        
        # 计算速度
        velocities = []
        for i in range(len(positions) - 1):
            dx = positions[i+1][0] - positions[i][0]
            dy = positions[i+1][1] - positions[i][1]
            velocity = np.sqrt(dx**2 + dy**2)
            velocities.append(velocity)
            
            if i < 5:  # 显示前5个计算步骤
                print(f"第{i+1}帧: dx={dx:.2f}, dy={dy:.2f}, v={velocity:.2f} 像素/帧")
        
        # 统计信息
        mean_vel = np.mean(velocities)
        max_vel = np.max(velocities)
        std_vel = np.std(velocities)
        
        print(f"\n📊 统计结果:")
        print(f"   平均速度: {mean_vel:.2f} 像素/帧")
        print(f"   最大速度: {max_vel:.2f} 像素/帧")
        print(f"   速度标准差: {std_vel:.2f}")
        
        return velocities
    
    def demo_frequency_analysis(self):
        """演示频域分析算法"""
        print("\n🌊 【算法演示2】频域分析 (FFT)")
        print("-" * 30)
        
        # 选择第0个关键点的X坐标进行演示
        keypoint_id = 0
        x_sequence = np.array([frame[keypoint_id][0] for frame in self.keypoints_sequence])
        
        print("数学公式: X_k = Σ x_n * e^(-2πikn/N)")
        print("目的: 分析关键点运动的频率成分")
        
        # 执行FFT
        fft_result = np.fft.fft(x_sequence)
        freqs = np.fft.fftfreq(len(x_sequence), 1/self.fps)
        
        # 计算功率谱
        power_spectrum = np.abs(fft_result)**2
        
        # 只取正频率部分
        positive_freqs = freqs[:len(freqs)//2]
        positive_power = power_spectrum[:len(power_spectrum)//2]
        
        # 找到主导频率
        dominant_freq_idx = np.argmax(positive_power[1:]) + 1  # 排除DC分量
        dominant_freq = positive_freqs[dominant_freq_idx]
        
        print(f"\n📊 分析结果:")
        print(f"   主导频率: {dominant_freq:.3f} Hz")
        print(f"   周期: {1/dominant_freq:.2f} 秒" if dominant_freq > 0 else "   周期: 无穷大")
        
        return dominant_freq, positive_power

def main():
    """主演示函数"""
    demo = AlgorithmDemo()
    
    print("\n🎬 开始算法演示...")
    
    # 1. 速度计算演示
    velocities = demo.demo_velocity_calculation()
    
    # 2. 频域分析演示
    dominant_freq, power_spectrum = demo.demo_frequency_analysis()
    
    print("\n🎉 算法演示完成！")

if __name__ == "__main__":
    main() 