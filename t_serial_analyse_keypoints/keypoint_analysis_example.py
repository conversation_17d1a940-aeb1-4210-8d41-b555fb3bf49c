#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关键点双维度分析工具使用示例
"""

import numpy as np
import json
import os
from keypoint_dual_dimension_analyzer import KeypointDualDimensionAnalyzer


def generate_sample_data(num_frames=100, num_keypoints=19, output_file="sample_keypoints.json"):
    """
    生成示例关键点数据
    
    Args:
        num_frames: 帧数
        num_keypoints: 关键点数量
        output_file: 输出文件名
    """
    print(f"正在生成 {num_frames} 帧，{num_keypoints} 个关键点的示例数据...")
    
    # 基础关键点位置（模拟人脸关键点）
    base_keypoints = np.random.rand(num_keypoints, 2) * 200 + 100  # 在[100,300]范围内
    
    sample_data = []
    
    for frame_id in range(num_frames):
        # 添加时间相关的运动
        t = frame_id / 30.0  # 假设30fps
        
        # 模拟自然的面部运动
        # 1. 整体平移（呼吸、车辆震动等）
        global_offset = np.array([
            5 * np.sin(0.5 * t),  # X方向低频运动
            3 * np.cos(0.3 * t)   # Y方向低频运动
        ])
        
        # 2. 局部形变（表情变化）
        local_deformation = np.random.normal(0, 1, (num_keypoints, 2))
        
        # 3. 添加一些噪声
        noise = np.random.normal(0, 0.5, (num_keypoints, 2))
        
        # 4. 模拟偶发的大幅运动（如转头）
        if frame_id in [30, 60]:  # 在第30帧和第60帧添加大幅运动
            large_motion = np.random.normal(0, 10, (num_keypoints, 2))
        else:
            large_motion = np.zeros((num_keypoints, 2))
        
        # 合成最终的关键点位置
        current_keypoints = base_keypoints + global_offset + local_deformation + noise + large_motion
        
        frame_data = {
            "frame_id": frame_id,
            "timestamp": t,
            "keypoints": current_keypoints.tolist()
        }
        
        sample_data.append(frame_data)
    
    # 保存到JSON文件
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(sample_data, f, indent=2, ensure_ascii=False)
    
    print(f"示例数据已保存到: {output_file}")
    return output_file


def run_analysis_example():
    """运行分析示例"""
    print("=== 关键点双维度分析工具示例 ===\n")
    
    # 1. 生成示例数据
    sample_file = generate_sample_data(
        num_frames=100,
        num_keypoints=19,
        output_file="sample_keypoints.json"
    )
    
    # 2. 创建分析器
    analyzer = KeypointDualDimensionAnalyzer(fps=30.0)
    
    # 3. 加载数据
    print(f"\n正在加载数据: {sample_file}")
    if not analyzer.load_data_from_json(sample_file):
        print("数据加载失败")
        return
    
    # 4. 执行分析
    print("\n开始执行双维度分析...")
    
    # 时间序列分析
    print("1. 时间序列分析...")
    temporal_results = analyzer.temporal_analysis()
    
    # 空间分析
    print("2. 空间分析...")
    spatial_results = analyzer.spatial_analysis()
    
    # 综合分析
    print("3. 综合分析...")
    integrated_results = analyzer.integrated_analysis()
    
    # 5. 生成报告
    print("\n生成分析报告...")
    output_dir = "./analysis_output"
    os.makedirs(output_dir, exist_ok=True)
    
    report_file = analyzer.generate_comprehensive_report(output_dir)
    
    # 6. 生成可视化
    print("生成可视化图表...")
    analyzer.create_visualizations(output_dir)
    
    # 7. 显示分析摘要
    print("\n=== 分析摘要 ===")
    
    if temporal_results and 'trajectory_stats' in temporal_results:
        stats = temporal_results['trajectory_stats']
        avg_velocity = np.mean([s['mean_velocity'] for s in stats])
        max_velocity = max([s['max_velocity'] for s in stats])
        print(f"平均运动速度: {avg_velocity:.3f} 像素/帧")
        print(f"最大运动速度: {max_velocity:.3f} 像素/帧")
    
    if spatial_results and 'spatial_stats' in spatial_results:
        stats = spatial_results['spatial_stats']
        avg_spread = np.mean([s['spread'] for s in stats])
        print(f"平均空间散布度: {avg_spread:.3f} 像素")
    
    if integrated_results and 'anomaly_detection' in integrated_results:
        anomalies = integrated_results['anomaly_detection']
        anomaly_count = sum(1 for a in anomalies if a['is_anomaly'])
        print(f"检测到异常帧: {anomaly_count} / {len(anomalies)}")
    
    print(f"\n详细报告请查看: {report_file}")
    print(f"可视化图表保存在: {output_dir}")
    
    # 清理示例文件
    if os.path.exists(sample_file):
        os.remove(sample_file)
        print(f"已清理示例文件: {sample_file}")


def batch_analysis_example():
    """批量分析示例"""
    print("\n=== 批量分析示例 ===")
    
    # 生成多个不同特征的数据集
    scenarios = [
        {"name": "稳定场景", "noise_level": 0.5, "motion_scale": 2},
        {"name": "抖动场景", "noise_level": 2.0, "motion_scale": 2},
        {"name": "大运动场景", "noise_level": 0.5, "motion_scale": 10},
    ]
    
    results_summary = []
    
    for scenario in scenarios:
        print(f"\n分析场景: {scenario['name']}")
        
        # 生成特定场景的数据
        sample_file = f"sample_{scenario['name']}.json"
        generate_scenario_data(
            scenario['noise_level'], 
            scenario['motion_scale'], 
            sample_file
        )
        
        # 分析
        analyzer = KeypointDualDimensionAnalyzer(fps=30.0)
        analyzer.load_data_from_json(sample_file)
        
        temporal_results = analyzer.temporal_analysis()
        integrated_results = analyzer.integrated_analysis()
        
        # 提取关键指标
        if temporal_results and 'trajectory_stats' in temporal_results:
            stats = temporal_results['trajectory_stats']
            avg_velocity = np.mean([s['mean_velocity'] for s in stats])
        else:
            avg_velocity = 0
        
        if integrated_results and 'anomaly_detection' in integrated_results:
            anomalies = integrated_results['anomaly_detection']
            anomaly_rate = sum(1 for a in anomalies if a['is_anomaly']) / len(anomalies) if anomalies else 0
        else:
            anomaly_rate = 0
        
        results_summary.append({
            'scenario': scenario['name'],
            'avg_velocity': avg_velocity,
            'anomaly_rate': anomaly_rate
        })
        
        # 清理临时文件
        if os.path.exists(sample_file):
            os.remove(sample_file)
    
    # 显示批量分析结果
    print("\n=== 批量分析结果对比 ===")
    print(f"{'场景':<15} {'平均速度':<12} {'异常率':<10}")
    print("-" * 40)
    for result in results_summary:
        print(f"{result['scenario']:<15} {result['avg_velocity']:<12.3f} {result['anomaly_rate']:<10.1%}")


def generate_scenario_data(noise_level, motion_scale, output_file, num_frames=50):
    """生成特定场景的数据"""
    base_keypoints = np.random.rand(19, 2) * 200 + 100
    sample_data = []
    
    for frame_id in range(num_frames):
        t = frame_id / 30.0
        
        # 基础运动
        global_offset = np.array([
            motion_scale * np.sin(0.5 * t),
            motion_scale * np.cos(0.3 * t)
        ])
        
        # 噪声
        noise = np.random.normal(0, noise_level, (19, 2))
        
        current_keypoints = base_keypoints + global_offset + noise
        
        frame_data = {
            "frame_id": frame_id,
            "timestamp": t,
            "keypoints": current_keypoints.tolist()
        }
        sample_data.append(frame_data)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(sample_data, f, indent=2)


if __name__ == "__main__":
    # 运行基本示例
    run_analysis_example()
    
    # 运行批量分析示例
    batch_analysis_example()
    
    print("\n=== 示例运行完成 ===")
    print("您可以使用以下命令行方式运行分析:")
    print("python keypoint_dual_dimension_analyzer.py -i your_data.json -o ./output --fps 30") 