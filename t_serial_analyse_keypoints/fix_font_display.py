#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字体显示问题诊断和修复工具
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import os
import subprocess
import sys

def check_available_fonts():
    """检查系统中可用的中文字体"""
    print("=== 系统字体检查 ===")
    
    # 获取所有可用字体
    font_list = [f.name for f in fm.fontManager.ttflist]
    
    # 常见的中文字体
    chinese_fonts = [
        'Noto Sans CJK SC', 'Noto Sans CJK TC', 'Noto Sans CJK JP',
        'Source Han Sans SC', 'Source Han Sans TC', 'Source Han Sans JP', 
        'AR PL UMing CN', 'AR PL UKai CN', 'WenQuanYi Micro Hei',
        'Microsoft YaHei', 'SimHei', 'SimSun', 'FangSong', 'KaiTi'
    ]
    
    available_chinese_fonts = []
    for font in chinese_fonts:
        if font in font_list:
            available_chinese_fonts.append(font)
            print(f"✓ 找到字体: {font}")
    
    if not available_chinese_fonts:
        print("✗ 未找到中文字体")
        return None
    
    return available_chinese_fonts

def install_fonts_ubuntu():
    """在Ubuntu系统上安装中文字体"""
    print("\n=== Ubuntu字体安装 ===")
    
    try:
        # 安装常用中文字体包
        fonts_packages = [
            'fonts-noto-cjk',          # Noto字体
            'fonts-arphic-uming',      # AR PL UMing字体
            'fonts-wqy-microhei',      # 文泉驿微米黑
            'fonts-wqy-zenhei'         # 文泉驿正黑
        ]
        
        for package in fonts_packages:
            print(f"尝试安装: {package}")
            result = subprocess.run(['sudo', 'apt', 'install', '-y', package], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✓ 成功安装: {package}")
            else:
                print(f"✗ 安装失败: {package}")
        
        # 更新字体缓存
        print("更新字体缓存...")
        subprocess.run(['fc-cache', '-fv'], capture_output=True)
        
        return True
    except Exception as e:
        print(f"安装字体时出错: {e}")
        return False

def test_font_display():
    """测试字体显示效果"""
    print("\n=== 字体显示测试 ===")
    
    # 测试文本
    test_text = "关键点分析工具测试 - 时间序列"
    
    # 获取可用的中文字体
    available_fonts = check_available_fonts()
    
    if not available_fonts:
        print("没有可用的中文字体进行测试")
        return
    
    # 为每个字体创建测试图
    for i, font_name in enumerate(available_fonts[:3]):  # 最多测试3个字体
        try:
            plt.figure(figsize=(8, 4))
            plt.rcParams['font.sans-serif'] = [font_name]
            plt.text(0.5, 0.5, test_text, fontsize=16, ha='center', va='center')
            plt.title(f'字体测试: {font_name}', fontsize=14)
            plt.axis('off')
            
            output_file = f'font_test_{font_name.replace(" ", "_")}.png'
            plt.savefig(output_file, dpi=150, bbox_inches='tight')
            plt.close()
            
            print(f"✓ 生成测试图片: {output_file}")
            
        except Exception as e:
            print(f"✗ 字体 {font_name} 测试失败: {e}")

def fix_matplotlib_font():
    """修复matplotlib字体配置"""
    print("\n=== 修复matplotlib字体配置 ===")
    
    # 清除matplotlib字体缓存
    try:
        cache_dir = fm.get_cachedir()
        cache_files = ['fontlist-v330.json', 'fontlist-v310.json', 'fontList.cache']
        
        for cache_file in cache_files:
            cache_path = os.path.join(cache_dir, cache_file)
            if os.path.exists(cache_path):
                os.remove(cache_path)
                print(f"✓ 删除字体缓存: {cache_file}")
        
        # 重建字体缓存
        fm._rebuild()
        print("✓ 重建matplotlib字体缓存")
        
    except Exception as e:
        print(f"字体缓存处理失败: {e}")

def create_font_config():
    """创建字体配置文件"""
    print("\n=== 创建字体配置 ===")
    
    available_fonts = check_available_fonts()
    if not available_fonts:
        return
    
    config_content = f'''# matplotlib中文字体配置
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = {available_fonts}
plt.rcParams['axes.unicode_minus'] = False

print("字体配置已加载，使用字体: {available_fonts[0]}")
'''
    
    with open('font_config.py', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print("✓ 创建字体配置文件: font_config.py")
    print("使用方法: 在脚本开头添加 import font_config")

def main():
    """主函数"""
    print("字体显示问题诊断和修复工具")
    print("=" * 50)
    
    # 检查当前字体情况
    available_fonts = check_available_fonts()
    
    if not available_fonts:
        print("\n检测到没有中文字体，尝试安装...")
        
        # 检测操作系统
        if os.path.exists('/etc/debian_version'):
            # Ubuntu/Debian系统
            install_fonts_ubuntu()
            # 重新检查
            available_fonts = check_available_fonts()
        else:
            print("请手动安装中文字体包")
            print("Ubuntu/Debian: sudo apt install fonts-noto-cjk fonts-wqy-microhei")
            print("CentOS/RHEL: sudo yum install google-noto-cjk-fonts wqy-microhei-fonts")
    
    if available_fonts:
        # 修复matplotlib配置
        fix_matplotlib_font()
        
        # 测试字体显示
        test_font_display()
        
        # 创建配置文件
        create_font_config()
        
        print(f"\n✓ 修复完成！推荐使用字体: {available_fonts[0]}")
        print("请重新运行分析工具测试字体显示效果")
    else:
        print("\n✗ 未能解决字体问题，请手动安装中文字体")

if __name__ == "__main__":
    main() 