# 关键点关系与轨迹分析工具

专门分析关键点数据的两个核心维度：
1. **连续帧间不同关键点的相对关系变化**
2. **单一关键点与其历史轨迹的对比分析**

## 🎯 核心功能

### 1. 连续帧间关键点关系分析
- **距离关系变化**：分析关键点对在连续帧间的距离变化
- **角度关系变化**：计算关键点对的方向角变化
- **相对运动分析**：分析关键点间的相对运动模式
- **形状稳定性评估**：评估由关键点构成的几何形状的稳定性

### 2. 单一关键点历史轨迹分析
- **轨迹平滑性**：基于急动度(jerk)的平滑性评估
- **运动预测性**：基于历史轨迹的运动预测准确度
- **周期性检测**：使用FFT检测关键点运动的周期性模式
- **异常检测**：识别不符合历史模式的异常运动

## 📊 分析指标

### 关键点关系指标
- 距离变化幅度和变化率
- 角度变化分布
- 相对运动强度
- 形状相似度（归一化距离矩阵相似性）

### 轨迹质量指标
- 平滑度分数（1/(1+jerk_magnitude)）
- 预测准确度（1/(1+prediction_error)）
- 周期性强度（主导频率功率占比）
- 异常率（超过3σ的数据点比例）

## 🚀 快速开始

### 基本使用
```bash
# 使用默认参数分析
python keypoint_relation_trajectory_analyzer.py -i data/face_keypoints_temporal_stability.csv

# 自定义参数
python keypoint_relation_trajectory_analyzer.py \
    -i data/face_keypoints_temporal_stability.csv \
    -o ./output/ \
    --fps 10.0 \
    --history-window 10 \
    --relation-pairs "0,4;5,9;10,11" \
    --trajectory-keypoints "0,4,5,9,10,11"
```

### 编程接口使用
```python
from keypoint_relation_trajectory_analyzer import KeypointRelationTrajectoryAnalyzer

# 初始化分析器
analyzer = KeypointRelationTrajectoryAnalyzer(fps=10.0, history_window=10)

# 加载数据
analyzer.load_data("data/face_keypoints_temporal_stability.csv")

# 分析关键点关系
relation_results = analyzer.analyze_inter_keypoint_relations([(0,4), (5,9)])

# 分析关键点轨迹
trajectory_results = analyzer.analyze_keypoint_trajectories([0, 4, 5, 9])

# 生成报告和可视化
analyzer.generate_comprehensive_analysis("./output/")
analyzer.create_visualizations("./output/")
```

### 使用示例脚本
```bash
python run_keypoint_relation_analysis.py
```

## 📁 数据格式

支持CSV格式的关键点数据，包含以下列：
- `frame_id`: 帧ID
- `keypoint_id`: 关键点ID  
- `x`, `y`: 关键点坐标
- `confidence`: 置信度
- `velocity`: 速度（可选，如果没有会自动计算）
- `acceleration`: 加速度（可选，如果没有会自动计算）

示例数据格式：
```csv
frame_id,keypoint_id,x,y,confidence,std_x,std_y,velocity,acceleration,instability_score,is_unstable
0,0,476.758,477.995,0.970688,0,0,0,0,0,0
0,1,508.203,473.893,0.997527,0,0,0,0,0,0
...
```

## 📈 输出结果

### 1. 分析报告 (`keypoint_relation_trajectory_report_*.txt`)
包含详细的数值分析结果和结论建议

### 2. 可视化图表
- `keypoint_relations_*.png`: 关键点关系分析图表
  - 距离变化时序图
  - 距离变化率分布
  - 形状稳定性曲线
  - 相对运动幅度
  
- `trajectory_analysis_*.png`: 轨迹分析图表
  - 轨迹平滑性对比
  - 运动预测准确度
  - 速度分布直方图
  - 异常率统计
  - 周期性强度
  - 示例轨迹图

## 🎛️ 预定义关键点对

工具预定义了面部重要区域的关键点对：
- `(0, 4)`: 左眼外角到内角
- `(5, 9)`: 右眼内角到外角  
- `(0, 5)`: 左右眼外角
- `(4, 9)`: 左右眼内角
- `(12, 14)`: 左右眉毛
- `(13, 15)`: 左右眉毛中部
- `(10, 11)`: 鼻部中心线
- `(16, 17)`: 嘴角对
- `(18, 22)`: 面部左右轮廓

## 📐 算法原理

### 轨迹平滑性（急动度分析）
```
速度: v = dx/dt
加速度: a = dv/dt  
急动度: j = da/dt
平滑度 = 1/(1 + |j|)
```

### 运动预测（线性趋势外推）
```
predicted_pos = current_pos + (current_pos - prev_pos)
accuracy = 1/(1 + prediction_error)
```

### 周期性检测（FFT频域分析）
```
FFT(velocity_signal) → 主导频率
周期性强度 = 主导频率功率 / 总功率
```

### 形状稳定性（归一化距离相似性）
```
distance_matrix_normalized = distances / mean(distances)
similarity = 1 - mean(|curr_matrix - prev_matrix|)
```

## 🔧 参数说明

- `fps`: 视频帧率，影响时间计算和频域分析
- `history_window`: 历史轨迹分析窗口大小，用于运动预测
- `relation_pairs`: 要分析的关键点对列表
- `trajectory_keypoints`: 要分析轨迹的关键点ID列表

## 🎯 DMS应用场景

### 驾驶员监控系统(DMS)应用
1. **疲劳检测**：分析眼部关键点的运动模式，检测眨眼频率变化
2. **注意力监控**：通过面部朝向的关键点关系变化判断视线方向
3. **异常行为识别**：检测不正常的面部运动或表情变化
4. **系统稳定性评估**：评估关键点检测算法的稳定性和可靠性

### 分析结论解读
- **形状稳定性 > 0.95**：关键点检测非常稳定
- **平滑度 > 0.8**：轨迹质量优秀，滤波效果好
- **异常率 < 5%**：数据质量良好
- **周期性检测**：可识别眨眼、说话等生理活动

## 🛠️ 依赖库

```bash
numpy
pandas  
matplotlib
scipy
```

## 📝 许可证

本工具作为DMS系统开发的辅助分析工具，专注于关键点数据的深度分析。 