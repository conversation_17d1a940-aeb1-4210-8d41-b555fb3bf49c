#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关键点可视化工具
在1280x800画幅上显示关键点，右侧显示当前帧关键点间的关系
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.widgets import Slider, Button
import os
from typing import Dict, List, Tuple

class KeypointVisualizer:
    """关键点可视化器"""
    
    def __init__(self, csv_file: str):
        """
        初始化可视化器
        
        Args:
            csv_file: CSV数据文件路径
        """
        self.csv_file = csv_file
        self.data = None
        self.frames_data = {}
        self.current_frame = 0
        self.num_frames = 0
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['Noto Sans CJK JP', 'AR PL UMing CN', 'AR PL UKai CN', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 画布设置
        self.canvas_width = 1280
        self.canvas_height = 800
        
        # 预定义重要的关键点对
        self.important_pairs = [
            (0, 4),   # 左眼
            (5, 9),   # 右眼
            (0, 5),   # 左右眼外角
            (4, 9),   # 左右眼内角
            (12, 14), # 左右眉毛
            (13, 15), # 左右眉毛中部
            (10, 11), # 鼻部
            (16, 17), # 嘴角
        ]
        
        # 颜色配置
        self.keypoint_colors = plt.cm.tab20(np.linspace(0, 1, 20))
        self.line_colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'cyan']
        
        print("关键点可视化器初始化完成")
    
    def load_data(self) -> bool:
        """加载CSV数据"""
        try:
            print(f"正在加载数据: {self.csv_file}")
            self.data = pd.read_csv(self.csv_file)
            
            print(f"数据加载成功:")
            print(f"  总记录数: {len(self.data)}")
            print(f"  帧数范围: {self.data['frame_id'].min()} - {self.data['frame_id'].max()}")
            print(f"  关键点数: {len(self.data['keypoint_id'].unique())}")
            
            # 按帧组织数据
            self._organize_data_by_frame()
            
            self.num_frames = len(self.frames_data)
            print(f"  组织完成，共 {self.num_frames} 帧")
            
            return True
            
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    def _organize_data_by_frame(self):
        """按帧组织数据"""
        for frame_id in self.data['frame_id'].unique():
            frame_data = self.data[self.data['frame_id'] == frame_id].copy()
            frame_data = frame_data.sort_values('keypoint_id')
            self.frames_data[frame_id] = frame_data
    
    def create_interactive_plot(self):
        """创建交互式可视化界面"""
        if self.data is None:
            print("请先加载数据")
            return
        
        # 创建图形和子图
        self.fig = plt.figure(figsize=(16, 10))
        
        # 左侧：关键点显示区域 (主要区域)
        self.ax_main = plt.subplot2grid((4, 3), (0, 0), colspan=2, rowspan=3)
        self.ax_main.set_xlim(0, self.canvas_width)
        self.ax_main.set_ylim(0, self.canvas_height)
        self.ax_main.set_aspect('equal')
        self.ax_main.set_title('关键点可视化 (1280x800)', fontsize=14)
        self.ax_main.invert_yaxis()  # 图像坐标系Y轴向下
        
        # 右上：关键点信息
        self.ax_info = plt.subplot2grid((4, 3), (0, 2))
        self.ax_info.axis('off')
        self.ax_info.set_title('关键点信息', fontsize=12)
        
        # 右中：关键点间距离
        self.ax_distance = plt.subplot2grid((4, 3), (1, 2))
        self.ax_distance.set_title('关键点间距离', fontsize=12)
        
        # 右下：关键点连接关系
        self.ax_relations = plt.subplot2grid((4, 3), (2, 2))
        self.ax_relations.axis('off')
        self.ax_relations.set_title('连接关系', fontsize=12)
        
        # 底部：帧控制滑块
        self.ax_slider = plt.subplot2grid((4, 3), (3, 0), colspan=3)
        
        # 创建帧数滑块
        frame_ids = sorted(self.frames_data.keys())
        self.frame_slider = Slider(
            self.ax_slider, '帧数', 
            frame_ids[0], frame_ids[-1], 
            valinit=frame_ids[0], 
            valfmt='%d'
        )
        self.frame_slider.on_changed(self.update_frame)
        
        # 初始化显示
        self.current_frame = frame_ids[0]
        self.update_display()
        
        plt.tight_layout()
        plt.show()
    
    def update_frame(self, val):
        """更新当前帧"""
        self.current_frame = int(self.frame_slider.val)
        self.update_display()
    
    def update_display(self):
        """更新显示内容"""
        if self.current_frame not in self.frames_data:
            return
        
        frame_data = self.frames_data[self.current_frame]
        
        # 清除之前的显示
        self.ax_main.clear()
        self.ax_info.clear()
        self.ax_distance.clear()
        self.ax_relations.clear()
        
        # 重新设置主显示区域
        self.ax_main.set_xlim(0, self.canvas_width)
        self.ax_main.set_ylim(0, self.canvas_height)
        self.ax_main.set_aspect('equal')
        self.ax_main.set_title(f'关键点可视化 - 帧 {self.current_frame}', fontsize=14)
        self.ax_main.invert_yaxis()
        self.ax_main.grid(True, alpha=0.3)
        
        # 显示关键点
        self._plot_keypoints(frame_data)
        
        # 显示关键点连接关系
        self._plot_keypoint_connections(frame_data)
        
        # 更新信息面板
        self._update_info_panel(frame_data)
        
        # 更新距离图表
        self._update_distance_chart(frame_data)
        
        # 更新关系信息
        self._update_relations_info(frame_data)
        
        # 刷新显示
        self.fig.canvas.draw()
    
    def _plot_keypoints(self, frame_data: pd.DataFrame):
        """绘制关键点"""
        for _, row in frame_data.iterrows():
            kp_id = int(row['keypoint_id'])
            x, y = row['x'], row['y']
            confidence = row['confidence']
            
            # 根据置信度设置点的大小和透明度
            size = 50 + confidence * 100  # 基础大小50，最大150
            alpha = 0.3 + confidence * 0.7  # 透明度0.3-1.0
            
            # 选择颜色
            color_idx = kp_id % len(self.keypoint_colors)
            color = self.keypoint_colors[color_idx]
            
            # 绘制关键点
            self.ax_main.scatter(x, y, s=size, c=[color], alpha=alpha, 
                               edgecolors='black', linewidth=1, zorder=5)
            
            # 添加关键点ID标签
            self.ax_main.annotate(str(kp_id), (x, y), xytext=(5, 5), 
                                textcoords='offset points', fontsize=8, 
                                color='black', weight='bold')
    
    def _plot_keypoint_connections(self, frame_data: pd.DataFrame):
        """绘制关键点连接关系"""
        # 创建关键点位置字典
        keypoint_positions = {}
        for _, row in frame_data.iterrows():
            kp_id = int(row['keypoint_id'])
            keypoint_positions[kp_id] = (row['x'], row['y'])
        
        # 绘制预定义的重要连接
        for i, (kp1, kp2) in enumerate(self.important_pairs):
            if kp1 in keypoint_positions and kp2 in keypoint_positions:
                x1, y1 = keypoint_positions[kp1]
                x2, y2 = keypoint_positions[kp2]
                
                # 计算距离
                distance = np.sqrt((x2 - x1)**2 + (y2 - y1)**2)
                
                # 根据距离设置线条粗细
                linewidth = max(1, min(5, distance / 50))
                
                # 选择颜色
                color = self.line_colors[i % len(self.line_colors)]
                
                # 绘制连接线
                self.ax_main.plot([x1, x2], [y1, y2], color=color, 
                                linewidth=linewidth, alpha=0.6, zorder=3)
                
                # 在线中点显示距离
                mid_x, mid_y = (x1 + x2) / 2, (y1 + y2) / 2
                self.ax_main.annotate(f'{distance:.1f}', (mid_x, mid_y), 
                                    fontsize=8, ha='center', va='center',
                                    bbox=dict(boxstyle='round,pad=0.2', 
                                            facecolor='white', alpha=0.7))
    
    def _update_info_panel(self, frame_data: pd.DataFrame):
        """更新信息面板"""
        self.ax_info.axis('off')
        
        # 基本信息
        info_text = f"帧 ID: {self.current_frame}\n"
        info_text += f"关键点数: {len(frame_data)}\n\n"
        
        # 置信度统计
        confidences = frame_data['confidence'].values
        info_text += f"置信度统计:\n"
        info_text += f"  平均: {np.mean(confidences):.3f}\n"
        info_text += f"  最小: {np.min(confidences):.3f}\n"
        info_text += f"  最大: {np.max(confidences):.3f}\n\n"
        
        # 速度统计
        if 'velocity' in frame_data.columns:
            velocities = frame_data['velocity'].values
            info_text += f"速度统计:\n"
            info_text += f"  平均: {np.mean(velocities):.2f}\n"
            info_text += f"  最大: {np.max(velocities):.2f}\n"
        
        self.ax_info.text(0.05, 0.95, info_text, transform=self.ax_info.transAxes,
                         fontsize=10, verticalalignment='top',
                         bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
    
    def _update_distance_chart(self, frame_data: pd.DataFrame):
        """更新距离图表"""
        # 计算重要关键点对的距离
        keypoint_positions = {}
        for _, row in frame_data.iterrows():
            kp_id = int(row['keypoint_id'])
            keypoint_positions[kp_id] = (row['x'], row['y'])
        
        distances = []
        pair_names = []
        
        for kp1, kp2 in self.important_pairs:
            if kp1 in keypoint_positions and kp2 in keypoint_positions:
                x1, y1 = keypoint_positions[kp1]
                x2, y2 = keypoint_positions[kp2]
                distance = np.sqrt((x2 - x1)**2 + (y2 - y1)**2)
                distances.append(distance)
                pair_names.append(f'{kp1}-{kp2}')
        
        if distances:
            bars = self.ax_distance.bar(range(len(distances)), distances, 
                                      color=self.line_colors[:len(distances)])
            self.ax_distance.set_xticks(range(len(distances)))
            self.ax_distance.set_xticklabels(pair_names, rotation=45, fontsize=8)
            self.ax_distance.set_ylabel('距离 (像素)', fontsize=10)
            self.ax_distance.set_title('关键点间距离', fontsize=12)
            
            # 在柱状图上显示数值
            for i, (bar, dist) in enumerate(zip(bars, distances)):
                height = bar.get_height()
                self.ax_distance.text(bar.get_x() + bar.get_width()/2., height,
                                    f'{dist:.1f}', ha='center', va='bottom', fontsize=8)
    
    def _update_relations_info(self, frame_data: pd.DataFrame):
        """更新关系信息"""
        self.ax_relations.axis('off')
        
        # 创建关键点位置字典
        keypoint_positions = {}
        for _, row in frame_data.iterrows():
            kp_id = int(row['keypoint_id'])
            keypoint_positions[kp_id] = (row['x'], row['y'])
        
        # 计算关系信息
        relation_text = "连接关系:\n"
        
        for i, (kp1, kp2) in enumerate(self.important_pairs):
            if kp1 in keypoint_positions and kp2 in keypoint_positions:
                x1, y1 = keypoint_positions[kp1]
                x2, y2 = keypoint_positions[kp2]
                
                # 距离
                distance = np.sqrt((x2 - x1)**2 + (y2 - y1)**2)
                
                # 角度
                angle = np.degrees(np.arctan2(y2 - y1, x2 - x1))
                
                # 颜色标识
                color = self.line_colors[i % len(self.line_colors)]
                
                relation_text += f"● KP{kp1}-KP{kp2}:\n"
                relation_text += f"  距离: {distance:.1f}px\n"
                relation_text += f"  角度: {angle:.1f}°\n"
        
        self.ax_relations.text(0.05, 0.95, relation_text, 
                             transform=self.ax_relations.transAxes,
                             fontsize=9, verticalalignment='top',
                             bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8))


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='关键点可视化工具')
    parser.add_argument('-i', '--input', 
                       default='data/face_keypoints_temporal_stability.csv',
                       help='输入CSV文件路径')
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not os.path.exists(args.input):
        print(f"错误: 文件 {args.input} 不存在")
        return
    
    # 创建可视化器
    visualizer = KeypointVisualizer(args.input)
    
    # 加载数据
    if not visualizer.load_data():
        print("数据加载失败")
        return
    
    print("\n开始创建交互式可视化界面...")
    print("使用说明:")
    print("- 拖动底部滑块可以切换帧")
    print("- 关键点大小和透明度反映置信度")
    print("- 连接线显示重要关键点对的关系")
    print("- 右侧面板显示详细信息")
    
    # 创建交互式界面
    visualizer.create_interactive_plot()


if __name__ == "__main__":
    main() 