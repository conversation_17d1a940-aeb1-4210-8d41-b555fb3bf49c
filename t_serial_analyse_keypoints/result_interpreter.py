#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关键点分析结果解读工具
将技术指标转换为易懂的解释
"""

import os
import sys
import glob
from datetime import datetime

def interpret_analysis_results(report_file: str, output_dir: str):
    """
    解读分析结果并生成通俗易懂的解释
    
    Args:
        report_file: 分析报告文件路径
        output_dir: 输出目录（包含图表）
    """
    print("=" * 60)
    print("📊 关键点分析结果详细解读")
    print("=" * 60)
    
    # 读取报告文件
    try:
        with open(report_file, 'r', encoding='utf-8') as f:
            report_content = f.read()
    except FileNotFoundError:
        print(f"❌ 报告文件不存在：{report_file}")
        return
    
    # 解析报告内容
    lines = report_content.split('\n')
    
    # 基本信息解读
    print("\n🔍 **数据基本信息**")
    print("-" * 30)
    
    for line in lines:
        if "数据帧数:" in line:
            frame_count = line.split(":")[1].strip()
            print(f"📹 视频总帧数：{frame_count} 帧")
            print(f"   💡 说明：这是你的视频数据总共有多少帧画面")
            
        elif "关键点数量:" in line:
            keypoint_count = line.split(":")[1].strip()
            print(f"🎯 关键点数量：{keypoint_count} 个")
            print(f"   💡 说明：每一帧画面中检测到的面部关键点数量")
            
        elif "时间范围:" in line:
            time_range = line.split(":")[1].strip()
            duration = float(time_range.split(" - ")[1].replace("s", ""))
            print(f"⏱️ 视频时长：{time_range} ({duration:.1f}秒)")
            print(f"   💡 说明：分析的视频片段从开始到结束的时间")
    
    # 时间序列分析解读
    print("\n📈 **时间序列分析结果**")
    print("-" * 30)
    
    for line in lines:
        if "平均速度:" in line:
            speed = float(line.split(":")[1].strip().split()[0])
            print(f"🏃 关键点平均移动速度：{speed:.2f} 像素/帧")
            if speed < 2:
                print("   ✅ 解读：移动很稳定，人脸基本保持静止")
            elif speed < 5:
                print("   ⚠️ 解读：有轻微移动，可能是正常的头部微动")
            elif speed < 10:
                print("   ⚠️ 解读：移动较明显，可能是头部转动或说话")
            else:
                print("   🔴 解读：移动很剧烈，可能是大幅度头部运动或检测不稳定")
                
        elif "平均加速度:" in line:
            acceleration = float(line.split(":")[1].strip().split()[0])
            print(f"🚀 关键点平均加速度：{acceleration:.2f} 像素/帧²")
            if acceleration < 3:
                print("   ✅ 解读：运动变化平滑，没有突然的加速或减速")
            elif acceleration < 8:
                print("   ⚠️ 解读：运动变化适中，可能有一些快速动作")
            else:
                print("   🔴 解读：运动变化很剧烈，可能有急停急起的动作")
                
        elif "主导频率范围:" in line:
            freq_range = line.split(":")[1].strip()
            freq_value = float(freq_range.split(" - ")[0])
            print(f"📊 主要运动频率：{freq_range} Hz")
            if freq_value < 0.1:
                print("   ✅ 解读：运动频率很低，基本是静态的")
            elif freq_value < 0.5:
                print("   ⚠️ 解读：低频运动，可能是缓慢的头部转动")
            elif freq_value < 2:
                print("   ⚠️ 解读：中频运动，可能是说话或轻微摇头")
            else:
                print("   🔴 解读：高频运动，可能是快速运动或抖动")
    
    # 空间分析解读
    print("\n🎭 **空间分析结果**")
    print("-" * 30)
    
    for line in lines:
        if "平均散布度:" in line:
            spread = float(line.split(":")[1].strip().split()[0])
            print(f"📏 关键点散布度：{spread:.2f} 像素")
            if spread < 20:
                print("   ✅ 解读：关键点分布紧密，面部特征集中")
            elif spread < 50:
                print("   ⚠️ 解读：关键点分布适中，正常的面部大小")
            else:
                print("   🔴 解读：关键点分布很散，可能是大脸或距离很近")
                
        elif "平均凸包面积:" in line:
            area = float(line.split(":")[1].strip().split()[0])
            print(f"📐 面部覆盖面积：{area:.0f} 像素²")
            if area < 10000:
                print("   ✅ 解读：面部区域较小，可能距离较远")
            elif area < 50000:
                print("   ⚠️ 解读：面部区域适中，距离合适")
            else:
                print("   🔴 解读：面部区域很大，可能距离很近")
                
        elif "平均形状一致性:" in line:
            consistency = float(line.split(":")[1].strip())
            print(f"🎯 面部形状一致性：{consistency:.3f}")
            if consistency > 0.95:
                print("   ✅ 解读：面部形状非常稳定，检测质量很好")
            elif consistency > 0.90:
                print("   ⚠️ 解读：面部形状较稳定，检测质量良好")
            elif consistency > 0.80:
                print("   ⚠️ 解读：面部形状有一定变化，可能有表情变化")
            else:
                print("   🔴 解读：面部形状变化较大，可能检测不稳定")
    
    # 异常检测解读
    print("\n🚨 **异常检测结果**")
    print("-" * 30)
    
    for line in lines:
        if "检测到异常帧数:" in line:
            parts = line.split(":")
            if len(parts) > 1:
                anomaly_info = parts[1].strip()
                if "/" in anomaly_info:
                    anomaly_count = int(anomaly_info.split("/")[0].strip())
                    total_frames = int(anomaly_info.split("/")[1].strip())
                    print(f"⚠️ 异常帧数：{anomaly_count} / {total_frames}")
                    
        elif "异常率:" in line:
            anomaly_rate = float(line.split(":")[1].strip().replace("%", ""))
            print(f"📊 异常率：{anomaly_rate:.1f}%")
            if anomaly_rate < 2:
                print("   ✅ 解读：异常很少，数据质量优秀")
            elif anomaly_rate < 5:
                print("   ⚠️ 解读：异常较少，数据质量良好")
            elif anomaly_rate < 10:
                print("   ⚠️ 解读：有一定异常，可能需要检查数据")
            else:
                print("   🔴 解读：异常较多，数据质量需要改善")
    
    # 图表解读
    print("\n📊 **生成的图表说明**")
    print("-" * 30)
    
    # 查找图表文件
    png_files = glob.glob(os.path.join(output_dir, "*.png"))
    
    for png_file in png_files:
        filename = os.path.basename(png_file)
        
        if "temporal_analysis" in filename:
            print(f"📈 {filename}")
            print("   🔍 内容：时间序列分析图")
            print("   📝 说明：")
            print("     - 左上：各关键点的平均移动速度柱状图")
            print("     - 右上：各关键点的平均加速度柱状图")
            print("     - 左下：关键点之间运动相关性热力图（颜色越红相关性越强）")
            print("     - 右下：运动频率分布直方图")
            print()
            
        elif "spatial_analysis" in filename:
            print(f"🎭 {filename}")
            print("   🔍 内容：空间分析图")
            print("   📝 说明：")
            print("     - 左上：关键点散布度随时间变化曲线")
            print("     - 右上：面部凸包面积随时间变化曲线")
            print("     - 左下：面部形状一致性随时间变化曲线")
            print("     - 右下：关键点位置变化量随时间变化曲线")
            print()
            
        elif "integrated_analysis" in filename:
            print(f"🔮 {filename}")
            print("   🔍 内容：综合分析图")
            print("   📝 说明：")
            print("     - 左上：空间移动速度随时间变化曲线")
            print("     - 右上：面部形状变化量随时间变化曲线")
            print("     - 左下：异常检测散点图（红点是异常帧，蓝点是正常帧）")
            print("     - 右下：正常/异常帧比例饼图")
            print()
    
    # 总结建议
    print("\n💡 **实用建议**")
    print("-" * 30)
    
    # 根据分析结果给出针对性建议
    suggestions = []
    
    for line in lines:
        if "检测到高速运动" in line:
            suggestions.append("🔧 由于检测到高速运动，建议：")
            suggestions.append("   - 增加卡尔曼滤波器的过程噪声参数")
            suggestions.append("   - 使用更强的平滑滤波算法")
            suggestions.append("   - 检查是否有外界干扰导致的抖动")
            
        elif "数据质量良好" in line:
            suggestions.append("✅ 数据质量评估：")
            suggestions.append("   - 当前数据质量良好，可以用于后续分析")
            suggestions.append("   - 异常率在可接受范围内")
            suggestions.append("   - 检测算法参数设置合理")
    
    if not suggestions:
        suggestions.append("📋 通用建议：")
        suggestions.append("   - 定期检查关键点检测的稳定性")
        suggestions.append("   - 关注异常帧的出现模式")
        suggestions.append("   - 根据应用场景调整滤波参数")
    
    for suggestion in suggestions:
        print(suggestion)
    
    print("\n" + "=" * 60)
    print("🎉 分析完成！以上是对你的关键点数据的详细解读")
    print("=" * 60)

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法: python result_interpreter.py <报告文件路径> [输出目录]")
        print("示例: python result_interpreter.py ./test_output/keypoint_analysis_report_*.txt ./test_output")
        return
    
    report_file = sys.argv[1]
    output_dir = sys.argv[2] if len(sys.argv) > 2 else os.path.dirname(report_file)
    
    # 如果报告文件使用通配符，找到最新的
    if "*" in report_file:
        matching_files = glob.glob(report_file)
        if matching_files:
            # 按修改时间排序，取最新的
            report_file = max(matching_files, key=os.path.getmtime)
        else:
            print("❌ 未找到匹配的报告文件")
            return
    
    interpret_analysis_results(report_file, output_dir)

if __name__ == "__main__":
    main() 