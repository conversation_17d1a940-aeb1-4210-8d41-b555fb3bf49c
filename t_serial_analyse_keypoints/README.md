# 时间序列关键点分析工具

## 概述

本目录包含用于关键点数据双维度分析的工具集，能够从连续时间序列和每一帧关键点空间分布两个维度进行全面分析。

## 文件说明

- `keypoint_dual_dimension_analyzer.py` - 主要分析工具
- `keypoint_analysis_example.py` - 使用示例和演示
- `requirements.txt` - 依赖包列表

## 快速开始

1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 解决中文字体显示问题（如有）：
```bash
python fix_font_display.py
```

3. 运行示例：
```bash
python keypoint_analysis_example.py
```

4. 分析你的数据：
```bash
python keypoint_dual_dimension_analyzer.py -i your_data.json -o ./output
```

## 主要功能

### 🔍 双维度分析
- ✅ **时间序列维度**：轨迹统计、运动相关性、频域分析
- ✅ **空间关键点维度**：空间分布、一致性评估、质量指标
- ✅ **综合分析**：时空耦合、异常检测、综合评估

### 📊 输出内容
- 详细的分析报告（文本格式）
- 多维度可视化图表
- 异常检测结果
- 数据质量评估和建议

### 📁 支持的数据格式
- JSON格式（推荐）
- CSV格式

## 应用场景

- DMS（驾驶员监控系统）关键点质量评估
- 动作捕捉系统数据分析
- 计算机视觉算法性能评估
- 实时数据质量监控

## 使用示例

```python
from keypoint_dual_dimension_analyzer import KeypointDualDimensionAnalyzer

# 创建分析器
analyzer = KeypointDualDimensionAnalyzer(fps=30.0)

# 加载数据
analyzer.load_data_from_json("keypoints_data.json")

# 执行分析
temporal_results = analyzer.temporal_analysis()
spatial_results = analyzer.spatial_analysis()
integrated_results = analyzer.integrated_analysis()

# 生成报告
analyzer.generate_comprehensive_report("./output")
analyzer.create_visualizations("./output")
```

## 分析指标

### 时间序列指标
- 位置方差、平均速度、平均加速度
- 关键点间运动相关性
- 主导频率分析

### 空间分析指标  
- 散布度、凸包面积
- 形状一致性、均匀性
- 空间质量评估

### 综合分析指标
- 空间速度、异常分数
- 时空耦合度分析

## 常见问题解决

### 🔧 中文字体显示乱码

**问题**: 生成的图片中中文显示为方框或乱码

**解决方案**:
1. 运行字体修复工具：
```bash
python fix_font_display.py
```

2. 手动安装中文字体（Ubuntu/Debian）：
```bash
sudo apt update
sudo apt install fonts-noto-cjk fonts-wqy-microhei
```

3. 重新运行分析工具测试效果

**说明**: 工具会自动使用系统中最佳的中文字体，当前支持：
- Noto Sans CJK JP
- AR PL UMing CN  
- AR PL UKai CN

### 📊 数据格式问题

**问题**: CSV加载失败

**解决方案**: 参考 `DATA_FORMAT_GUIDE.md` 中的格式说明，或运行：
```bash
python keypoint_dual_dimension_analyzer.py -i your_data.csv -o ./test_output
```
查看详细错误信息和智能识别结果。 