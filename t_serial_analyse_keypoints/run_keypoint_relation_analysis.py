#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关键点关系与轨迹分析工具使用示例
展示如何使用KeypointRelationTrajectoryAnalyzer进行分析
"""

from keypoint_relation_trajectory_analyzer import KeypointRelationTrajectoryAnalyzer

def main():
    """主函数"""
    print("关键点关系与轨迹分析工具使用示例")
    print("=" * 50)
    
    # 初始化分析器
    analyzer = KeypointRelationTrajectoryAnalyzer(fps=10.0, history_window=10)
    
    # 加载数据
    data_file = "data/face_keypoints_temporal_stability.csv"
    print(f"加载数据文件: {data_file}")
    
    if not analyzer.load_data(data_file):
        print("数据加载失败")
        return
    
    print("数据加载成功！")
    
    # 定义要分析的关键点对（面部关键特征）
    # 这些对应面部的重要区域：眼部、鼻部、嘴部等
    custom_pairs = [
        (0, 4),   # 左眼区域
        (5, 9),   # 右眼区域  
        (0, 5),   # 左右眼外角
        (4, 9),   # 左右眼内角
        (12, 14), # 左右眉毛
        (10, 11), # 鼻部中心
        (16, 17), # 嘴角
    ]
    
    # 定义要分析历史轨迹的关键点（重要的面部特征点）
    trajectory_points = [0, 4, 5, 9, 10, 11, 16, 17]  # 眼角、鼻尖、嘴角等
    
    print("\n开始分析...")
    print(f"分析的关键点对: {len(custom_pairs)} 对")
    print(f"分析的轨迹关键点: {len(trajectory_points)} 个")
    
    # 1. 执行关键点关系分析
    print("\n1. 分析连续帧间关键点关系...")
    relation_results = analyzer.analyze_inter_keypoint_relations(custom_pairs)
    
    # 打印一些关键结果
    print("   距离变化摘要:")
    for pair_name, changes in relation_results['distance_changes'].items():
        if changes:
            import numpy as np
            distance_changes = [c['distance_change'] for c in changes]
            mean_change = np.mean(np.abs(distance_changes))
            print(f"     {pair_name}: 平均变化 {mean_change:.2f} 像素")
    
    # 形状稳定性
    if 'shape_stability' in relation_results:
        import numpy as np
        stability_scores = [s['shape_similarity'] for s in relation_results['shape_stability'].values()]
        mean_stability = np.mean(stability_scores)
        print(f"   整体形状稳定性: {mean_stability:.3f}")
    
    # 2. 执行关键点轨迹分析
    print("\n2. 分析关键点历史轨迹...")
    trajectory_results = analyzer.analyze_keypoint_trajectories(trajectory_points)
    
    # 打印轨迹分析摘要
    print("   轨迹平滑性摘要:")
    for kp_id, smoothness in trajectory_results['trajectory_smoothness'].items():
        print(f"     关键点{kp_id}: 平滑度 {smoothness['smoothness_score']:.3f}")
    
    print("   预测准确度摘要:")
    for kp_id, prediction in trajectory_results['motion_prediction'].items():
        print(f"     关键点{kp_id}: 准确度 {prediction['prediction_accuracy']:.3f}")
    
    print("   异常检测摘要:")
    total_anomalies = 0
    for kp_id, anomaly in trajectory_results['anomaly_detection'].items():
        anomaly_count = anomaly['total_anomalies']
        anomaly_rate = anomaly['anomaly_rate']
        total_anomalies += anomaly_count
        print(f"     关键点{kp_id}: {anomaly_count}个异常 ({anomaly_rate:.1%})")
    
    # 3. 生成完整报告和可视化
    print("\n3. 生成完整分析报告和可视化...")
    report_file = analyzer.generate_comprehensive_analysis(
        output_dir="./", 
        relation_pairs=custom_pairs, 
        trajectory_keypoints=trajectory_points
    )
    
    analyzer.create_visualizations(
        output_dir="./", 
        relation_pairs=custom_pairs, 
        trajectory_keypoints=trajectory_points
    )
    
    print(f"\n分析完成！")
    print(f"报告文件: {report_file}")
    print(f"可视化图表已生成在当前目录")
    
    # 4. 分析结论
    print("\n" + "=" * 50)
    print("分析结论:")
    
    # 关键点关系稳定性评估
    if mean_stability > 0.95:
        print("✓ 关键点间关系非常稳定，面部形状保持优秀")
    elif mean_stability > 0.9:
        print("✓ 关键点间关系很稳定，面部形状保持良好")
    elif mean_stability > 0.8:
        print("⚠ 关键点间关系较稳定，有轻微形变")
    else:
        print("⚠ 关键点间关系不够稳定，建议检查检测算法")
    
    # 轨迹质量评估
    import numpy as np
    smoothness_scores = [s['smoothness_score'] for s in trajectory_results['trajectory_smoothness'].values()]
    mean_smoothness = np.mean(smoothness_scores)
    
    if mean_smoothness > 0.8:
        print("✓ 关键点轨迹很平滑，运动连续性优秀")
    elif mean_smoothness > 0.6:
        print("✓ 关键点轨迹较平滑，运动连续性良好")
    elif mean_smoothness > 0.4:
        print("⚠ 关键点轨迹平滑性一般，有一定抖动")
    else:
        print("⚠ 关键点轨迹不够平滑，建议增加滤波处理")
    
    # 异常率评估
    total_frames = len(analyzer.frames_data)
    total_anomaly_rate = total_anomalies / (total_frames * len(trajectory_points))
    
    if total_anomaly_rate < 0.05:
        print("✓ 关键点运动正常，数据质量优秀")
    elif total_anomaly_rate < 0.1:
        print("✓ 检测到少量异常运动，整体质量良好")
    elif total_anomaly_rate < 0.15:
        print("⚠ 检测到一定数量异常运动，数据质量中等")
    else:
        print("⚠ 检测到较多异常运动，建议检查数据质量")
    
    print("\n使用建议:")
    if mean_stability < 0.9:
        print("- 考虑调整关键点检测算法参数")
    if mean_smoothness < 0.6:
        print("- 建议增加卡尔曼滤波或其他平滑滤波")
    if total_anomaly_rate > 0.1:
        print("- 检查输入视频质量和光照条件")
        print("- 考虑增加数据预处理步骤")


if __name__ == "__main__":
    main() 