#!/usr/bin/env python3
"""
核心模块 - 从现有工具中提取的可复用组件
基于cut_video_fromwhatyouwant_optimized.py的核心功能
"""

import os
import sys
import json
import subprocess
import threading
import time
import psutil
from pathlib import Path
from typing import Dict, Any, Optional, List
from threading import Lock, Event

# Rich库用于终端显示
try:
    from rich.console import Console
    from rich.live import Live
    from rich.progress import Progress, TaskID, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
    from rich.panel import Panel
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False
    print("Warning: Rich library not available, using basic output")

# 全局变量
shutdown_event = Event()
if RICH_AVAILABLE:
    console = Console()
else:
    console = None

class Config:
    """配置管理类"""
    DEFAULT_CONFIG = {
        'ffmpeg_preset': 'medium',
        'ffmpeg_crf': 23,
        'max_memory_usage': 80,
        'update_interval': 0.5,
        'supported_formats': ['.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv'],
        'output_format': 'png',
        'target_fps': 10.0,
        'hardware_acceleration': 'auto'
    }
    
    def __init__(self, config_file: Optional[str] = None):
        self.config = self.DEFAULT_CONFIG.copy()
        if config_file and os.path.exists(config_file):
            self.load_config(config_file)
    
    def load_config(self, config_file: str):
        """从JSON文件加载配置"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                self.config.update(user_config)
        except Exception as e:
            if console:
                console.print(f"[yellow]警告: 无法加载配置文件 {config_file}: {e}[/yellow]")
            else:
                print(f"Warning: Cannot load config file {config_file}: {e}")
    
    def get(self, key: str, default=None):
        return self.config.get(key, default)

class InputValidator:
    """输入验证类"""
    
    @staticmethod
    def validate_video_file(file_path: str, config: Config) -> bool:
        """验证视频文件"""
        if not os.path.exists(file_path):
            if console:
                console.print(f"[red]错误: 输入文件不存在: {file_path}[/red]")
            else:
                print(f"Error: Input file not found: {file_path}")
            return False
        
        file_ext = Path(file_path).suffix.lower()
        if file_ext not in config.get('supported_formats'):
            if console:
                console.print(f"[red]错误: 不支持的文件格式: {file_ext}[/red]")
            else:
                print(f"Error: Unsupported file format: {file_ext}")
            return False
        
        return True
    
    @staticmethod
    def validate_time_format(time_str: str) -> bool:
        """验证时间格式 HH:MM:SS"""
        try:
            parts = time_str.split(':')
            if len(parts) != 3:
                return False
            h, m, s = map(int, parts)
            return 0 <= h <= 23 and 0 <= m <= 59 and 0 <= s <= 59
        except ValueError:
            return False

class ResourceMonitor:
    """资源监控类"""
    
    def __init__(self, max_memory_percent: int = 80):
        self.max_memory_percent = max_memory_percent
        self.process = psutil.Process()
    
    def check_memory_usage(self) -> bool:
        """检查内存使用是否超限"""
        memory_percent = psutil.virtual_memory().percent
        return memory_percent < self.max_memory_percent
    
    def get_optimal_thread_count(self) -> int:
        """根据系统资源计算最优线程数"""
        cpu_count = os.cpu_count()
        memory_gb = psutil.virtual_memory().total / (1024**3)
        
        if memory_gb >= 16:
            return min(cpu_count * 2, 16)
        elif memory_gb >= 8:
            return min(cpu_count * 1.5, 8)
        else:
            return max(cpu_count, 4)

class ProgressDisplay:
    """进度显示类"""
    
    def __init__(self, total_segments: int):
        self.total_segments = total_segments
        self.segments_status = {}
        self.lock = Lock()
        self.live = None
        self.progress = None
        self.task_ids = {}
        
    def start(self):
        """启动进度显示"""
        if not RICH_AVAILABLE:
            print(f"Starting processing {self.total_segments} segments...")
            return
            
        self.progress = Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TimeElapsedColumn(),
            console=console
        )
        
        # 为每个片段创建进度任务
        for i in range(self.total_segments):
            task_id = self.progress.add_task(f"任务 {i+1}", total=100)
            self.task_ids[i] = task_id
        
        self.live = Live(self._create_display(), console=console, refresh_per_second=2)
        self.live.start()
    
    def stop(self):
        """停止进度显示"""
        if self.live:
            self.live.stop()
        elif not RICH_AVAILABLE:
            print("Processing completed.")
    
    def update_segment(self, index: int, status: str, progress: float = 0, message: str = ""):
        """更新片段状态"""
        with self.lock:
            self.segments_status[index] = {
                'status': status,
                'progress': progress,
                'message': message
            }
            
            if RICH_AVAILABLE and self.progress and index in self.task_ids:
                task_id = self.task_ids[index]
                description = f"任务 {index+1}: {message}"
                self.progress.update(task_id, completed=progress, description=description)
            elif not RICH_AVAILABLE:
                print(f"Task {index+1}: {message} ({progress:.1f}%)")
    
    def _create_display(self) -> Panel:
        """创建显示面板"""
        if not self.progress:
            return Panel("初始化中...")
        
        return Panel(
            self.progress,
            title=f"处理进度 (共 {self.total_segments} 个任务)",
            border_style="blue"
        )

class FFmpegProcessor:
    """FFmpeg处理器"""
    
    def __init__(self, config: Config):
        self.config = config
    
    def get_video_info(self, input_file: str) -> Dict[str, Any]:
        """获取视频信息"""
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json',
            '-show_format', '-show_streams', input_file
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            if result.returncode != 0:
                raise RuntimeError(f"FFprobe失败: {result.stderr}")
            
            info = json.loads(result.stdout)
            video_stream = next(
                (s for s in info['streams'] if s['codec_type'] == 'video'), None
            )
            
            if not video_stream:
                raise RuntimeError("未找到视频流")
            
            return {
                'width': int(video_stream['width']),
                'height': int(video_stream['height']),
                'duration': float(info['format'].get('duration', 0)),
                'fps': float(video_stream.get('r_frame_rate', '30/1').split('/')[0]) / float(video_stream.get('r_frame_rate', '30/1').split('/')[1]),
                'codec': video_stream.get('codec_name', 'unknown')
            }
        except subprocess.TimeoutExpired:
            raise RuntimeError("获取视频信息超时")
        except json.JSONDecodeError:
            raise RuntimeError("解析视频信息失败")

def safe_terminal_reset():
    """安全的终端重置函数"""
    try:
        if hasattr(sys.stdout, 'isatty') and sys.stdout.isatty():
            reset_sequences = [
                '\033[!p',      # 软重置
                '\033[?25h',    # 显示光标
                '\033[0m',      # 重置所有属性
                '\033[?1049l',  # 退出备用屏幕缓冲区
            ]
            
            for seq in reset_sequences:
                sys.stdout.write(seq)
            
            sys.stdout.flush()
            
            try:
                os.system('stty sane 2>/dev/null')
            except:
                pass
    except Exception:
        pass
