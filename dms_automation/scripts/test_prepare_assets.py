#!/usr/bin/env python3
"""
测试prepare_assets.py脚本的功能
"""

import os
import sys
import tempfile
import subprocess
from pathlib import Path

def create_test_video(output_path: str, duration: int = 10) -> bool:
    """创建测试视频文件"""
    try:
        cmd = [
            'ffmpeg', '-y',
            '-f', 'lavfi',
            '-i', f'testsrc=duration={duration}:size=1920x1080:rate=30',
            '-c:v', 'libx264',
            '-preset', 'ultrafast',
            '-crf', '23',
            output_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        return result.returncode == 0
    except Exception as e:
        print(f"创建测试视频失败: {e}")
        return False

def test_prepare_assets():
    """测试prepare_assets.py的基本功能"""
    print("=== 测试prepare_assets.py ===")
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        test_video = temp_path / "test_video.mp4"
        assets_dir = temp_path / "assets"
        
        print(f"临时目录: {temp_dir}")
        
        # 1. 创建测试视频
        print("1. 创建测试视频...")
        if not create_test_video(str(test_video), duration=5):
            print("❌ 创建测试视频失败")
            return False
        print(f"✅ 测试视频创建成功: {test_video}")
        
        # 2. 测试参数验证（dry-run）
        print("\n2. 测试参数验证...")
        cmd = [
            sys.executable, "prepare_assets.py",
            "--source", str(test_video),
            "--start", "00:00:01",
            "--end", "00:00:03",
            "--name", "test_asset_001",
            "--fps", "5",
            "--output-dir", str(assets_dir),
            "--dry-run"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=Path(__file__).parent)
        if result.returncode != 0:
            print(f"❌ 参数验证失败: {result.stderr}")
            return False
        print("✅ 参数验证通过")
        
        # 3. 测试实际处理
        print("\n3. 测试实际处理...")
        cmd = [
            sys.executable, "prepare_assets.py",
            "--source", str(test_video),
            "--start", "00:00:01",
            "--end", "00:00:03",
            "--name", "test_asset_001",
            "--fps", "5",
            "--output-dir", str(assets_dir)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=Path(__file__).parent)
        if result.returncode != 0:
            print(f"❌ 处理失败: {result.stderr}")
            print(f"stdout: {result.stdout}")
            return False
        
        print("✅ 处理完成")
        print(f"输出: {result.stdout}")
        
        # 4. 验证输出结果
        print("\n4. 验证输出结果...")
        asset_path = assets_dir / "test_asset_001"
        
        # 检查目录结构
        if not asset_path.exists():
            print(f"❌ 资产包目录不存在: {asset_path}")
            return False
        
        frames_dir = asset_path / "frames"
        if not frames_dir.exists():
            print(f"❌ 帧目录不存在: {frames_dir}")
            return False
        
        metadata_file = asset_path / "metadata.json"
        if not metadata_file.exists():
            print(f"❌ 元数据文件不存在: {metadata_file}")
            return False
        
        # 检查帧文件
        frame_files = list(frames_dir.glob("*.png"))
        if len(frame_files) == 0:
            print("❌ 没有找到帧文件")
            return False
        
        print(f"✅ 找到 {len(frame_files)} 个帧文件")
        
        # 检查元数据
        import json
        try:
            with open(metadata_file, 'r') as f:
                metadata = json.load(f)
            
            required_keys = ["asset_info", "source_info", "extraction_info", "frame_info"]
            for key in required_keys:
                if key not in metadata:
                    print(f"❌ 元数据缺少必需字段: {key}")
                    return False
            
            print("✅ 元数据格式正确")
            
        except Exception as e:
            print(f"❌ 元数据解析失败: {e}")
            return False
        
        print("\n🎉 所有测试通过！")
        return True

def test_error_cases():
    """测试错误情况"""
    print("\n=== 测试错误情况 ===")
    
    # 测试不存在的视频文件
    print("1. 测试不存在的视频文件...")
    cmd = [
        sys.executable, "prepare_assets.py",
        "--source", "nonexistent.mp4",
        "--start", "00:00:01",
        "--end", "00:00:03",
        "--name", "test_asset",
        "--dry-run"
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True, cwd=Path(__file__).parent)
    if result.returncode == 0:
        print("❌ 应该检测到文件不存在错误")
        return False
    print("✅ 正确检测到文件不存在错误")
    
    # 测试无效的时间格式
    print("\n2. 测试无效的时间格式...")
    cmd = [
        sys.executable, "prepare_assets.py",
        "--source", "test.mp4",
        "--start", "invalid_time",
        "--end", "00:00:03",
        "--name", "test_asset",
        "--dry-run"
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True, cwd=Path(__file__).parent)
    if result.returncode == 0:
        print("❌ 应该检测到时间格式错误")
        return False
    print("✅ 正确检测到时间格式错误")
    
    # 测试无效的资产名称
    print("\n3. 测试无效的资产名称...")
    cmd = [
        sys.executable, "prepare_assets.py",
        "--source", "test.mp4",
        "--start", "00:00:01",
        "--end", "00:00:03",
        "--name", "invalid name with spaces",
        "--dry-run"
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True, cwd=Path(__file__).parent)
    if result.returncode == 0:
        print("❌ 应该检测到资产名称格式错误")
        return False
    print("✅ 正确检测到资产名称格式错误")
    
    print("\n🎉 错误情况测试通过！")
    return True

def main():
    """主函数"""
    print("开始测试prepare_assets.py脚本...")
    
    # 检查依赖
    try:
        subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ FFmpeg未安装或不在PATH中")
        return 1
    
    # 检查脚本文件
    script_path = Path(__file__).parent / "prepare_assets.py"
    if not script_path.exists():
        print(f"❌ 脚本文件不存在: {script_path}")
        return 1
    
    # 运行测试
    success = True
    
    try:
        if not test_prepare_assets():
            success = False
        
        if not test_error_cases():
            success = False
            
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        success = False
    
    if success:
        print("\n🎉 所有测试完成！prepare_assets.py工作正常。")
        return 0
    else:
        print("\n❌ 部分测试失败，请检查问题。")
        return 1

if __name__ == '__main__':
    sys.exit(main())
