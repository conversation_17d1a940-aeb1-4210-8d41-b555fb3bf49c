#!/usr/bin/env python3
"""
DMS自动化分析与渲染平台 - 可视化工具模块
基于dms_postmortem_optimised.py的简化版本，专门用于阶段三的渲染功能

作者: DMS Automation Platform
创建时间: 2025-07-10
"""

import cv2
import numpy as np
import math
from typing import Dict, Any, Tuple, Optional


class VisualizationUtils:
    """可视化工具类，提供DMS分析结果的绘图功能"""
    
    def __init__(self):
        # 颜色定义
        self.colors = {
            'face_bbox': (0, 255, 127),      # 人脸检测框
            'face_points': (3, 97, 255),     # 人脸关键点
            'eye_contours': (0, 255, 0),     # 眼部轮廓
            'iris': (0, 0, 255),             # 虹膜
            'pupil': (255, 0, 0),            # 瞳孔
            'gaze_point': (255, 255, 0),     # 注视点
            'distraction': (0, 0, 255),      # 分心状态
            'normal': (100, 200, 255),       # 正常状态
            'warning': (0, 255, 255),        # 警告状态
            'text': (255, 255, 255)          # 文本
        }
    
    def add_text(self, image: np.ndarray, text: str, position: Tuple[int, int], 
                 color: Tuple[int, int, int] = None, font_scale: float = 0.6, 
                 thickness: int = 1) -> np.ndarray:
        """在图像上添加文本"""
        if color is None:
            color = self.colors['text']
        
        cv2.putText(image, text, position, cv2.FONT_HERSHEY_SIMPLEX, 
                   font_scale, color, thickness, cv2.LINE_AA)
        return image
    
    def draw_basic_face_detection(self, image: np.ndarray, face_detected: bool, 
                                 frame_size: Tuple[int, int] = (1920, 1080)) -> np.ndarray:
        """绘制基础人脸检测状态"""
        if face_detected:
            # 绘制简单的人脸检测框（模拟位置）
            h, w = image.shape[:2]
            # 假设人脸在图像中心区域
            face_x = w // 4
            face_y = h // 4
            face_w = w // 2
            face_h = h // 2
            
            cv2.rectangle(image, (face_x, face_y), (face_x + face_w, face_y + face_h), 
                         self.colors['face_bbox'], 2)
            
            # 添加状态文本
            self.add_text(image, "Face Detected", (10, 30), self.colors['normal'])
        else:
            self.add_text(image, "No Face", (10, 30), self.colors['warning'])
        
        return image
    
    def draw_eye_openness(self, image: np.ndarray, eye_openness: float) -> np.ndarray:
        """绘制眼部开合度信息"""
        # 颜色根据开合度变化
        if eye_openness > 0.7:
            color = self.colors['normal']
            status = "Open"
        elif eye_openness > 0.3:
            color = self.colors['warning']
            status = "Partial"
        else:
            color = self.colors['distraction']
            status = "Closed"
        
        # 绘制开合度信息
        text = f"Eye Openness: {eye_openness:.2f} ({status})"
        self.add_text(image, text, (10, 60), color)
        
        # 绘制进度条
        bar_x, bar_y = 10, 80
        bar_w, bar_h = 200, 10
        
        # 背景条
        cv2.rectangle(image, (bar_x, bar_y), (bar_x + bar_w, bar_y + bar_h), 
                     (50, 50, 50), -1)
        
        # 进度条
        progress_w = int(bar_w * eye_openness)
        cv2.rectangle(image, (bar_x, bar_y), (bar_x + progress_w, bar_y + bar_h), 
                     color, -1)
        
        return image
    
    def draw_gaze_direction(self, image: np.ndarray, gaze_x: float, gaze_y: float) -> np.ndarray:
        """绘制注视方向"""
        h, w = image.shape[:2]
        
        # 将归一化的注视方向转换为屏幕坐标
        center_x, center_y = w // 2, h // 2
        
        # 放大注视偏移以便可视化
        scale = 200
        gaze_point_x = int(center_x + gaze_x * scale)
        gaze_point_y = int(center_y + gaze_y * scale)
        
        # 确保点在图像范围内
        gaze_point_x = max(10, min(w - 10, gaze_point_x))
        gaze_point_y = max(10, min(h - 10, gaze_point_y))
        
        # 绘制注视点
        cv2.circle(image, (gaze_point_x, gaze_point_y), 5, self.colors['gaze_point'], -1)
        
        # 绘制从中心到注视点的箭头
        cv2.arrowedLine(image, (center_x, center_y), (gaze_point_x, gaze_point_y), 
                       self.colors['gaze_point'], 2, tipLength=0.1)
        
        # 添加数值信息
        text = f"Gaze: ({gaze_x:.3f}, {gaze_y:.3f})"
        self.add_text(image, text, (10, 120), self.colors['text'])
        
        return image
    
    def draw_distraction_score(self, image: np.ndarray, distraction_score: float) -> np.ndarray:
        """绘制分心评分"""
        # 根据分心评分确定颜色和状态
        if distraction_score < 0.3:
            color = self.colors['normal']
            status = "Focused"
        elif distraction_score < 0.7:
            color = self.colors['warning']
            status = "Mild Distraction"
        else:
            color = self.colors['distraction']
            status = "Distracted"
        
        # 绘制分心评分
        text = f"Distraction: {distraction_score:.2f} ({status})"
        self.add_text(image, text, (10, 150), color)
        
        # 绘制分心评分条
        bar_x, bar_y = 10, 170
        bar_w, bar_h = 200, 15
        
        # 背景条
        cv2.rectangle(image, (bar_x, bar_y), (bar_x + bar_w, bar_y + bar_h), 
                     (50, 50, 50), -1)
        
        # 分心评分条
        score_w = int(bar_w * distraction_score)
        cv2.rectangle(image, (bar_x, bar_y), (bar_x + score_w, bar_y + bar_h), 
                     color, -1)
        
        # 添加刻度线
        for i in range(0, 11):
            tick_x = bar_x + int(bar_w * i / 10)
            cv2.line(image, (tick_x, bar_y + bar_h), (tick_x, bar_y + bar_h + 5), 
                    (200, 200, 200), 1)
        
        return image
    
    def draw_frame_info(self, image: np.ndarray, frame_id: int, timestamp: float) -> np.ndarray:
        """绘制帧信息"""
        # 在右上角显示帧信息
        h, w = image.shape[:2]
        
        frame_text = f"Frame: {frame_id}"
        time_text = f"Time: {timestamp:.2f}s"
        
        # 计算文本位置（右上角）
        frame_pos = (w - 150, 30)
        time_pos = (w - 150, 60)
        
        self.add_text(image, frame_text, frame_pos, self.colors['text'])
        self.add_text(image, time_text, time_pos, self.colors['text'])
        
        return image
    
    def process_frame_simple(self, image: np.ndarray, analysis_data: Dict[str, Any]) -> np.ndarray:
        """
        简化版本的帧处理函数，基于阶段二的分析结果进行可视化
        
        Args:
            image: 原始帧图像
            analysis_data: 阶段二的分析结果数据
            
        Returns:
            处理后的图像
        """
        # 复制图像以避免修改原图
        result_image = image.copy()
        
        # 调整图像大小以便显示
        result_image = cv2.resize(result_image, (1280, 720))
        
        # 提取分析数据
        frame_id = analysis_data.get('frame_id', 0)
        timestamp = analysis_data.get('timestamp', 0.0)
        face_detected = analysis_data.get('face_detected', False)
        eye_openness = analysis_data.get('eye_openness', 0.0)
        gaze_direction = analysis_data.get('gaze_direction', {'x': 0.0, 'y': 0.0})
        distraction_score = analysis_data.get('distraction_score', 0.0)
        
        # 绘制各种可视化元素
        result_image = self.draw_frame_info(result_image, frame_id, timestamp)
        result_image = self.draw_basic_face_detection(result_image, face_detected)
        result_image = self.draw_eye_openness(result_image, eye_openness)
        result_image = self.draw_gaze_direction(result_image, 
                                              gaze_direction['x'], gaze_direction['y'])
        result_image = self.draw_distraction_score(result_image, distraction_score)
        
        return result_image


def create_visualization_utils() -> VisualizationUtils:
    """创建可视化工具实例的工厂函数"""
    return VisualizationUtils()


if __name__ == "__main__":
    # 简单测试
    print("DMS可视化工具模块 - 测试模式")
    
    # 创建测试图像
    test_image = np.zeros((720, 1280, 3), dtype=np.uint8)
    
    # 创建可视化工具
    viz_utils = create_visualization_utils()
    
    # 测试数据
    test_data = {
        'frame_id': 42,
        'timestamp': 1.5,
        'face_detected': True,
        'eye_openness': 0.8,
        'gaze_direction': {'x': 0.1, 'y': -0.05},
        'distraction_score': 0.3
    }
    
    # 处理测试图像
    result = viz_utils.process_frame_simple(test_image, test_data)
    
    print("✅ 可视化工具模块测试完成")
