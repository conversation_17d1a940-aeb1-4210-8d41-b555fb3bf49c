#!/usr/bin/env python3
"""
DMS自动化分析与渲染平台 - 阶段一：素材准备
基于cut_video_fromwhatyouwant_optimized.py改造，实现视频到帧资产包的转换功能
"""

import os
import sys
import signal
import subprocess
import argparse
import json
import atexit
import time
from pathlib import Path
from typing import List, Tuple, Optional, Dict, Any

# 导入核心模块
from core_modules import (
    Config, InputValidator, ResourceMonitor, ProgressDisplay,
    FFmpegProcessor, safe_terminal_reset, shutdown_event, console
)

# 导入资产包管理器
from asset_package_manager import AssetPackageManager

# Rich库用于终端显示
try:
    from rich.panel import Panel
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False
    Panel = None

class FrameAssetProcessor:
    """帧资产处理器 - 基于VideoProcessor改造"""
    
    def __init__(self, input_file: str, asset_name: str, config: Config,
                 progress_display: ProgressDisplay, asset_manager: AssetPackageManager):
        self.input_file = input_file
        self.asset_name = asset_name
        self.config = config
        self.progress_display = progress_display
        self.asset_manager = asset_manager
        self.ffmpeg_processor = FFmpegProcessor(config)
        self.video_info = self.ffmpeg_processor.get_video_info(input_file)
        
        # 存储提取的帧文件列表
        self.extracted_frames = []
    
    def process_time_range(self, start_time: str, end_time: str) -> Tuple[bool, str]:
        """处理指定时间范围，提取帧序列"""
        if shutdown_event.is_set():
            return False, "User interrupted"
        
        try:
            # 更新进度状态
            self.progress_display.update_segment(0, 'processing', 0, f"提取帧序列: {start_time} - {end_time}")
            
            # 创建资产包
            source_info = {
                "video_path": os.path.abspath(self.input_file),
                "video_size": {
                    "width": self.video_info["width"],
                    "height": self.video_info["height"]
                },
                "video_fps": self.video_info.get("fps", 30.0),
                "video_duration": self.video_info.get("duration", 0.0),
                "video_codec": self.video_info.get("codec", "unknown")
            }
            
            extraction_info = {
                "time_range": {
                    "start_time": start_time,
                    "end_time": end_time,
                    "duration": self._time_to_seconds(end_time) - self._time_to_seconds(start_time)
                },
                "target_fps": self.config.get('target_fps', 10.0),
                "output_format": self.config.get('output_format', 'png'),
                "frame_size": {
                    "width": self.video_info["width"],
                    "height": self.video_info["height"]
                }
            }
            
            # 添加ROI信息（如果有）
            roi = self.config.get('roi')
            if roi:
                w, h, x, y = map(int, roi.split(':'))
                extraction_info["roi"] = {"x": x, "y": y, "width": w, "height": h}
                extraction_info["frame_size"] = {"width": w, "height": h}
            
            # 创建资产包
            asset_path = self.asset_manager.create_asset_package(
                self.asset_name, source_info, extraction_info
            )
            frames_dir = asset_path / "frames"
            
            # 构建FFmpeg命令提取帧
            success = self._extract_frames_with_ffmpeg(start_time, end_time, frames_dir)
            
            if success:
                # 更新帧信息
                frame_files = sorted([f.name for f in frames_dir.glob("*.png")])
                self.asset_manager.update_frame_info(self.asset_name, frame_files)
                
                self.progress_display.update_segment(0, 'completed', 100, f"完成: 提取了 {len(frame_files)} 帧")
                return True, str(asset_path)
            else:
                self.progress_display.update_segment(0, 'failed', 0, "帧提取失败")
                return False, "Frame extraction failed"
                
        except Exception as e:
            self.progress_display.update_segment(0, 'failed', 0, f"异常: {str(e)[:30]}...")
            return False, str(e)
    
    def _extract_frames_with_ffmpeg(self, start_time: str, end_time: str, output_dir: Path) -> bool:
        """使用FFmpeg提取帧序列"""
        try:
            # 构建FFmpeg命令
            target_fps = self.config.get('target_fps', 10.0)
            output_format = self.config.get('output_format', 'png')
            
            cmd = [
                'ffmpeg', '-y',
                '-i', self.input_file,
                '-ss', start_time,
                '-to', end_time,
                '-vf', f'fps={target_fps}',
                '-q:v', '1',  # 高质量
                str(output_dir / f"%06d.{output_format}")
            ]
            
            # 添加ROI裁剪（如果有）
            roi = self.config.get('roi')
            if roi:
                w, h, x, y = map(int, roi.split(':'))
                # 在fps滤镜后添加crop滤镜
                cmd[cmd.index('-vf') + 1] = f'fps={target_fps},crop={w}:{h}:{x}:{y}'
            
            if console:
                console.print(f"[blue]执行FFmpeg命令: {' '.join(cmd)}[/blue]")
            else:
                print(f"执行FFmpeg命令: {' '.join(cmd)}")
            
            # 启动FFmpeg进程
            process = subprocess.Popen(
                cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                text=True, bufsize=1, universal_newlines=True
            )
            
            # 监控进程并更新进度
            return self._monitor_ffmpeg_process(process, start_time, end_time)
            
        except Exception as e:
            if console:
                console.print(f"[red]FFmpeg执行异常: {e}[/red]")
            else:
                print(f"FFmpeg执行异常: {e}")
            return False
    
    def _monitor_ffmpeg_process(self, process: subprocess.Popen, 
                              start_time: str, end_time: str) -> bool:
        """监控FFmpeg进程并更新进度"""
        total_duration = self._time_to_seconds(end_time) - self._time_to_seconds(start_time)
        
        try:
            while True:
                if shutdown_event.is_set():
                    process.terminate()
                    return False
                
                # 检查进程是否结束
                if process.poll() is not None:
                    break
                
                # 读取stderr输出（FFmpeg的进度信息）
                try:
                    line = process.stderr.readline()
                    if line:
                        # 解析进度信息
                        progress_percent = self._parse_ffmpeg_progress(line, total_duration)
                        if progress_percent is not None:
                            message = f"提取帧序列 ({progress_percent:.1f}%)"
                            self.progress_display.update_segment(0, 'processing', progress_percent, message)
                except:
                    pass
                
                time.sleep(0.1)
            
            # 等待进程完成
            return_code = process.wait(timeout=10)
            return return_code == 0
            
        except subprocess.TimeoutExpired:
            process.kill()
            return False
        except Exception:
            process.terminate()
            return False
    
    def _parse_ffmpeg_progress(self, line: str, total_duration: float) -> Optional[float]:
        """解析FFmpeg进度信息"""
        try:
            if 'time=' in line:
                # 提取时间信息，格式如: time=00:01:23.45
                time_part = line.split('time=')[1].split()[0]
                current_seconds = self._time_to_seconds(time_part)
                if total_duration > 0:
                    return min((current_seconds / total_duration) * 100, 100)
        except:
            pass
        return None
    
    def _time_to_seconds(self, time_str: str) -> float:
        """时间字符串转秒数"""
        try:
            parts = time_str.split(':')
            if len(parts) == 3:
                h, m, s = map(float, parts)
                return h * 3600 + m * 60 + s
        except:
            pass
        return 0.0

def signal_handler(signum, frame):
    """信号处理器 - 优雅退出"""
    if console:
        console.print("\n[yellow]接收到中断信号，正在优雅退出...[/yellow]")
    else:
        print("\n接收到中断信号，正在优雅退出...")
    shutdown_event.set()

def cleanup():
    """清理函数 - 确保终端状态恢复"""
    try:
        safe_terminal_reset()
        if console:
            console.clear()
            console.show_cursor(True)
            console.print("\n[green]程序已安全退出，终端状态已恢复[/green]")
        else:
            print("\n程序已安全退出，终端状态已恢复")
    except Exception:
        print("\n程序已退出")

def main():
    """主函数"""
    # 注册信号处理器和清理函数
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    atexit.register(cleanup)
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(
        description='DMS自动化分析与渲染平台 - 阶段一：素材准备',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python3 prepare_assets.py --source video.mp4 --start 00:01:00 --end 00:01:30 --name test_asset
  python3 prepare_assets.py --source video.mp4 --start 00:01:00 --end 00:01:30 --name test_asset --fps 15 --roi 1280:800:0:0
        """
    )
    
    parser.add_argument('--source', required=True, help='源视频文件路径')
    parser.add_argument('--start', required=True, help='开始时间 (HH:MM:SS)')
    parser.add_argument('--end', required=True, help='结束时间 (HH:MM:SS)')
    parser.add_argument('--name', required=True, help='资产名称')
    parser.add_argument('--fps', type=float, default=10.0, help='目标帧率 (默认: 10.0)')
    parser.add_argument('--roi', help='感兴趣区域，格式: width:height:x:y')
    parser.add_argument('--config', help='配置文件路径')
    parser.add_argument('--output-dir', default='../assets', help='输出目录 (默认: ../assets)')
    parser.add_argument('--dry-run', action='store_true', help='仅验证参数，不执行实际处理')
    
    args = parser.parse_args()
    
    try:
        # 加载配置
        config = Config(args.config)
        config.config['target_fps'] = args.fps
        if args.roi:
            config.config['roi'] = args.roi
        
        # 验证输入
        if not InputValidator.validate_video_file(args.source, config):
            return 1
        
        if not InputValidator.validate_time_format(args.start):
            if console:
                console.print(f"[red]无效的开始时间格式: {args.start}[/red]")
            else:
                print(f"无效的开始时间格式: {args.start}")
            return 1

        if not InputValidator.validate_time_format(args.end):
            if console:
                console.print(f"[red]无效的结束时间格式: {args.end}[/red]")
            else:
                print(f"无效的结束时间格式: {args.end}")
            return 1
        
        # 验证资产名称
        asset_manager = AssetPackageManager(args.output_dir)
        if not asset_manager._validate_asset_name(args.name):
            if console:
                console.print(f"[red]无效的资产名称: {args.name}[/red]")
            else:
                print(f"无效的资产名称: {args.name}")
            return 1

        # 检查资产是否已存在
        if args.name in asset_manager.list_asset_packages():
            if console:
                console.print(f"[red]资产包已存在: {args.name}[/red]")
            else:
                print(f"资产包已存在: {args.name}")
            return 1

        # 如果是dry-run模式，只验证参数
        if args.dry_run:
            if console:
                console.print("[green]参数验证通过，所有设置正确[/green]")
            else:
                print("参数验证通过，所有设置正确")
            return 0
        
        # 显示处理信息
        if RICH_AVAILABLE and Panel:
            console.print(Panel(
                f"[bold blue]DMS自动化分析与渲染平台 - 阶段一：素材准备[/bold blue]\n\n"
                f"源视频: {args.source}\n"
                f"时间范围: {args.start} - {args.end}\n"
                f"资产名称: {args.name}\n"
                f"目标帧率: {args.fps} fps\n"
                f"输出目录: {args.output_dir}",
                title="处理参数",
                border_style="blue"
            ))
        else:
            print("=== DMS自动化分析与渲染平台 - 阶段一：素材准备 ===")
            print(f"源视频: {args.source}")
            print(f"时间范围: {args.start} - {args.end}")
            print(f"资产名称: {args.name}")
            print(f"目标帧率: {args.fps} fps")
            print(f"输出目录: {args.output_dir}")
            print("=" * 50)
        
        # 初始化进度显示
        progress_display = ProgressDisplay(1)  # 只有一个处理任务
        
        # 初始化帧资产处理器
        try:
            processor = FrameAssetProcessor(
                args.source, args.name, config, progress_display, asset_manager
            )
        except Exception as e:
            if console:
                console.print(f"[red]初始化处理器失败: {e}[/red]")
            else:
                print(f"初始化处理器失败: {e}")
            return 1
        
        # 开始处理
        progress_display.start()
        
        try:
            start_time = time.time()
            success, result = processor.process_time_range(args.start, args.end)
            processing_time = time.time() - start_time
            
            if success:
                if console:
                    console.print(f"\n[green]✅ 帧资产包创建成功！[/green]")
                    console.print(f"[blue]📁 资产包路径: {result}[/blue]")
                    console.print(f"[blue]⏱️  处理耗时: {processing_time:.2f} 秒[/blue]")
                else:
                    print(f"\n✅ 帧资产包创建成功！")
                    print(f"📁 资产包路径: {result}")
                    print(f"⏱️  处理耗时: {processing_time:.2f} 秒")

                # 验证资产包
                is_valid, errors = asset_manager.validate_asset_package(args.name)
                if is_valid:
                    if console:
                        console.print(f"[green]✅ 资产包验证通过[/green]")
                    else:
                        print("✅ 资产包验证通过")
                else:
                    if console:
                        console.print(f"[yellow]⚠️  资产包验证发现问题:[/yellow]")
                        for error in errors:
                            console.print(f"  - {error}")
                    else:
                        print("⚠️  资产包验证发现问题:")
                        for error in errors:
                            print(f"  - {error}")

                return 0
            else:
                if console:
                    console.print(f"\n[red]❌ 处理失败: {result}[/red]")
                else:
                    print(f"\n❌ 处理失败: {result}")
                return 1
                
        finally:
            progress_display.stop()
            cleanup()
    
    except KeyboardInterrupt:
        if console:
            console.print("\n[yellow]用户中断处理[/yellow]")
        else:
            print("\n用户中断处理")
        return 130
    except Exception as e:
        if console:
            console.print(f"\n[red]程序异常: {e}[/red]")
        else:
            print(f"\n程序异常: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
