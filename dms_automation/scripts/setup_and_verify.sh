#!/bin/bash
# DMS自动化分析与渲染平台 - 阶段二：环境配置和验证脚本
# 功能：检查系统依赖、验证C++工具包、配置远程服务器连接

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CPP_KITS_DIR="$PROJECT_ROOT/cpp_kits"
CONFIG_FILE="$SCRIPT_DIR/config.json"

# 远程服务器配置
REMOTE_HOST="***********"
REMOTE_USER="root"
REMOTE_PASSWORD="root"
REMOTE_MODEL_PATH="/userfs/model"
REMOTE_SERVICE_PATH="/userfs/tx_dms_oax_test_tool_update"

# 检查系统依赖
check_system_dependencies() {
    log_info "检查系统依赖..."
    
    local missing_deps=()
    
    # 检查必需的命令
    local required_commands=("ssh" "scp" "md5sum" "python3" "ldd")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            missing_deps+=("$cmd")
        fi
    done
    
    # 检查Python模块
    if ! python3 -c "import json, subprocess, os, hashlib" 2>/dev/null; then
        missing_deps+=("python3-standard-modules")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "缺少以下依赖: ${missing_deps[*]}"
        log_info "请安装缺少的依赖后重试"
        return 1
    fi
    
    log_success "系统依赖检查通过"
    return 0
}

# 计算文件MD5
calculate_md5() {
    local file_path="$1"
    if [ -f "$file_path" ]; then
        md5sum "$file_path" | cut -d' ' -f1
    else
        echo ""
    fi
}

# 验证C++工具包
verify_cpp_kit() {
    local kit_path="$1"
    local kit_name="$(basename "$kit_path")"
    
    log_info "验证C++工具包: $kit_name"
    
    if [ ! -d "$kit_path" ]; then
        log_error "工具包目录不存在: $kit_path"
        return 1
    fi
    
    # 检查metadata.json
    local metadata_file="$kit_path/metadata.json"
    if [ ! -f "$metadata_file" ]; then
        log_error "元数据文件不存在: $metadata_file"
        return 1
    fi
    
    # 检查主要可执行文件
    local main_executable
    main_executable=$(python3 -c "
import json
with open('$metadata_file', 'r') as f:
    data = json.load(f)
print(data.get('main_executable', 'test_dms_internal_postmortem'))
" 2>/dev/null)
    
    local executable_path="$kit_path/$main_executable"
    if [ ! -f "$executable_path" ]; then
        log_error "主要可执行文件不存在: $executable_path"
        return 1
    fi
    
    if [ ! -x "$executable_path" ]; then
        log_error "可执行文件没有执行权限: $executable_path"
        return 1
    fi
    
    # 检查动态链接库
    local lib_file="$kit_path/libtx_dms.so"
    if [ ! -f "$lib_file" ]; then
        log_error "动态链接库不存在: $lib_file"
        return 1
    fi
    
    # 检查依赖库
    log_info "检查可执行文件依赖..."
    if ! ldd "$executable_path" | grep -q "libtx_dms.so"; then
        log_warning "可执行文件可能未正确链接到libtx_dms.so"
    fi
    
    # 检查缺失的依赖
    local missing_libs
    missing_libs=$(ldd "$executable_path" | grep "not found" || true)
    if [ -n "$missing_libs" ]; then
        log_error "发现缺失的依赖库:"
        echo "$missing_libs"
        return 1
    fi
    
    # 验证模型文件MD5
    log_info "验证模型文件MD5校验和..."
    local model_files
    model_files=$(python3 -c "
import json
with open('$metadata_file', 'r') as f:
    data = json.load(f)
model_files = data.get('model_files', [])
for model_file in model_files:
    print(model_file)
" 2>/dev/null)
    
    local checksums
    checksums=$(python3 -c "
import json
with open('$metadata_file', 'r') as f:
    data = json.load(f)
checksums = data.get('model_md5_checksums', {})
import json
print(json.dumps(checksums))
" 2>/dev/null)
    
    if [ -n "$model_files" ]; then
        while IFS= read -r model_file; do
            if [ -n "$model_file" ]; then
                local model_path="$kit_path/$model_file"
                if [ -f "$model_path" ]; then
                    local actual_md5
                    actual_md5=$(calculate_md5 "$model_path")
                    local expected_md5
                    expected_md5=$(echo "$checksums" | python3 -c "
import json, sys
data = json.load(sys.stdin)
print(data.get('$model_file', ''))
" 2>/dev/null)
                    
                    if [ -n "$expected_md5" ] && [ "$actual_md5" != "$expected_md5" ]; then
                        log_error "模型文件MD5校验失败: $model_file"
                        log_error "期望: $expected_md5"
                        log_error "实际: $actual_md5"
                        return 1
                    else
                        log_success "模型文件校验通过: $model_file"
                    fi
                else
                    log_error "模型文件不存在: $model_path"
                    return 1
                fi
            fi
        done <<< "$model_files"
    fi
    
    log_success "C++工具包验证通过: $kit_name"
    return 0
}

# 测试远程服务器连接
test_remote_connection() {
    log_info "测试远程服务器连接..."
    
    # 使用sshpass进行密码认证（如果可用）
    local ssh_cmd="ssh"
    if command -v sshpass &> /dev/null; then
        ssh_cmd="sshpass -p '$REMOTE_PASSWORD' ssh"
    fi
    
    # 测试SSH连接
    log_info "测试SSH连接到 $REMOTE_USER@$REMOTE_HOST..."
    if eval "$ssh_cmd -o ConnectTimeout=10 -o StrictHostKeyChecking=no $REMOTE_USER@$REMOTE_HOST 'echo \"SSH连接成功\"'" &>/dev/null; then
        log_success "SSH连接测试通过"
    else
        log_warning "SSH连接失败"
        log_info "提示:"
        log_info "1. 远程服务器 $REMOTE_HOST 可能不可访问"
        log_info "2. 需要安装sshpass: sudo apt-get install sshpass"
        log_info "3. 用户名密码: $REMOTE_USER/$REMOTE_PASSWORD"
        return 1
    fi
    
    # 检查远程路径
    log_info "检查远程模型路径: $REMOTE_MODEL_PATH"
    if eval "$ssh_cmd -o ConnectTimeout=10 -o StrictHostKeyChecking=no $REMOTE_USER@$REMOTE_HOST 'test -d $REMOTE_MODEL_PATH'" &>/dev/null; then
        log_success "远程模型路径存在"
    else
        log_warning "远程模型路径不存在: $REMOTE_MODEL_PATH"
    fi
    
    log_info "检查远程服务程序: $REMOTE_SERVICE_PATH"
    if eval "$ssh_cmd -o ConnectTimeout=10 -o StrictHostKeyChecking=no $REMOTE_USER@$REMOTE_HOST 'test -f $REMOTE_SERVICE_PATH'" &>/dev/null; then
        log_success "远程服务程序存在"
    else
        log_warning "远程服务程序不存在: $REMOTE_SERVICE_PATH"
    fi
    
    return 0
}

# 配置环境变量
setup_environment() {
    log_info "配置环境变量..."
    
    # 创建必要的目录
    mkdir -p "$CPP_KITS_DIR"
    mkdir -p "$PROJECT_ROOT/results"
    
    log_success "环境配置完成"
    return 0
}

# 主函数
main() {
    echo "========================================"
    echo "DMS自动化分析与渲染平台 - 环境配置和验证"
    echo "========================================"
    
    # 检查系统依赖
    if ! check_system_dependencies; then
        exit 1
    fi
    
    # 配置环境
    if ! setup_environment; then
        exit 1
    fi
    
    # 验证所有C++工具包
    if [ -d "$CPP_KITS_DIR" ]; then
        local kit_count=0
        for kit_dir in "$CPP_KITS_DIR"/*; do
            if [ -d "$kit_dir" ]; then
                if verify_cpp_kit "$kit_dir"; then
                    kit_count=$((kit_count + 1))
                else
                    log_error "工具包验证失败: $(basename "$kit_dir")"
                    exit 1
                fi
            fi
        done
        
        if [ $kit_count -eq 0 ]; then
            log_warning "未找到C++工具包，请先安装工具包到 $CPP_KITS_DIR"
        else
            log_success "验证了 $kit_count 个C++工具包"
        fi
    fi
    
    # 测试远程连接（可选）
    log_info "跳过远程连接测试（可在需要时手动启用）"
    # if test_remote_connection; then
    #     log_success "远程连接测试通过"
    # else
    #     log_warning "远程连接测试失败，但不影响本地处理"
    # fi
    
    echo "========================================"
    log_success "环境配置和验证完成！"
    echo "========================================"
    
    return 0
}

# 执行主函数
main "$@"
