#!/usr/bin/env python3
"""
性能测试脚本 - 测试prepare_assets.py在不同场景下的性能
"""

import os
import sys
import time
import tempfile
import subprocess
import json
from pathlib import Path
from typing import Dict, List, Tu<PERSON>

def create_test_video(output_path: str, duration: int = 30, resolution: str = "1920x1080") -> bool:
    """创建指定时长和分辨率的测试视频"""
    try:
        cmd = [
            'ffmpeg', '-y',
            '-f', 'lavfi',
            '-i', f'testsrc=duration={duration}:size={resolution}:rate=30',
            '-c:v', 'libx264',
            '-preset', 'ultrafast',
            '-crf', '23',
            output_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        return result.returncode == 0
    except Exception as e:
        print(f"创建测试视频失败: {e}")
        return False

def run_performance_test(test_name: str, video_path: str, start_time: str, 
                        end_time: str, fps: float, assets_dir: str, 
                        roi: str = None) -> Dict[str, any]:
    """运行单个性能测试"""
    print(f"\n--- {test_name} ---")
    
    asset_name = f"perf_test_{int(time.time())}"
    
    cmd = [
        sys.executable, "prepare_assets.py",
        "--source", video_path,
        "--start", start_time,
        "--end", end_time,
        "--name", asset_name,
        "--fps", str(fps),
        "--output-dir", assets_dir
    ]
    
    if roi:
        cmd.extend(["--roi", roi])
    
    print(f"命令: {' '.join(cmd)}")
    
    # 记录开始时间
    start = time.time()
    
    # 执行命令
    result = subprocess.run(cmd, capture_output=True, text=True, cwd=Path(__file__).parent)
    
    # 记录结束时间
    end = time.time()
    processing_time = end - start
    
    # 分析结果
    success = result.returncode == 0
    
    if success:
        # 统计生成的帧数
        asset_path = Path(assets_dir) / asset_name
        frames_dir = asset_path / "frames"
        frame_count = len(list(frames_dir.glob("*.png")))
        
        # 计算总文件大小
        total_size = sum(f.stat().st_size for f in frames_dir.glob("*.png"))
        
        # 读取元数据
        metadata_path = asset_path / "metadata.json"
        with open(metadata_path, 'r') as f:
            metadata = json.load(f)
        
        # 清理测试文件
        import shutil
        shutil.rmtree(asset_path)
        
        return {
            "success": True,
            "processing_time": processing_time,
            "frame_count": frame_count,
            "total_size_mb": total_size / (1024 * 1024),
            "fps_actual": frame_count / (processing_time if processing_time > 0 else 1),
            "metadata": metadata
        }
    else:
        print(f"测试失败: {result.stderr}")
        return {
            "success": False,
            "error": result.stderr,
            "processing_time": processing_time
        }

def main():
    """主函数"""
    print("=== DMS自动化分析与渲染平台 - 性能测试 ===")
    
    # 检查依赖
    try:
        subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ FFmpeg未安装或不在PATH中")
        return 1
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        assets_dir = temp_path / "assets"
        assets_dir.mkdir()
        
        print(f"临时目录: {temp_dir}")
        
        # 测试场景配置
        test_scenarios = [
            {
                "name": "短视频低帧率",
                "duration": 10,
                "resolution": "1920x1080",
                "start": "00:00:01",
                "end": "00:00:06",
                "fps": 5.0,
                "roi": None
            },
            {
                "name": "短视频高帧率",
                "duration": 10,
                "resolution": "1920x1080", 
                "start": "00:00:01",
                "end": "00:00:06",
                "fps": 30.0,
                "roi": None
            },
            {
                "name": "长视频中等帧率",
                "duration": 60,
                "resolution": "1920x1080",
                "start": "00:00:10",
                "end": "00:00:40",
                "fps": 15.0,
                "roi": None
            },
            {
                "name": "ROI裁剪测试",
                "duration": 15,
                "resolution": "1920x1080",
                "start": "00:00:02",
                "end": "00:00:08",
                "fps": 10.0,
                "roi": "1280:720:320:180"
            },
            {
                "name": "高分辨率测试",
                "duration": 10,
                "resolution": "2560x1440",
                "start": "00:00:01",
                "end": "00:00:05",
                "fps": 10.0,
                "roi": None
            }
        ]
        
        results = []
        
        for scenario in test_scenarios:
            print(f"\n{'='*60}")
            print(f"准备测试场景: {scenario['name']}")
            
            # 创建测试视频
            video_path = temp_path / f"test_{scenario['name'].replace(' ', '_')}.mp4"
            print(f"创建测试视频: {scenario['resolution']}, {scenario['duration']}秒")
            
            if not create_test_video(str(video_path), scenario['duration'], scenario['resolution']):
                print(f"❌ 创建测试视频失败: {scenario['name']}")
                continue
            
            # 运行性能测试
            result = run_performance_test(
                scenario['name'],
                str(video_path),
                scenario['start'],
                scenario['end'],
                scenario['fps'],
                str(assets_dir),
                scenario['roi']
            )
            
            result['scenario'] = scenario
            results.append(result)
            
            # 显示结果
            if result['success']:
                print(f"✅ 测试成功")
                print(f"   处理时间: {result['processing_time']:.2f} 秒")
                print(f"   生成帧数: {result['frame_count']}")
                print(f"   总文件大小: {result['total_size_mb']:.2f} MB")
                print(f"   实际处理帧率: {result['fps_actual']:.2f} 帧/秒")
            else:
                print(f"❌ 测试失败")
        
        # 生成性能报告
        print(f"\n{'='*60}")
        print("=== 性能测试报告 ===")
        
        successful_tests = [r for r in results if r['success']]
        failed_tests = [r for r in results if not r['success']]
        
        print(f"总测试数: {len(results)}")
        print(f"成功: {len(successful_tests)}")
        print(f"失败: {len(failed_tests)}")
        
        if successful_tests:
            print(f"\n--- 成功测试详情 ---")
            print(f"{'场景':<20} {'时间(s)':<10} {'帧数':<8} {'大小(MB)':<12} {'帧率(fps)':<12}")
            print("-" * 70)
            
            for result in successful_tests:
                scenario_name = result['scenario']['name']
                print(f"{scenario_name:<20} {result['processing_time']:<10.2f} "
                      f"{result['frame_count']:<8} {result['total_size_mb']:<12.2f} "
                      f"{result['fps_actual']:<12.2f}")
            
            # 统计信息
            avg_time = sum(r['processing_time'] for r in successful_tests) / len(successful_tests)
            total_frames = sum(r['frame_count'] for r in successful_tests)
            total_size = sum(r['total_size_mb'] for r in successful_tests)
            
            print(f"\n--- 统计信息 ---")
            print(f"平均处理时间: {avg_time:.2f} 秒")
            print(f"总生成帧数: {total_frames}")
            print(f"总文件大小: {total_size:.2f} MB")
        
        if failed_tests:
            print(f"\n--- 失败测试 ---")
            for result in failed_tests:
                print(f"❌ {result['scenario']['name']}: {result.get('error', 'Unknown error')}")
        
        # 性能建议
        print(f"\n--- 性能建议 ---")
        if successful_tests:
            max_time = max(r['processing_time'] for r in successful_tests)
            min_time = min(r['processing_time'] for r in successful_tests)
            
            if max_time > 10:
                print("⚠️  发现处理时间较长的测试，建议:")
                print("   - 降低目标帧率")
                print("   - 使用ROI裁剪减少处理区域")
                print("   - 缩短时间范围")
            
            if total_size > 100:
                print("⚠️  生成文件较大，建议:")
                print("   - 使用JPEG格式替代PNG")
                print("   - 调整图片质量设置")
                print("   - 考虑压缩选项")
            
            print("✅ 总体性能良好，prepare_assets.py工作正常")
        
        return 0 if len(failed_tests) == 0 else 1

if __name__ == '__main__':
    sys.exit(main())
