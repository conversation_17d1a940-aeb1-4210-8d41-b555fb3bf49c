#!/usr/bin/env python3
"""
DMS自动化分析与渲染平台 - 阶段二：C++批量处理脚本
基于dms_postmortem_optimised.py改造，实现帧资产包的批量C++分析处理
"""

import os
import sys
import json
import time
import subprocess
import threading
import hashlib
import signal
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加脚本目录到Python路径
script_dir = Path(__file__).parent
sys.path.insert(0, str(script_dir))

# 导入自定义模块
from remote_service_manager import RemoteServiceManager
from cpp_kit_copy_manager import CppKitCopyManager
from ui_state_manager import UIStateManager, ContextualUIManager

# 导入核心模块
from core_modules import (
    Config, ResourceMonitor, ProgressDisplay, console
)
from asset_package_manager import AssetPackageManager
from config_validator import ConfigValidator

class CppKitManager:
    """C++工具包管理器"""
    
    def __init__(self, cpp_kits_dir: str = "cpp_kits"):
        # 确保使用绝对路径避免多线程环境中的路径问题
        self.cpp_kits_dir = Path(cpp_kits_dir).resolve()
        self.available_kits = {}
        self._load_available_kits()
    
    def _load_available_kits(self):
        """加载可用的C++工具包"""
        if not self.cpp_kits_dir.exists():
            return
        
        for kit_dir in self.cpp_kits_dir.iterdir():
            if kit_dir.is_dir():
                metadata_file = kit_dir / "metadata.json"
                if metadata_file.exists():
                    try:
                        with open(metadata_file, 'r') as f:
                            metadata = json.load(f)
                        
                        kit_name = metadata['kit_info']['name']
                        executable_path = kit_dir / metadata['main_executable']
                        self.available_kits[kit_name] = {
                            'path': str(kit_dir),
                            'metadata': metadata,
                            'executable': str(executable_path)
                        }
                    except Exception as e:
                        if console:
                            console.print(f"[yellow]警告: 无法加载工具包 {kit_dir.name}: {e}[/yellow]")
    
    def get_kit(self, kit_name: str) -> Optional[Dict[str, Any]]:
        """获取指定的C++工具包"""
        return self.available_kits.get(kit_name)
    
    def list_kits(self) -> List[str]:
        """列出所有可用的C++工具包"""
        return list(self.available_kits.keys())

class CppAnalysisProcessor:
    """C++分析处理器"""
    
    def __init__(self, config: Config, cpp_kit_manager: CppKitManager, asset_manager: AssetPackageManager = None):
        self.config = config
        self.cpp_kit_manager = cpp_kit_manager
        self.asset_manager = asset_manager or AssetPackageManager()
        self.resource_monitor = ResourceMonitor()
        self.remote_service_manager = RemoteServiceManager()
        self.cpp_kit_copy_manager = CppKitCopyManager()
        self.ui_manager = UIStateManager()
        # 使用绝对路径避免多线程环境中的路径问题
        script_dir = Path(__file__).parent
        self.results_dir = script_dir / "../results"
        self.results_dir.mkdir(exist_ok=True)
        
        # 处理统计
        self.processed_count = 0
        self.failed_count = 0
        self.total_processing_time = 0.0
    
    def call_cpp_program(self, kit_info: Dict[str, Any], input_file: str,
                        output_dir: str, timeout: int = 300, total_frames: int = 0) -> Tuple[bool, str]:
        """调用C++程序进行分析
        
        Args:
            kit_info: C++工具包信息
            input_file: 输入文件路径
            output_dir: 输出目录
            timeout: 超时时间（秒）
            
        Returns:
            (是否成功, 结果信息)
        """
        try:
            executable_path = kit_info['executable']
            kit_path = kit_info['path']

            if not os.path.exists(executable_path):
                return False, f"可执行文件不存在: {executable_path}"
            
            # 设置环境变量
            env = os.environ.copy()
            env['LD_LIBRARY_PATH'] = f"{kit_path}:{env.get('LD_LIBRARY_PATH', '')}"

            # 禁用代理，确保直接连接远程服务
            env.pop('http_proxy', None)
            env.pop('https_proxy', None)
            env.pop('HTTP_PROXY', None)
            env.pop('HTTPS_PROXY', None)
            
            # 创建输出目录
            os.makedirs(output_dir, exist_ok=True)
            
            # 构建命令 - 确保使用绝对路径
            abs_input_file = os.path.abspath(input_file)
            cmd = [executable_path, abs_input_file]
            
            if console:
                console.print(f"[blue]执行C++程序: {' '.join(cmd)}[/blue]")
            
            # 启动进程
            start_time = time.time()
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                env=env,
                cwd=kit_path
            )

            # 参考优化版本的处理方式：实时读取输出并写入文件
            output_lines = []
            error_lines = []
            processed_frames = 0
            all_frames_processed = False

            # 创建日志文件
            log_file_path = os.path.join(output_dir, "cpp_analysis.log")
            last_print_time = time.time()
            print_interval = 2  # 每2秒打印一次进度

            try:
                # 使用线程来处理超时和优雅终止
                def read_output():
                    nonlocal processed_frames, all_frames_processed, last_print_time
                    import re

                    try:
                        with open(log_file_path, 'w', encoding='utf-8') as log_file:
                            # 实时读取 stdout
                            for line in process.stdout:
                                output_lines.append(line)
                                log_file.write(line)
                                log_file.flush()  # 确保实时写入

                                # 检测帧处理模式：--------[X, timestamp]:path
                                frame_match = re.search(r'--------\[(\d+),', line)
                                if frame_match:
                                    frame_num = int(frame_match.group(1))
                                    processed_frames = max(processed_frames, frame_num + 1)  # 帧号从0开始

                                    # 间隔打印进度
                                    current_time = time.time()
                                    if console and (current_time - last_print_time >= print_interval):
                                        print(f"📊 处理进度: {processed_frames}/{total_frames} 帧")
                                        last_print_time = current_time

                                    # 检查是否处理完所有帧
                                    if total_frames > 0 and processed_frames >= total_frames:
                                        if console:
                                            print(f"✅ 检测到所有 {total_frames} 帧已处理完成")
                                        all_frames_processed = True
                                elif console and "algo sdk continue" in line:
                                    # 只在第一次看到continue时打印
                                    if "algo sdk continue" not in str(output_lines[-10:]):
                                        print("🔄 C++程序进入等待状态...")

                            # 实时读取 stderr
                            for line in process.stderr:
                                error_lines.append(line)
                                log_file.write(f"STDERR: {line}")
                                log_file.flush()
                    except Exception as e:
                        if console:
                            print(f"读取输出时出错: {e}")

                # 启动输出读取线程
                output_thread = threading.Thread(target=read_output)
                output_thread.start()

                # 智能等待：检测处理完成或超时
                check_interval = 1  # 每秒检查一次
                elapsed_time = 0

                while elapsed_time < timeout:
                    # 检查进程是否已经结束
                    if process.poll() is not None:
                        processing_time = time.time() - start_time
                        break

                    # 检查是否所有帧都处理完成
                    if all_frames_processed:
                        if console:
                            print(f"🎯 所有帧处理完成，等待3秒后优雅退出...")
                        time.sleep(3)  # 等待3秒确保后续处理完成

                        if console:
                            print(f"📤 发送SIGINT信号优雅终止C++程序...")
                        process.send_signal(signal.SIGINT)

                        # 等待程序响应SIGINT信号
                        try:
                            process.wait(timeout=10)  # 给10秒时间优雅退出
                            if console:
                                print("✅ C++程序已优雅退出")
                            processing_time = time.time() - start_time
                            break
                        except subprocess.TimeoutExpired:
                            # 如果还不退出，强制终止
                            if console:
                                print("⚠️ 强制终止C++程序")
                            process.kill()
                            process.wait()
                            processing_time = time.time() - start_time
                            break

                    time.sleep(check_interval)
                    elapsed_time += check_interval

                # 如果超时仍未完成，执行超时处理
                if elapsed_time >= timeout and process.poll() is None:
                    if console:
                        print(f"⚠️ C++程序执行超时({timeout}秒)，正在优雅终止...")

                    # 发送SIGINT信号优雅终止
                    process.send_signal(signal.SIGINT)

                    # 等待程序响应SIGINT信号
                    try:
                        process.wait(timeout=10)  # 给10秒时间优雅退出
                        if console:
                            print("✅ C++程序已优雅退出")
                    except subprocess.TimeoutExpired:
                        # 如果还不退出，强制终止
                        if console:
                            print("⚠️ 强制终止C++程序")
                        process.kill()
                        process.wait()

                    processing_time = time.time() - start_time
                    raise subprocess.TimeoutExpired(cmd, timeout)

                # 等待输出读取线程完成
                output_thread.join(timeout=5)

                stdout = ''.join(output_lines)
                stderr = ''.join(error_lines)

                # 无论成功失败都保存输出日志
                log_file = Path(output_dir) / "cpp_output.log"
                with open(log_file, 'w') as f:
                    f.write(f"=== C++程序执行日志 ===\n")
                    f.write(f"命令: {' '.join(cmd)}\n")
                    f.write(f"执行时间: {processing_time:.2f}秒\n")
                    f.write(f"返回码: {process.returncode}\n\n")
                    f.write("=== 标准输出 ===\n")
                    f.write(stdout)
                    f.write("\n=== 标准错误 ===\n")
                    f.write(stderr)

                if process.returncode == 0:
                    return True, f"处理成功，耗时 {processing_time:.2f}秒"
                else:
                    return False, f"C++程序返回错误码 {process.returncode}: {stderr}"

            except subprocess.TimeoutExpired:
                # 尝试获取已有的输出
                try:
                    stdout, stderr = process.communicate(timeout=1)
                except subprocess.TimeoutExpired:
                    stdout, stderr = "", ""

                process.kill()

                # 超时时也尝试保存日志
                log_file = Path(output_dir) / "cpp_output.log"
                with open(log_file, 'w') as f:
                    f.write(f"=== C++程序执行日志 ===\n")
                    f.write(f"命令: {' '.join(cmd)}\n")
                    f.write(f"执行时间: {timeout}秒（超时）\n")
                    f.write(f"状态: 超时被终止\n\n")
                    f.write("=== 标准输出（超时前） ===\n")
                    f.write(stdout if stdout else "无输出\n")
                    f.write("\n=== 标准错误（超时前） ===\n")
                    f.write(stderr if stderr else "无错误输出\n")
                    f.write("\n=== 错误信息 ===\n")
                    f.write("程序执行超时，被强制终止\n")
                return False, f"C++程序执行超时（{timeout}秒）"
                
        except Exception as e:
            return False, f"C++程序执行异常: {e}"
    
    def create_input_file(self, asset_metadata: Dict[str, Any],
                         frame_files: List[Path], output_dir: str,
                         asset_manager, asset_name: str) -> str:
        """创建C++程序的输入文件（使用预处理后的图像）

        Args:
            asset_metadata: 资产包元数据
            frame_files: 原始帧文件列表
            output_dir: 输出目录
            asset_manager: 资产包管理器实例
            asset_name: 资产包名称

        Returns:
            输入文件路径
        """
        # 显示溯源信息（仅用于日志）
        if asset_metadata and 'source_info' in asset_metadata:
            source_video = asset_metadata['source_info']['video_path']
            extraction_info = asset_metadata.get('extraction_info', {})
            time_range = extraction_info.get('time_range', {})
            start_time = time_range.get('start_time', '00:00:00')
            end_time = time_range.get('end_time', '99:99:99')
            print(f"📋 溯源信息: {source_video} ({start_time}-{end_time})")

        # 使用资产包管理器预处理图像
        print(f"🔄 预处理图像：裁剪和缩放...")
        try:
            processed_files, parseinfo_path = asset_manager.preprocess_frames(
                asset_name=asset_name,
                output_dir=output_dir,
                roi_region=[0, 0, 1280, 800],  # 给C++程序的裁剪尺寸
                target_size=[1280, 800]        # 给C++程序的目标尺寸
            )
            print(f"✅ 预处理完成，生成 {len(processed_files)} 个处理后图像")
            return parseinfo_path
        except Exception as e:
            print(f"❌ 图像预处理失败: {e}")
            raise

    def copy_cpp_files(self, source_path: str, kit_name: str) -> Tuple[bool, str]:
        """从源路径拷贝C++工具包文件

        Args:
            source_path: 源C++文件路径
            kit_name: 目标工具包名称（将被源文件夹名称覆盖）

        Returns:
            Tuple[bool, str]: (是否成功, 结果信息)
        """
        try:
            from rich.prompt import Confirm

            source_dir = Path(source_path)
            if not source_dir.exists():
                return False, f"源路径不存在: {source_path}"

            # 使用源文件夹的名称作为目标文件夹名称
            source_folder_name = source_dir.name
            target_dir = self.cpp_kit_manager.cpp_kits_dir / source_folder_name

            self.ui_manager.console.print(f"📁 将使用源文件夹名称: {source_folder_name}")
            self.ui_manager.console.print(f"📂 目标目录: {target_dir}")

            with ContextualUIManager(self.ui_manager, "processing", title="扫描源文件"):
                # 扫描源目录
                self.ui_manager.console.print(f"🔍 扫描源目录: {source_dir}")
                categorized_files = self.cpp_kit_copy_manager.scan_source_directory(source_dir)

                # 显示扫描结果
                self.cpp_kit_copy_manager.display_scan_results(categorized_files)

                # 检查必需文件
                if not self.cpp_kit_copy_manager.check_required_files(categorized_files):
                    if not Confirm.ask("未找到必需的可执行文件，是否继续拷贝？"):
                        return False, "用户取消了文件拷贝"

                # 检查文件冲突
                conflicts = self.cpp_kit_copy_manager.check_target_conflicts(categorized_files, target_dir)
                self.cpp_kit_copy_manager.display_conflicts(conflicts)

                overwrite = False
                if conflicts:
                    overwrite = Confirm.ask("发现文件冲突，是否覆盖现有文件？")
                    if not overwrite and not Confirm.ask("是否跳过冲突文件继续拷贝？"):
                        return False, "用户取消了文件拷贝"

            with ContextualUIManager(self.ui_manager, "processing", title="拷贝文件"):
                # 执行拷贝
                copy_success, copy_msg = self.cpp_kit_copy_manager.copy_files(
                    categorized_files, target_dir, overwrite
                )

                if not copy_success:
                    return False, copy_msg

                # 验证拷贝结果
                verify_success, verify_msg = self.cpp_kit_copy_manager.verify_copied_files(
                    categorized_files, target_dir
                )

                if not verify_success:
                    return False, f"文件验证失败: {verify_msg}"

                # 创建metadata.json文件
                metadata = {
                    "kit_info": {
                        "name": source_folder_name,
                        "version": "1.0.0",
                        "description": f"从 {source_path} 拷贝的C++工具包",
                        "created_at": time.strftime("%Y-%m-%dT%H:%M:%SZ")
                    },
                    "main_executable": "test_dms_internal_postmortem",
                    "system_requirements": {
                        "os": "Linux",
                        "arch": "x86_64",
                        "min_memory_mb": 1024
                    }
                }

                metadata_file = target_dir / "metadata.json"
                with open(metadata_file, 'w', encoding='utf-8') as f:
                    json.dump(metadata, f, indent=2, ensure_ascii=False)

                self.ui_manager.console.print(f"✅ 创建metadata.json文件: {metadata_file}")

            return True, f"成功拷贝C++工具包文件到 {source_folder_name}: {copy_msg}\n{verify_msg}"

        except Exception as e:
            return False, f"拷贝C++文件失败: {e}"

    def process_asset_package(self, asset_name: str, kit_name: str, skip_config_check: bool = False) -> Tuple[bool, str]:
        """处理单个帧资产包

        Args:
            asset_name: 资产包名称
            kit_name: C++工具包名称
            skip_config_check: 是否跳过配置文件检查

        Returns:
            (是否成功, 结果信息)
        """
        try:
            # 配置文件验证阶段
            with ContextualUIManager(self.ui_manager, "config"):
                from config_validator import ConfigValidator

                kit_dir = self.cpp_kit_manager.cpp_kits_dir / kit_name
                validator = ConfigValidator(str(kit_dir))

                config_success, config_msg = validator.validate_config_files_with_confirmation(skip_config_check)
                if not config_success:
                    return False, f"配置文件验证失败: {config_msg}"

                self.ui_manager.console.print(f"✅ {config_msg}")

            # 确保远端DMS服务正常运行
            self.ui_manager.display_message("🔍 检查远端DMS服务状态...", "info")
            service_success, service_msg = self.remote_service_manager.ensure_single_service()
            if not service_success:
                return False, f"远端服务管理失败: {service_msg}"
            self.ui_manager.display_message(f"✅ {service_msg}", "success")

            # 获取C++工具包
            kit_info = self.cpp_kit_manager.get_kit(kit_name)
            if not kit_info:
                return False, f"C++工具包不存在: {kit_name}"

            # 配置验证 - 智能检查和交互确认缺失文件
            if not skip_config_check:
                console.print(f"\n🔧 验证C++工具包配置: {kit_name}")
                validator = ConfigValidator(kit_info['path'])
                if not validator.full_validation():
                    return False, "配置验证失败，请检查配置文件"
            else:
                console.print(f"\n⏭️ 跳过C++工具包配置验证: {kit_name}")
            
            # 加载资产包
            try:
                console.print(f"🔍 正在加载资产包: {asset_name}")
                console.print(f"📁 资产管理器路径: {self.asset_manager.assets_dir}")

                # 检查资产包是否存在
                available_assets = self.asset_manager.list_asset_packages()
                console.print(f"📋 可用资产包: {available_assets}")

                if asset_name not in available_assets:
                    return False, f"资产包不存在: {asset_name}，可用资产包: {available_assets}"

                metadata, frame_files = self.asset_manager.load_asset_package(asset_name)
                console.print(f"✅ 资产包加载成功，帧数: {len(frame_files)}")
            except Exception as e:
                import traceback
                error_detail = traceback.format_exc()
                console.print(f"❌ 加载资产包详细错误:\n{error_detail}")
                return False, f"加载资产包失败: {e}"
            
            # 创建结果目录
            result_dir = self.results_dir / f"{asset_name}_{kit_name}_{int(time.time())}"
            result_dir.mkdir(parents=True, exist_ok=True)
            
            # 创建输入文件（使用预处理后的图像）
            input_file = self.create_input_file(metadata, frame_files, str(result_dir),
                                              self.asset_manager, asset_name)
            
            # 调用C++程序
            success, message = self.call_cpp_program(
                kit_info, input_file, str(result_dir),
                timeout=self.config.get('processing_timeout', 300),
                total_frames=len(frame_files)
            )
            
            if success:
                # 保存处理结果元数据
                result_metadata = {
                    "asset_info": metadata.get("asset_info", {}),
                    "kit_info": kit_info['metadata']['kit_info'],
                    "processing_info": {
                        "processed_at": time.strftime("%Y-%m-%d %H:%M:%S"),
                        "input_frames": len(frame_files),
                        "result_dir": str(result_dir),
                        "status": "success",
                        "message": message
                    }
                }
                
                result_metadata_file = result_dir / "result_metadata.json"
                with open(result_metadata_file, 'w') as f:
                    json.dump(result_metadata, f, indent=2, ensure_ascii=False)
                
                self.processed_count += 1

                # 执行渲染步骤
                if self.config.get('enable_rendering', True):
                    console.print(f"🎬 开始渲染视频: {asset_name} + {kit_name}")
                    render_success = self._render_video(result_dir, asset_name, kit_name)
                    if render_success:
                        console.print(f"✅ 视频渲染完成: {asset_name} + {kit_name}")
                    else:
                        console.print(f"⚠️ 视频渲染失败: {asset_name} + {kit_name}")

                # 只有在成功时才清理临时文件
                self.asset_manager.cleanup_processed_frames(str(result_dir))

                return True, f"处理成功: {result_dir}"
            else:
                self.failed_count += 1

                # 失败时保留所有临时文件用于调试，不进行清理
                print(f"⚠️ 处理失败，保留临时文件用于调试: {result_dir}")

                return False, message
                
        except Exception as e:
            self.failed_count += 1
            return False, f"处理异常: {e}"
    
    def batch_process(self, asset_names: List[str], kit_names: List[str],
                     max_workers: int = None, skip_config_check: bool = False) -> Dict[str, Any]:
        """批量处理多个资产包
        
        Args:
            asset_names: 资产包名称列表
            kit_names: C++工具包名称列表
            max_workers: 最大并发数
            
        Returns:
            处理结果统计
        """
        if max_workers is None:
            max_workers = self.resource_monitor.get_optimal_thread_count()
        
        # 生成所有处理任务
        tasks = []
        for asset_name in asset_names:
            for kit_name in kit_names:
                tasks.append((asset_name, kit_name))
        
        total_tasks = len(tasks)
        print(f"开始批量处理: {len(asset_names)}个资产包 × {len(kit_names)}个工具包 = {total_tasks}个任务")

        # 启动UI管理器进度模式
        self.ui_manager.start_progress_mode(total_tasks, "批量处理进度")

        results = []
        start_time = time.time()

        try:
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有任务
                future_to_task = {
                    executor.submit(self.process_asset_package, asset_name, kit_name, skip_config_check): (asset_name, kit_name)
                    for asset_name, kit_name in tasks
                }
                
                # 处理完成的任务
                for i, future in enumerate(as_completed(future_to_task)):
                    asset_name, kit_name = future_to_task[future]
                    
                    try:
                        success, message = future.result()
                        results.append({
                            'asset_name': asset_name,
                            'kit_name': kit_name,
                            'success': success,
                            'message': message
                        })
                        
                        # 更新进度
                        status_text = '成功' if success else '失败'
                        progress_message = f"{asset_name} + {kit_name}: {status_text}"
                        self.ui_manager.update_progress(1, progress_message)

                    except Exception as e:
                        results.append({
                            'asset_name': asset_name,
                            'kit_name': kit_name,
                            'success': False,
                            'message': f"任务异常: {e}"
                        })
                        self.ui_manager.update_progress(1, f"{asset_name} + {kit_name}: 异常")

        finally:
            self.ui_manager.stop_all()
        
        # 统计结果
        total_time = time.time() - start_time
        successful_tasks = sum(1 for r in results if r['success'])
        failed_tasks = len(results) - successful_tasks
        
        summary = {
            'total_tasks': total_tasks,
            'successful_tasks': successful_tasks,
            'failed_tasks': failed_tasks,
            'total_time': total_time,
            'average_time_per_task': total_time / total_tasks if total_tasks > 0 else 0,
            'results': results
        }
        
        return summary

    def _render_video(self, result_dir: Path, asset_name: str, kit_name: str) -> bool:
        """渲染视频 - 参考dms_postmortem_optimised.py的实现

        Args:
            result_dir: 结果目录
            asset_name: 资产包名称
            kit_name: 工具包名称

        Returns:
            是否成功
        """
        try:
            # 导入渲染模块 - 直接使用优化版本的渲染器
            import sys
            import cv2
            import json

            # 创建输出目录
            base_dir = Path(__file__).parent.parent  # dms_automation目录
            final_videos_dir = base_dir / "final_videos"
            final_videos_dir.mkdir(parents=True, exist_ok=True)

            # 生成输出文件名
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            output_filename = f"{asset_name}_{kit_name}_{timestamp}.mp4"
            output_path = final_videos_dir / output_filename

            # 获取视频参数 - 保持原图尺寸1920x1080
            fps = self.config.get('video_fps', 10.0)
            video_width = 1920
            video_height = 1080

            # 查找JSON文件目录 - C++程序输出的JSON文件位置
            kit_dir = base_dir / "cpp_kits" / kit_name
            json_dir = kit_dir / "output_json" / "processed_frames"

            if not json_dir.exists():
                console.print(f"❌ 找不到JSON输出目录: {json_dir}")
                return False

            # 查找原始图像文件 - 从assets目录读取完整原图
            original_frames_dir = Path(__file__).parent.parent / "assets" / asset_name / "frames"
            if not original_frames_dir.exists():
                console.print(f"❌ 找不到原始图像目录: {original_frames_dir}")
                return False

            # 获取所有JSON文件并排序
            json_files = list(json_dir.glob("*.json"))
            if not json_files:
                console.print(f"❌ 在 {json_dir} 中找不到JSON文件")
                return False

            # 按文件名排序
            json_files.sort(key=lambda x: x.name)

            console.print(f"🎬 开始渲染视频，共 {len(json_files)} 帧")
            console.print(f"📁 JSON目录: {json_dir}")
            console.print(f"📁 原始图像目录: {original_frames_dir}")

            # 创建视频写入器 - 参考优化版本的设置
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(str(output_path), fourcc, fps, (video_width, video_height))

            if not out.isOpened():
                console.print(f"❌ 无法创建视频写入器")
                return False

            # 逐帧处理并写入视频
            for i, json_file in enumerate(json_files):
                try:
                    # 构造对应的原始图像文件路径
                    frame_name = json_file.stem  # 例如 "000000"
                    # JSON文件名是000000格式，原始图像是000001格式，需要转换
                    frame_number = int(frame_name) + 1
                    original_frame_name = f"{frame_number:06d}"
                    image_file = original_frames_dir / f"{original_frame_name}.png"

                    if not image_file.exists():
                        console.print(f"⚠️ 找不到对应的原始图像文件: {image_file}")
                        continue

                    # 读取JSON数据
                    with open(json_file, 'r', encoding='utf-8') as f:
                        json_data = json.load(f)

                    # 读取图像
                    image = cv2.imread(str(image_file))
                    if image is None:
                        console.print(f"⚠️ 无法读取图像: {image_file}")
                        continue

                    # 直接在原图上绘制DMS信息
                    processed_image = self._draw_dms_info_on_image(image.copy(), json_data)

                    # 调整图像大小
                    resized_image = cv2.resize(processed_image, (video_width, video_height))

                    # 写入视频
                    out.write(resized_image)

                    # 显示进度
                    if (i + 1) % 5 == 0 or i == len(json_files) - 1:
                        console.print(f"📊 渲染进度: {i + 1}/{len(json_files)} 帧")

                except Exception as e:
                    console.print(f"⚠️ 处理帧 {json_file} 时出错: {e}")
                    continue

            # 释放资源
            out.release()

            if output_path.exists():
                console.print(f"📹 视频已保存: {output_path}")
                return True
            else:
                console.print(f"❌ 视频文件未生成")
                return False

        except Exception as e:
            console.print(f"❌ 渲染视频时出错: {e}")
            return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(
        description='DMS自动化分析与渲染平台 - 阶段二：C++批量处理',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python3 run_cpp_analysis.py --assets test_asset_001 --kits v1.0
  python3 run_cpp_analysis.py --assets test_asset_001,test_asset_002 --kits v1.0,v2.0 --max-workers 4
  python3 run_cpp_analysis.py --list-assets
  python3 run_cpp_analysis.py --list-kits
        """
    )
    
    parser.add_argument('--assets', help='资产包名称，多个用逗号分隔')
    parser.add_argument('--kits', help='C++工具包名称，多个用逗号分隔')
    parser.add_argument('--max-workers', type=int, help='最大并发数')
    parser.add_argument('--config', help='配置文件路径')
    parser.add_argument('--list-assets', action='store_true', help='列出所有可用的资产包')
    parser.add_argument('--list-kits', action='store_true', help='列出所有可用的C++工具包')
    parser.add_argument('--timeout', type=int, default=300, help='单个任务超时时间（秒）')
    parser.add_argument('--source-cpp-path', type=str,
                        help='源C++文件路径，用于自动拷贝工具包文件')
    parser.add_argument('--skip-config-check', action='store_true',
                        help='跳过配置文件检查与确认')
    parser.add_argument('--skip-rendering', action='store_true',
                        help='跳过视频渲染步骤')
    parser.add_argument('--video-fps', type=float, default=10.0,
                        help='渲染视频的帧率（默认：10.0）')
    parser.add_argument('--video-width', type=int, default=1280,
                        help='渲染视频的宽度（默认：1280）')
    parser.add_argument('--video-height', type=int, default=720,
                        help='渲染视频的高度（默认：720）')

    args = parser.parse_args()
    
    try:
        # 加载配置
        config = Config(args.config)
        config.config['processing_timeout'] = args.timeout
        config.config['enable_rendering'] = not args.skip_rendering
        config.config['video_fps'] = args.video_fps
        config.config['video_width'] = args.video_width
        config.config['video_height'] = args.video_height
        
        # 初始化管理器 - 使用绝对路径避免多线程环境中的路径问题
        script_dir = Path(__file__).parent
        cpp_kit_manager = CppKitManager(script_dir / "../cpp_kits")
        asset_manager = AssetPackageManager(script_dir / "../assets")
        
        # 列出资产包
        if args.list_assets:
            assets = asset_manager.list_asset_packages()
            if console:
                console.print(f"[blue]可用的资产包 ({len(assets)}个):[/blue]")
                for asset in assets:
                    console.print(f"  - {asset}")
            else:
                print(f"可用的资产包 ({len(assets)}个):")
                for asset in assets:
                    print(f"  - {asset}")
            return 0
        
        # 列出C++工具包
        if args.list_kits:
            kits = cpp_kit_manager.list_kits()
            if console:
                console.print(f"[blue]可用的C++工具包 ({len(kits)}个):[/blue]")
                for kit in kits:
                    console.print(f"  - {kit}")
            else:
                print(f"可用的C++工具包 ({len(kits)}个):")
                for kit in kits:
                    print(f"  - {kit}")
            return 0
        
        # 检查必需参数
        if not args.assets or not args.kits:
            parser.print_help()
            return 1
        
        # 解析参数
        asset_names = [name.strip() for name in args.assets.split(',')]
        kit_names = [name.strip() for name in args.kits.split(',')]
        
        # 验证资产包和工具包存在
        available_assets = asset_manager.list_asset_packages()
        available_kits = cpp_kit_manager.list_kits()
        
        for asset_name in asset_names:
            if asset_name not in available_assets:
                if console:
                    console.print(f"[red]错误: 资产包不存在: {asset_name}[/red]")
                else:
                    print(f"错误: 资产包不存在: {asset_name}")
                return 1
        
        for kit_name in kit_names:
            if kit_name not in available_kits:
                if console:
                    console.print(f"[red]错误: C++工具包不存在: {kit_name}[/red]")
                else:
                    print(f"错误: C++工具包不存在: {kit_name}")
                return 1
        
        # 初始化处理器
        processor = CppAnalysisProcessor(config, cpp_kit_manager, asset_manager)

        # 处理C++文件拷贝（如果指定了源路径）
        if args.source_cpp_path:
            print(f"🔄 开始拷贝C++工具包文件...")
            # 拷贝文件并获取新的工具包名称
            source_dir = Path(args.source_cpp_path)
            new_kit_name = source_dir.name

            copy_success, copy_msg = processor.copy_cpp_files(args.source_cpp_path, kit_names[0])
            if copy_success:
                print(f"✅ {copy_msg}")
                # 更新工具包名称列表
                kit_names = [new_kit_name]
                print(f"📋 更新工具包名称为: {kit_names}")

                # 重新扫描工具包目录
                print(f"🔄 重新扫描C++工具包目录...")
                cpp_kit_manager._load_available_kits()
                print(f"✅ 工具包目录扫描完成")
            else:
                print(f"❌ 拷贝失败: {copy_msg}")
                return 1

        # 开始批量处理
        print("开始批量处理...")

        summary = processor.batch_process(asset_names, kit_names, args.max_workers, args.skip_config_check)
        
        # 显示结果
        if console:
            console.print(f"\n[green]批量处理完成！[/green]")
            console.print(f"[blue]总任务数: {summary['total_tasks']}[/blue]")
            console.print(f"[green]成功: {summary['successful_tasks']}[/green]")
            console.print(f"[red]失败: {summary['failed_tasks']}[/red]")
            console.print(f"[blue]总耗时: {summary['total_time']:.2f}秒[/blue]")
            console.print(f"[blue]平均耗时: {summary['average_time_per_task']:.2f}秒/任务[/blue]")
        else:
            print(f"\n批量处理完成！")
            print(f"总任务数: {summary['total_tasks']}")
            print(f"成功: {summary['successful_tasks']}")
            print(f"失败: {summary['failed_tasks']}")
            print(f"总耗时: {summary['total_time']:.2f}秒")
            print(f"平均耗时: {summary['average_time_per_task']:.2f}秒/任务")
        
        # 保存处理报告
        report_file = f"results/batch_processing_report_{int(time.time())}.json"
        with open(report_file, 'w') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        if console:
            console.print(f"[blue]处理报告已保存: {report_file}[/blue]")
        else:
            print(f"处理报告已保存: {report_file}")
        
        return 0 if summary['failed_tasks'] == 0 else 1
        
    except KeyboardInterrupt:
        if console:
            console.print("\n[yellow]用户中断处理[/yellow]")
        else:
            print("\n用户中断处理")
        return 130
    except Exception as e:
        if console:
            console.print(f"\n[red]程序异常: {e}[/red]")
        else:
            print(f"\n程序异常: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
