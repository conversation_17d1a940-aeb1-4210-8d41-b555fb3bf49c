#!/usr/bin/env python3
"""
DMS自动化分析与渲染平台 - 阶段三测试套件

测试阶段三的渲染功能，包括单版本渲染和多版本对比渲染

作者: DMS Automation Platform
创建时间: 2025-07-10
"""

import os
import sys
import json
import subprocess
import time
from pathlib import Path
from typing import List, Dict, Any
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

console = Console()

class Stage3Tester:
    """阶段三测试器"""
    
    def __init__(self):
        self.test_results = []
        self.scripts_dir = Path("scripts")
        self.results_dir = Path("results")
        self.rendered_videos_dir = Path("rendered_videos")
        
        # 确保输出目录存在
        self.rendered_videos_dir.mkdir(exist_ok=True)
    
    def run_test(self, test_name: str, test_func) -> bool:
        """运行单个测试"""
        console.print(f"\n=== 测试{test_name} ===")
        
        try:
            start_time = time.time()
            result = test_func()
            end_time = time.time()
            
            if result:
                console.print(f"✅ {test_name} 通过")
                self.test_results.append({
                    'name': test_name,
                    'status': 'PASS',
                    'duration': end_time - start_time,
                    'message': '测试通过'
                })
                return True
            else:
                console.print(f"❌ {test_name} 失败")
                self.test_results.append({
                    'name': test_name,
                    'status': 'FAIL',
                    'duration': end_time - start_time,
                    'message': '测试失败'
                })
                return False
                
        except Exception as e:
            console.print(f"❌ {test_name} 异常: {e}")
            self.test_results.append({
                'name': test_name,
                'status': 'ERROR',
                'duration': 0,
                'message': str(e)
            })
            return False
    
    def test_visualization_utils(self) -> bool:
        """测试可视化工具模块"""
        try:
            # 导入并测试可视化工具
            sys.path.insert(0, str(self.scripts_dir))
            from visualization_utils import create_visualization_utils
            
            viz_utils = create_visualization_utils()
            
            # 创建测试图像
            import numpy as np
            test_image = np.zeros((720, 1280, 3), dtype=np.uint8)
            
            # 测试数据
            test_data = {
                'frame_id': 1,
                'timestamp': 0.1,
                'face_detected': True,
                'eye_openness': 0.8,
                'gaze_direction': {'x': 0.1, 'y': -0.05},
                'distraction_score': 0.3
            }
            
            # 处理测试图像
            result = viz_utils.process_frame_simple(test_image, test_data)
            
            # 验证结果
            if result is not None and result.shape == (720, 1280, 3):
                console.print("   ✓ 可视化工具模块正常工作")
                return True
            else:
                console.print("   ✗ 可视化工具模块输出异常")
                return False
                
        except Exception as e:
            console.print(f"   ✗ 可视化工具模块测试失败: {e}")
            return False
    
    def test_single_video_rendering(self) -> bool:
        """测试单版本视频渲染"""
        try:
            # 查找可用的结果目录
            result_dirs = [d for d in self.results_dir.iterdir() 
                          if d.is_dir() and d.name.startswith('test_small_001')]
            
            if not result_dirs:
                console.print("   ✗ 没有找到测试结果目录")
                return False
            
            test_result_dir = result_dirs[0]
            output_path = self.rendered_videos_dir / "test_single_render.mp4"
            
            # 运行渲染命令
            cmd = [
                "python3", str(self.scripts_dir / "render_video.py"),
                str(test_result_dir),
                "-o", str(output_path),
                "--fps", "5.0"  # 使用较低帧率加快测试
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0 and output_path.exists():
                file_size = output_path.stat().st_size
                console.print(f"   ✓ 单版本视频渲染成功，文件大小: {file_size / 1024 / 1024:.2f} MB")
                return True
            else:
                console.print(f"   ✗ 单版本视频渲染失败: {result.stderr}")
                return False
                
        except Exception as e:
            console.print(f"   ✗ 单版本视频渲染测试失败: {e}")
            return False
    
    def test_comparison_video_rendering(self) -> bool:
        """测试对比视频渲染"""
        try:
            # 查找可用的结果目录
            result_dirs = [d for d in self.results_dir.iterdir() 
                          if d.is_dir() and d.name.startswith('test_small_001')]
            
            if len(result_dirs) < 2:
                console.print("   ✗ 需要至少2个结果目录进行对比测试")
                return False
            
            # 使用前两个结果目录
            test_dirs = result_dirs[:2]
            output_path = self.rendered_videos_dir / "test_comparison_render.mp4"
            
            # 运行对比渲染命令
            cmd = [
                "python3", str(self.scripts_dir / "create_comparison.py"),
                str(test_dirs[0]), str(test_dirs[1]),
                "-o", str(output_path),
                "--fps", "5.0"  # 使用较低帧率加快测试
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0 and output_path.exists():
                file_size = output_path.stat().st_size
                console.print(f"   ✓ 对比视频渲染成功，文件大小: {file_size / 1024 / 1024:.2f} MB")
                return True
            else:
                console.print(f"   ✗ 对比视频渲染失败: {result.stderr}")
                return False
                
        except Exception as e:
            console.print(f"   ✗ 对比视频渲染测试失败: {e}")
            return False
    
    def test_end_to_end_workflow(self) -> bool:
        """测试端到端工作流程"""
        try:
            # 验证阶段一到阶段三的完整流程
            
            # 1. 检查资产包
            assets_dir = Path("assets")
            asset_packages = [d for d in assets_dir.iterdir() if d.is_dir()]
            
            if not asset_packages:
                console.print("   ✗ 没有找到资产包")
                return False
            
            console.print(f"   ✓ 发现 {len(asset_packages)} 个资产包")
            
            # 2. 检查阶段二结果
            result_dirs = [d for d in self.results_dir.iterdir() if d.is_dir()]
            
            if not result_dirs:
                console.print("   ✗ 没有找到阶段二结果")
                return False
            
            console.print(f"   ✓ 发现 {len(result_dirs)} 个处理结果")
            
            # 3. 检查渲染输出
            rendered_videos = list(self.rendered_videos_dir.glob("*.mp4"))
            
            if not rendered_videos:
                console.print("   ✗ 没有找到渲染视频")
                return False
            
            console.print(f"   ✓ 发现 {len(rendered_videos)} 个渲染视频")
            
            # 4. 验证数据一致性
            for result_dir in result_dirs[:1]:  # 只检查第一个
                metadata_file = result_dir / "result_metadata.json"
                analysis_file = result_dir / "analysis_result.json"
                
                if not metadata_file.exists() or not analysis_file.exists():
                    console.print(f"   ✗ 结果目录 {result_dir.name} 缺少必要文件")
                    return False
                
                # 验证JSON格式
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                
                with open(analysis_file, 'r') as f:
                    analysis = json.load(f)
                
                if 'analysis_results' not in analysis:
                    console.print(f"   ✗ 分析结果格式异常")
                    return False
            
            console.print("   ✓ 端到端工作流程验证通过")
            return True
            
        except Exception as e:
            console.print(f"   ✗ 端到端工作流程测试失败: {e}")
            return False
    
    def print_summary(self):
        """打印测试总结"""
        console.print("\n" + "="*50)
        console.print("DMS自动化分析与渲染平台 - 阶段三测试")
        console.print("="*50)
        
        # 创建结果表格
        table = Table(title="测试结果总结")
        table.add_column("测试项目", style="cyan")
        table.add_column("状态", style="magenta")
        table.add_column("耗时(秒)", style="green")
        table.add_column("备注", style="yellow")
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r['status'] == 'PASS')
        
        for result in self.test_results:
            status_icon = "✅" if result['status'] == 'PASS' else "❌"
            status_text = f"{status_icon} {result['status']}"
            duration_text = f"{result['duration']:.2f}"
            
            table.add_row(
                result['name'],
                status_text,
                duration_text,
                result['message']
            )
        
        console.print(table)
        
        # 总结信息
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        summary_panel = Panel(
            f"测试总结: {passed_tests}/{total_tests} 通过\n"
            f"成功率: {success_rate:.1f}%\n"
            f"{'🎉 所有测试通过！阶段三核心功能正常' if passed_tests == total_tests else '⚠️ 部分测试失败，需要检查'}",
            title="阶段三测试完成",
            border_style="green" if passed_tests == total_tests else "red"
        )
        
        console.print(summary_panel)
        
        return passed_tests == total_tests


def main():
    """主函数"""
    console.print(Panel(
        "DMS自动化分析与渲染平台 - 阶段三测试套件\n"
        "测试单版本渲染和多版本对比渲染功能",
        title="阶段三功能测试",
        border_style="blue"
    ))
    
    tester = Stage3Tester()
    
    # 运行所有测试
    tests = [
        ("可视化工具模块", tester.test_visualization_utils),
        ("单版本视频渲染", tester.test_single_video_rendering),
        ("对比视频渲染", tester.test_comparison_video_rendering),
        ("端到端工作流程", tester.test_end_to_end_workflow),
    ]
    
    all_passed = True
    for test_name, test_func in tests:
        if not tester.run_test(test_name, test_func):
            all_passed = False
    
    # 打印总结
    final_result = tester.print_summary()
    
    if final_result:
        console.print("\n🎉 所有测试通过！阶段三核心功能正常")
        sys.exit(0)
    else:
        console.print("\n❌ 部分测试失败，请检查相关功能")
        sys.exit(1)


if __name__ == "__main__":
    main()
