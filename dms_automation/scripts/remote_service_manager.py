#!/usr/bin/env python3
"""
远端DMS服务管理器
负责智能管理远端DMS服务的启动、停止和状态检查
"""

import subprocess
import time
from typing import Tuple, Optional, List
from pathlib import Path


class RemoteServiceManager:
    """远端DMS服务管理器"""
    
    def __init__(self, remote_host: str = "***********", remote_user: str = "root", 
                 remote_password: str = "root", service_path: str = "/userfs"):
        self.remote_host = remote_host
        self.remote_user = remote_user
        self.remote_password = remote_password
        self.service_path = service_path
        self.service_name = "tx_dms_oax_test_tool_update"
        
    def _run_remote_command(self, command: str, timeout: int = 10) -> Tuple[bool, str]:
        """执行远程命令"""
        try:
            full_command = [
                "sshpass", "-p", self.remote_password,
                "ssh", "-o", "ConnectTimeout=10", "-o", "StrictHostKeyChecking=no",
                f"{self.remote_user}@{self.remote_host}",
                command
            ]
            
            result = subprocess.run(
                full_command,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            
            return result.returncode == 0, result.stdout.strip()
            
        except subprocess.TimeoutExpired:
            return False, "命令执行超时"
        except Exception as e:
            return False, f"执行命令失败: {e}"
    
    def check_service_status(self) -> Tuple[bool, List[str]]:
        """检查DMS服务状态

        Returns:
            Tuple[bool, List[str]]: (是否有服务运行, 进程ID列表)
        """
        # 使用ps命令并直接提取PID
        success, output = self._run_remote_command(
            f"ps | grep {self.service_name} | grep -v grep | awk '{{print $1}}'"
        )

        if not success or not output:
            return False, []

        # 解析进程ID
        pids = []
        for line in output.split('\n'):
            line = line.strip()
            if line and line.isdigit():
                pids.append(line)

        return len(pids) > 0, pids
    
    def start_service(self, force_restart: bool = False) -> Tuple[bool, str]:
        """启动DMS服务
        
        Args:
            force_restart: 是否强制重启（先停止所有现有服务）
            
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        # 检查当前状态
        is_running, pids = self.check_service_status()
        
        if is_running and not force_restart:
            return True, f"DMS服务已在运行 (PID: {', '.join(pids)})"
        
        # 如果需要重启，先停止所有服务
        if force_restart and is_running:
            stop_success, stop_msg = self.stop_service()
            if not stop_success:
                return False, f"停止现有服务失败: {stop_msg}"
            time.sleep(2)  # 等待服务完全停止
        
        # 启动新服务
        start_command = f"cd {self.service_path} && nohup ./{self.service_name} > /dev/null 2>&1 &"
        success, output = self._run_remote_command(start_command, timeout=5)

        # 启动命令本身不需要等待返回结果
        # if not success:
        #     return False, f"启动服务失败: {output}"
        
        # 等待服务启动
        time.sleep(3)
        
        # 验证服务是否成功启动
        is_running, new_pids = self.check_service_status()
        if is_running:
            return True, f"DMS服务启动成功 (PID: {', '.join(new_pids)})"
        else:
            return False, "服务启动失败，未检测到运行的进程"
    
    def stop_service(self) -> Tuple[bool, str]:
        """停止DMS服务
        
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        is_running, pids = self.check_service_status()
        
        if not is_running:
            return True, "DMS服务未运行"
        
        # 停止所有DMS进程
        for pid in pids:
            success, output = self._run_remote_command(f"kill {pid}")
            if not success:
                # 如果普通kill失败，尝试强制kill
                self._run_remote_command(f"kill -9 {pid}")
        
        # 等待进程停止
        time.sleep(2)
        
        # 验证是否成功停止
        is_running, remaining_pids = self.check_service_status()
        if not is_running:
            return True, f"DMS服务已停止 (停止了 {len(pids)} 个进程)"
        else:
            return False, f"部分进程仍在运行: {', '.join(remaining_pids)}"
    
    def ensure_single_service(self) -> Tuple[bool, str]:
        """确保只有一个DMS服务在运行
        
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        is_running, pids = self.check_service_status()
        
        if not is_running:
            # 没有服务运行，启动一个
            return self.start_service()
        elif len(pids) == 1:
            # 只有一个服务运行，正常
            return True, f"DMS服务正常运行 (PID: {pids[0]})"
        else:
            # 多个服务运行，需要清理
            print(f"⚠️ 检测到多个DMS服务运行: {', '.join(pids)}")
            
            # 停止所有服务
            stop_success, stop_msg = self.stop_service()
            if not stop_success:
                return False, f"清理多余服务失败: {stop_msg}"
            
            # 启动单个服务
            return self.start_service()


def main():
    """测试远端服务管理器"""
    manager = RemoteServiceManager()
    
    print("🔍 检查DMS服务状态...")
    is_running, pids = manager.check_service_status()
    print(f"服务状态: {'运行中' if is_running else '未运行'}")
    if is_running:
        print(f"进程ID: {', '.join(pids)}")
    
    print("\n🎯 确保单一服务运行...")
    success, message = manager.ensure_single_service()
    print(f"结果: {message}")


if __name__ == "__main__":
    main()
