#!/usr/bin/env python3
"""
DMS自动化分析与渲染平台 - 多版本对比视频生成模块

基于多个阶段二的分析结果，生成对比视频

作者: DMS Automation Platform
创建时间: 2025-07-10
"""

import os
import sys
import json
import cv2
import argparse
import numpy as np
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from rich.console import Console
from rich.progress import Progress, TaskID
import time

# 添加脚本目录到路径，以便导入其他模块
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from visualization_utils import create_visualization_utils
from render_video import VideoRenderer

console = Console()

# 设置日志
def setup_logging(name: str) -> logging.Logger:
    """设置日志记录"""
    logger = logging.getLogger(name)
    logger.setLevel(logging.INFO)
    
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    
    return logger

logger = setup_logging("create_comparison")


class ComparisonRenderer:
    """对比视频渲染器类，负责生成多版本对比视频"""
    
    def __init__(self, result_dirs: List[str]):
        """
        初始化对比渲染器
        
        Args:
            result_dirs: 多个阶段二结果目录路径列表
        """
        self.result_dirs = [Path(d) for d in result_dirs]
        self.renderers = []
        
        # 验证所有结果目录并创建渲染器
        for result_dir in self.result_dirs:
            if not result_dir.exists():
                raise FileNotFoundError(f"结果目录不存在: {result_dir}")
            
            renderer = VideoRenderer(str(result_dir))
            self.renderers.append(renderer)
        
        # 验证所有版本使用相同的资产包
        asset_names = [r.asset_info['name'] for r in self.renderers]
        if len(set(asset_names)) > 1:
            raise ValueError(f"所有版本必须使用相同的资产包，发现: {asset_names}")
        
        self.asset_name = asset_names[0]
        logger.info(f"初始化对比渲染器: {len(self.renderers)} 个版本")
        logger.info(f"资产包: {self.asset_name}")
    
    def _get_version_info(self) -> List[Dict[str, str]]:
        """获取版本信息"""
        version_info = []
        for i, renderer in enumerate(self.renderers):
            kit_name = renderer.metadata['kit_info']['name']
            version_info.append({
                'index': i,
                'kit_name': kit_name,
                'label': f"v{kit_name}",
                'result_dir': str(renderer.result_dir)
            })
        return version_info
    
    def _create_side_by_side_layout(self, frames: List[np.ndarray], 
                                   version_info: List[Dict[str, str]],
                                   target_size: Tuple[int, int] = (1280, 720)) -> np.ndarray:
        """
        创建并排布局
        
        Args:
            frames: 各版本的处理后帧列表
            version_info: 版本信息
            target_size: 目标尺寸
            
        Returns:
            合成的对比帧
        """
        num_versions = len(frames)
        
        if num_versions == 1:
            # 单版本，直接返回
            return cv2.resize(frames[0], target_size)
        elif num_versions == 2:
            # 两版本并排
            frame_width = target_size[0] // 2
            frame_height = target_size[1]
        elif num_versions <= 4:
            # 四版本网格 (2x2)
            frame_width = target_size[0] // 2
            frame_height = target_size[1] // 2
        else:
            # 更多版本，使用3x3网格
            frame_width = target_size[0] // 3
            frame_height = target_size[1] // 3
        
        # 调整所有帧到统一尺寸
        resized_frames = []
        for frame in frames:
            resized_frame = cv2.resize(frame, (frame_width, frame_height))
            resized_frames.append(resized_frame)
        
        # 创建合成画布
        canvas = np.zeros((target_size[1], target_size[0], 3), dtype=np.uint8)
        
        # 布局帧
        if num_versions == 2:
            # 左右布局
            canvas[:, :frame_width] = resized_frames[0]
            canvas[:, frame_width:] = resized_frames[1]
        elif num_versions <= 4:
            # 2x2网格布局
            positions = [(0, 0), (0, 1), (1, 0), (1, 1)]
            for i, frame in enumerate(resized_frames):
                if i < len(positions):
                    row, col = positions[i]
                    y_start = row * frame_height
                    y_end = y_start + frame_height
                    x_start = col * frame_width
                    x_end = x_start + frame_width
                    canvas[y_start:y_end, x_start:x_end] = frame
        else:
            # 3x3网格布局
            for i, frame in enumerate(resized_frames[:9]):  # 最多9个版本
                row = i // 3
                col = i % 3
                y_start = row * frame_height
                y_end = y_start + frame_height
                x_start = col * frame_width
                x_end = x_start + frame_width
                canvas[y_start:y_end, x_start:x_end] = frame
        
        # 添加版本标签
        self._add_version_labels(canvas, version_info, num_versions, 
                               frame_width, frame_height, target_size)
        
        return canvas
    
    def _add_version_labels(self, canvas: np.ndarray, version_info: List[Dict[str, str]],
                           num_versions: int, frame_width: int, frame_height: int,
                           target_size: Tuple[int, int]) -> None:
        """在画布上添加版本标签"""
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.7
        color = (255, 255, 255)
        thickness = 2
        
        if num_versions == 2:
            positions = [(10, 30), (frame_width + 10, 30)]
        elif num_versions <= 4:
            positions = [(10, 30), (frame_width + 10, 30),
                        (10, frame_height + 30), (frame_width + 10, frame_height + 30)]
        else:
            positions = []
            for i in range(min(9, num_versions)):
                row = i // 3
                col = i % 3
                x = col * frame_width + 10
                y = row * frame_height + 30
                positions.append((x, y))
        
        for i, info in enumerate(version_info):
            if i < len(positions):
                label = info['label']
                cv2.putText(canvas, label, positions[i], font, font_scale, color, thickness)
    
    def render_comparison_video(self, output_path: str, fps: float = 10.0,
                              video_size: Tuple[int, int] = (1280, 720)) -> bool:
        """
        渲染对比视频
        
        Args:
            output_path: 输出视频路径
            fps: 视频帧率
            video_size: 视频尺寸
            
        Returns:
            是否成功
        """
        try:
            # 创建输出目录
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 初始化视频编写器
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            video_writer = cv2.VideoWriter(output_path, fourcc, fps, video_size)
            
            if not video_writer.isOpened():
                logger.error(f"无法创建视频文件: {output_path}")
                return False
            
            # 获取版本信息
            version_info = self._get_version_info()
            
            # 确定总帧数（使用最小帧数确保同步）
            frame_counts = [len(r.analysis_results) for r in self.renderers]
            total_frames = min(frame_counts)
            logger.info(f"开始渲染对比视频: {total_frames} 帧, {len(self.renderers)} 个版本")
            
            # 使用进度条显示渲染进度
            with Progress() as progress:
                task = progress.add_task("[green]渲染对比视频...", total=total_frames)
                
                for frame_idx in range(total_frames):
                    # 为每个版本生成当前帧
                    version_frames = []
                    
                    for renderer in self.renderers:
                        analysis_data = renderer.analysis_results[frame_idx]
                        
                        # 加载原始帧图像
                        frame_image = renderer._load_frame_image(analysis_data['frame_id'])
                        
                        if frame_image is None:
                            # 如果无法加载图像，创建黑色帧
                            frame_image = np.zeros((720, 1280, 3), dtype=np.uint8)
                        
                        # 使用可视化工具处理帧
                        processed_frame = renderer.viz_utils.process_frame_simple(
                            frame_image, analysis_data)
                        
                        version_frames.append(processed_frame)
                    
                    # 创建对比布局
                    comparison_frame = self._create_side_by_side_layout(
                        version_frames, version_info, video_size)
                    
                    # 写入视频
                    video_writer.write(comparison_frame)
                    
                    # 更新进度
                    progress.update(task, advance=1)
            
            # 释放资源
            video_writer.release()
            
            # 验证输出文件
            if not Path(output_path).exists():
                logger.error("对比视频文件创建失败")
                return False
            
            file_size = Path(output_path).stat().st_size
            logger.info(f"✅ 对比视频渲染完成: {output_path}")
            logger.info(f"   文件大小: {file_size / 1024 / 1024:.2f} MB")
            logger.info(f"   总帧数: {total_frames}")
            logger.info(f"   版本数: {len(self.renderers)}")
            logger.info(f"   帧率: {fps} FPS")
            logger.info(f"   分辨率: {video_size[0]}x{video_size[1]}")
            
            return True
            
        except Exception as e:
            logger.error(f"对比视频渲染失败: {e}")
            return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="DMS分析结果对比视频生成工具")
    parser.add_argument("result_dirs", nargs='+', help="多个阶段二结果目录路径")
    parser.add_argument("-o", "--output", help="输出视频路径", 
                       default="rendered_videos/comparison.mp4")
    parser.add_argument("--fps", type=float, default=10.0, help="视频帧率 (默认: 10.0)")
    parser.add_argument("--width", type=int, default=1280, help="视频宽度 (默认: 1280)")
    parser.add_argument("--height", type=int, default=720, help="视频高度 (默认: 720)")
    
    args = parser.parse_args()
    
    try:
        console.print(f"[bold green]DMS对比视频生成工具[/bold green]")
        console.print(f"结果目录: {args.result_dirs}")
        console.print(f"输出路径: {args.output}")
        
        # 创建对比渲染器
        renderer = ComparisonRenderer(args.result_dirs)
        
        # 渲染对比视频
        success = renderer.render_comparison_video(
            output_path=args.output,
            fps=args.fps,
            video_size=(args.width, args.height)
        )
        
        if success:
            console.print(f"[bold green]✅ 对比视频渲染成功![/bold green]")
            console.print(f"输出文件: {args.output}")
        else:
            console.print(f"[bold red]❌ 对比视频渲染失败![/bold red]")
            sys.exit(1)
            
    except Exception as e:
        console.print(f"[bold red]错误: {e}[/bold red]")
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
