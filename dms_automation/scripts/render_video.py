#!/usr/bin/env python3
"""
DMS自动化分析与渲染平台 - 单版本视频渲染模块

基于阶段二的分析结果，生成带有可视化标注的视频

作者: DMS Automation Platform
创建时间: 2025-07-10
"""

import os
import sys
import json
import cv2
import argparse
import numpy as np
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from rich.console import Console
from rich.progress import Progress, TaskID
import time

# 添加脚本目录到路径，以便导入其他模块
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from visualization_utils import create_visualization_utils

console = Console()

# 设置日志
def setup_logging(name: str) -> logging.Logger:
    """设置日志记录"""
    logger = logging.getLogger(name)
    logger.setLevel(logging.INFO)

    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)

    return logger

def validate_file_exists(file_path: Path) -> None:
    """验证文件是否存在"""
    if not file_path.exists():
        raise FileNotFoundError(f"文件不存在: {file_path}")

logger = setup_logging("render_video")


class VideoRenderer:
    """视频渲染器类，负责将分析结果渲染为可视化视频"""
    
    def __init__(self, result_dir: str):
        """
        初始化视频渲染器
        
        Args:
            result_dir: 阶段二的结果目录路径
        """
        self.result_dir = Path(result_dir)
        self.viz_utils = create_visualization_utils()
        
        # 验证结果目录
        if not self.result_dir.exists():
            raise FileNotFoundError(f"结果目录不存在: {result_dir}")
        
        # 加载元数据和分析结果
        self.metadata = self._load_metadata()
        self.analysis_results = self._load_analysis_results()
        self.asset_info = self._get_asset_info()
        
        logger.info(f"初始化视频渲染器: {result_dir}")
        logger.info(f"资产包: {self.asset_info['name']}, 帧数: {len(self.analysis_results)}")
    
    def _load_metadata(self) -> Dict[str, Any]:
        """加载结果元数据"""
        metadata_file = self.result_dir / "result_metadata.json"
        validate_file_exists(metadata_file)
        
        with open(metadata_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def _load_analysis_results(self) -> List[Dict[str, Any]]:
        """加载分析结果数据"""
        result_file = self.result_dir / "analysis_result.json"
        validate_file_exists(result_file)
        
        with open(result_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            return data.get('analysis_results', [])
    
    def _get_asset_info(self) -> Dict[str, Any]:
        """获取资产包信息"""
        asset_name = self.metadata['asset_info']['name']
        asset_dir = Path("assets") / asset_name
        
        if not asset_dir.exists():
            raise FileNotFoundError(f"资产包目录不存在: {asset_dir}")
        
        # 加载资产包元数据
        asset_metadata_file = asset_dir / "metadata.json"
        validate_file_exists(asset_metadata_file)
        
        with open(asset_metadata_file, 'r', encoding='utf-8') as f:
            asset_metadata = json.load(f)
        
        return {
            'name': asset_name,
            'dir': asset_dir,
            'metadata': asset_metadata,
            'frames_dir': asset_dir / "frames"
        }
    
    def _load_frame_image(self, frame_index: int) -> Optional[np.ndarray]:
        """
        加载指定索引的帧图像

        Args:
            frame_index: 帧索引

        Returns:
            图像数组，如果加载失败返回None
        """
        try:
            # 根据资产包的帧列表获取文件名
            frame_list = self.asset_info['metadata']['frame_info']['frame_list']

            # 如果frame_index超出实际帧数，使用循环索引或最后一帧
            if frame_index >= len(frame_list):
                # 使用循环索引，确保始终有图像可用
                actual_index = frame_index % len(frame_list)
                logger.debug(f"帧索引 {frame_index} 超出范围，使用循环索引 {actual_index}")
            else:
                actual_index = frame_index

            frame_filename = frame_list[actual_index]['filename']
            frame_path = self.asset_info['frames_dir'] / frame_filename

            if not frame_path.exists():
                logger.warning(f"帧文件不存在: {frame_path}")
                return None

            # 加载图像
            image = cv2.imread(str(frame_path))
            if image is None:
                logger.warning(f"无法加载图像: {frame_path}")
                return None

            return image

        except Exception as e:
            logger.error(f"加载帧图像失败 (索引: {frame_index}): {e}")
            return None
    
    def render_video(self, output_path: str, fps: float = 10.0, 
                    video_size: Tuple[int, int] = (1280, 720)) -> bool:
        """
        渲染视频
        
        Args:
            output_path: 输出视频路径
            fps: 视频帧率
            video_size: 视频尺寸
            
        Returns:
            是否成功
        """
        try:
            # 创建输出目录
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 初始化视频编写器
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            video_writer = cv2.VideoWriter(output_path, fourcc, fps, video_size)
            
            if not video_writer.isOpened():
                logger.error(f"无法创建视频文件: {output_path}")
                return False
            
            total_frames = len(self.analysis_results)
            logger.info(f"开始渲染视频: {total_frames} 帧")
            
            # 使用进度条显示渲染进度
            with Progress() as progress:
                task = progress.add_task("[green]渲染视频...", total=total_frames)
                
                for i, analysis_data in enumerate(self.analysis_results):
                    # 加载原始帧图像
                    frame_image = self._load_frame_image(analysis_data['frame_id'])
                    
                    if frame_image is None:
                        # 如果无法加载图像，创建黑色帧
                        frame_image = np.zeros((720, 1280, 3), dtype=np.uint8)
                        logger.warning(f"使用黑色帧替代缺失的帧: {analysis_data['frame_id']}")
                    
                    # 使用可视化工具处理帧
                    processed_frame = self.viz_utils.process_frame_simple(frame_image, analysis_data)
                    
                    # 确保帧尺寸正确
                    if processed_frame.shape[:2] != (video_size[1], video_size[0]):
                        processed_frame = cv2.resize(processed_frame, video_size)
                    
                    # 写入视频
                    video_writer.write(processed_frame)
                    
                    # 更新进度
                    progress.update(task, advance=1)
            
            # 释放资源
            video_writer.release()
            
            # 验证输出文件
            if not Path(output_path).exists():
                logger.error("视频文件创建失败")
                return False
            
            file_size = Path(output_path).stat().st_size
            logger.info(f"✅ 视频渲染完成: {output_path}")
            logger.info(f"   文件大小: {file_size / 1024 / 1024:.2f} MB")
            logger.info(f"   总帧数: {total_frames}")
            logger.info(f"   帧率: {fps} FPS")
            logger.info(f"   分辨率: {video_size[0]}x{video_size[1]}")
            
            return True
            
        except Exception as e:
            logger.error(f"视频渲染失败: {e}")
            return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="DMS分析结果视频渲染工具")
    parser.add_argument("result_dir", help="阶段二的结果目录路径")
    parser.add_argument("-o", "--output", help="输出视频路径", 
                       default="rendered_videos/output.mp4")
    parser.add_argument("--fps", type=float, default=10.0, help="视频帧率 (默认: 10.0)")
    parser.add_argument("--width", type=int, default=1280, help="视频宽度 (默认: 1280)")
    parser.add_argument("--height", type=int, default=720, help="视频高度 (默认: 720)")
    
    args = parser.parse_args()
    
    try:
        console.print(f"[bold green]DMS视频渲染工具[/bold green]")
        console.print(f"结果目录: {args.result_dir}")
        console.print(f"输出路径: {args.output}")
        
        # 创建渲染器
        renderer = VideoRenderer(args.result_dir)
        
        # 渲染视频
        success = renderer.render_video(
            output_path=args.output,
            fps=args.fps,
            video_size=(args.width, args.height)
        )
        
        if success:
            console.print(f"[bold green]✅ 视频渲染成功![/bold green]")
            console.print(f"输出文件: {args.output}")
        else:
            console.print(f"[bold red]❌ 视频渲染失败![/bold red]")
            sys.exit(1)
            
    except Exception as e:
        console.print(f"[bold red]错误: {e}[/bold red]")
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
