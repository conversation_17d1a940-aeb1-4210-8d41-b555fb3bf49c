#!/usr/bin/env python3
"""
帧资产包管理器
提供帧资产包的创建、验证、读取等核心功能
"""

import os
import json
import hashlib
import time
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import cv2
import numpy as np
try:
    import jsonschema
    JSONSCHEMA_AVAILABLE = True
except ImportError:
    JSONSCHEMA_AVAILABLE = False

class AssetPackageManager:
    """帧资产包管理器"""
    
    # 元数据Schema定义
    METADATA_SCHEMA = {
        "type": "object",
        "required": ["asset_info", "source_info", "extraction_info", "frame_info"],
        "properties": {
            "asset_info": {
                "type": "object",
                "required": ["name", "version", "created_at"],
                "properties": {
                    "name": {"type": "string", "pattern": "^[a-zA-Z0-9_-]+$"},
                    "version": {"type": "string"},
                    "created_at": {"type": "string"}
                }
            },
            "source_info": {
                "type": "object",
                "required": ["video_path", "video_size"],
                "properties": {
                    "video_path": {"type": "string"},
                    "video_size": {
                        "type": "object",
                        "required": ["width", "height"],
                        "properties": {
                            "width": {"type": "integer", "minimum": 1},
                            "height": {"type": "integer", "minimum": 1}
                        }
                    }
                }
            },
            "extraction_info": {
                "type": "object",
                "required": ["time_range", "target_fps"],
                "properties": {
                    "time_range": {
                        "type": "object",
                        "required": ["start_time", "end_time"],
                        "properties": {
                            "start_time": {"type": "string"},
                            "end_time": {"type": "string"}
                        }
                    },
                    "target_fps": {"type": "number", "minimum": 0.1}
                }
            },
            "frame_info": {
                "type": "object",
                "required": ["total_frames", "frame_size"],
                "properties": {
                    "total_frames": {"type": "integer", "minimum": 0},
                    "frame_size": {
                        "type": "object",
                        "required": ["width", "height"],
                        "properties": {
                            "width": {"type": "integer", "minimum": 1},
                            "height": {"type": "integer", "minimum": 1}
                        }
                    }
                }
            }
        }
    }
    
    def __init__(self, assets_dir: str = "assets"):
        """初始化资产包管理器
        
        Args:
            assets_dir: 资产包根目录
        """
        self.assets_dir = Path(assets_dir)
        self.assets_dir.mkdir(exist_ok=True)
    
    def create_asset_package(self, asset_name: str, source_info: Dict[str, Any], 
                           extraction_info: Dict[str, Any]) -> Path:
        """创建新的帧资产包
        
        Args:
            asset_name: 资产名称
            source_info: 源视频信息
            extraction_info: 提取参数信息
            
        Returns:
            资产包路径
        """
        # 验证资产名称
        if not self._validate_asset_name(asset_name):
            raise ValueError(f"Invalid asset name: {asset_name}")
        
        # 创建资产包目录
        asset_path = self.assets_dir / asset_name
        if asset_path.exists():
            raise FileExistsError(f"Asset package already exists: {asset_name}")
        
        asset_path.mkdir(parents=True)
        frames_dir = asset_path / "frames"
        frames_dir.mkdir()
        
        # 创建初始metadata
        metadata = self._create_initial_metadata(asset_name, source_info, extraction_info)
        
        # 保存metadata
        metadata_path = asset_path / "metadata.json"
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        return asset_path
    
    def update_frame_info(self, asset_name: str, frame_files: List[str], 
                         processing_time: float = 0.0) -> None:
        """更新帧信息
        
        Args:
            asset_name: 资产名称
            frame_files: 帧文件列表
            processing_time: 处理耗时
        """
        asset_path = self.assets_dir / asset_name
        metadata_path = asset_path / "metadata.json"
        
        if not metadata_path.exists():
            raise FileNotFoundError(f"Metadata not found for asset: {asset_name}")
        
        # 读取现有metadata
        with open(metadata_path, 'r', encoding='utf-8') as f:
            metadata = json.load(f)
        
        # 更新帧信息
        frame_list = []
        total_size = 0
        
        for i, frame_file in enumerate(frame_files):
            frame_path = asset_path / "frames" / frame_file
            if frame_path.exists():
                file_size = frame_path.stat().st_size
                total_size += file_size
                
                frame_list.append({
                    "index": i,
                    "filename": frame_file,
                    "size_bytes": file_size
                })
        
        metadata["frame_info"]["total_frames"] = len(frame_list)
        metadata["frame_info"]["frame_list"] = frame_list
        
        # 更新处理信息
        if "processing_info" not in metadata:
            metadata["processing_info"] = {}
        metadata["processing_info"]["processing_time"] = processing_time
        metadata["processing_info"]["total_size_bytes"] = total_size
        
        # 保存更新后的metadata
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
    
    def validate_asset_package(self, asset_name: str) -> Tuple[bool, List[str]]:
        """验证帧资产包的完整性
        
        Args:
            asset_name: 资产名称
            
        Returns:
            (是否有效, 错误信息列表)
        """
        errors = []
        asset_path = self.assets_dir / asset_name
        
        # 检查目录结构
        if not asset_path.exists():
            errors.append(f"Asset directory not found: {asset_name}")
            return False, errors
        
        if not (asset_path / "frames").exists():
            errors.append("Frames directory not found")
        
        metadata_path = asset_path / "metadata.json"
        if not metadata_path.exists():
            errors.append("Metadata file not found")
            return False, errors
        
        # 验证metadata格式
        try:
            with open(metadata_path, 'r', encoding='utf-8') as f:
                metadata = json.load(f)

            # 使用jsonschema验证（如果可用）
            if JSONSCHEMA_AVAILABLE:
                jsonschema.validate(metadata, self.METADATA_SCHEMA)
            else:
                # 简单的手动验证
                self._validate_metadata_manually(metadata, errors)

        except json.JSONDecodeError as e:
            errors.append(f"Invalid JSON format: {e}")
        except Exception as e:
            if JSONSCHEMA_AVAILABLE and "ValidationError" in str(type(e)):
                errors.append(f"Schema validation failed: {e.message}")
            else:
                errors.append(f"Metadata validation error: {e}")
        
        # 检查帧文件
        if "frame_info" in metadata:
            expected_frames = metadata["frame_info"].get("total_frames", 0)
            frames_dir = asset_path / "frames"
            actual_frames = len(list(frames_dir.glob("*.png"))) + len(list(frames_dir.glob("*.jpg")))
            
            if expected_frames != actual_frames:
                errors.append(f"Frame count mismatch: expected {expected_frames}, found {actual_frames}")
        
        return len(errors) == 0, errors
    
    def load_asset_package(self, asset_name: str) -> Tuple[Dict[str, Any], List[Path]]:
        """加载帧资产包
        
        Args:
            asset_name: 资产名称
            
        Returns:
            (metadata, frame_files)
        """
        asset_path = self.assets_dir / asset_name
        metadata_path = asset_path / "metadata.json"
        
        if not metadata_path.exists():
            raise FileNotFoundError(f"Asset package not found: {asset_name}")
        
        # 读取metadata
        with open(metadata_path, 'r', encoding='utf-8') as f:
            metadata = json.load(f)
        
        # 获取帧文件列表
        frames_dir = asset_path / "frames"
        frame_files = sorted(frames_dir.glob("*.png")) + sorted(frames_dir.glob("*.jpg"))
        
        return metadata, frame_files
    
    def list_asset_packages(self) -> List[str]:
        """列出所有可用的资产包
        
        Returns:
            资产包名称列表
        """
        asset_names = []
        for item in self.assets_dir.iterdir():
            if item.is_dir() and (item / "metadata.json").exists():
                asset_names.append(item.name)
        return sorted(asset_names)
    
    def delete_asset_package(self, asset_name: str) -> None:
        """删除帧资产包
        
        Args:
            asset_name: 资产名称
        """
        asset_path = self.assets_dir / asset_name
        if not asset_path.exists():
            raise FileNotFoundError(f"Asset package not found: {asset_name}")
        
        import shutil
        shutil.rmtree(asset_path)
    
    def _validate_asset_name(self, name: str) -> bool:
        """验证资产名称格式"""
        import re
        return bool(re.match(r'^[a-zA-Z0-9_-]+$', name)) and len(name) <= 64
    
    def _create_initial_metadata(self, asset_name: str, source_info: Dict[str, Any], 
                                extraction_info: Dict[str, Any]) -> Dict[str, Any]:
        """创建初始metadata"""
        return {
            "asset_info": {
                "name": asset_name,
                "version": "1.0",
                "created_at": datetime.now(timezone.utc).isoformat(),
                "created_by": "DMS Automation Platform"
            },
            "source_info": source_info,
            "extraction_info": extraction_info,
            "frame_info": {
                "total_frames": 0,
                "frame_size": extraction_info.get("frame_size", {"width": 1920, "height": 1080}),
                "frame_list": []
            }
        }
    
    def _validate_metadata_manually(self, metadata: Dict[str, Any], errors: List[str]) -> None:
        """手动验证metadata格式（当jsonschema不可用时）"""
        required_sections = ["asset_info", "source_info", "extraction_info", "frame_info"]

        for section in required_sections:
            if section not in metadata:
                errors.append(f"Missing required section: {section}")
                continue

            if section == "asset_info":
                required_fields = ["name", "version", "created_at"]
                for field in required_fields:
                    if field not in metadata[section]:
                        errors.append(f"Missing field in asset_info: {field}")

            elif section == "source_info":
                required_fields = ["video_path", "video_size"]
                for field in required_fields:
                    if field not in metadata[section]:
                        errors.append(f"Missing field in source_info: {field}")

            elif section == "extraction_info":
                required_fields = ["time_range", "target_fps"]
                for field in required_fields:
                    if field not in metadata[section]:
                        errors.append(f"Missing field in extraction_info: {field}")

            elif section == "frame_info":
                required_fields = ["total_frames", "frame_size"]
                for field in required_fields:
                    if field not in metadata[section]:
                        errors.append(f"Missing field in frame_info: {field}")

    def calculate_checksum(self, asset_name: str) -> str:
        """计算资产包的MD5校验值"""
        asset_path = self.assets_dir / asset_name
        frames_dir = asset_path / "frames"

        md5_hash = hashlib.md5()

        # 计算所有帧文件的校验值
        for frame_file in sorted(frames_dir.glob("*")):
            with open(frame_file, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    md5_hash.update(chunk)

        return md5_hash.hexdigest()

    def preprocess_frames(self, asset_name: str, output_dir: str,
                         roi_region: List[int] = [0, 0, 1280, 800],
                         target_size: List[int] = [1280, 800]) -> Tuple[List[str], str]:
        """
        预处理帧图像：裁剪和缩放

        Args:
            asset_name: 资产包名称
            output_dir: 输出目录
            roi_region: ROI区域 [x, y, width, height]
            target_size: 目标尺寸 [width, height]

        Returns:
            Tuple[List[str], str]: (处理后的图像路径列表, parseinfo.txt路径)
        """
        asset_path = self.assets_dir / asset_name
        frames_dir = asset_path / "frames"

        if not frames_dir.exists():
            raise FileNotFoundError(f"Frames directory not found: {frames_dir}")

        # 创建临时处理目录
        processed_dir = Path(output_dir) / "processed_frames"
        processed_dir.mkdir(parents=True, exist_ok=True)

        processed_files = []
        frame_files = sorted(frames_dir.glob("*.png"))

        for i, frame_file in enumerate(frame_files):
            # 读取原始图像
            original_image = cv2.imread(str(frame_file))
            if original_image is None:
                print(f"Warning: Failed to load image {frame_file}")
                continue

            # 裁剪图像
            x, y, w, h = roi_region
            height, width = original_image.shape[:2]

            # 验证裁剪参数
            if (x < 0 or y < 0 or w <= 0 or h <= 0 or
                x + w > width or y + h > height):
                print(f"Warning: Invalid crop parameters for {frame_file}")
                print(f"Image size: {width}x{height}, ROI: {roi_region}")
                continue

            cropped_image = original_image[y:y+h, x:x+w]

            # 缩放到目标尺寸
            if cropped_image.shape[:2] != (target_size[1], target_size[0]):
                processed_image = cv2.resize(cropped_image, target_size,
                                           interpolation=cv2.INTER_LINEAR)
            else:
                processed_image = cropped_image

            # 保存处理后的图像
            processed_filename = processed_dir / f"{i:06d}.png"
            cv2.imwrite(str(processed_filename), processed_image)
            processed_files.append(str(processed_filename))

        # 生成parseinfo.txt
        parseinfo_path = Path(output_dir) / "parseinfo.txt"
        with open(parseinfo_path, 'w') as f:
            for i, processed_file in enumerate(processed_files):
                # 确保使用绝对路径
                abs_path = Path(processed_file).resolve()
                f.write(f"{i},{abs_path}\n")

        return processed_files, str(parseinfo_path)

    def cleanup_processed_frames(self, output_dir: str):
        """清理预处理生成的临时文件"""
        processed_dir = Path(output_dir) / "processed_frames"
        parseinfo_path = Path(output_dir) / "parseinfo.txt"

        try:
            # 删除处理后的图像目录
            if processed_dir.exists():
                import shutil
                shutil.rmtree(processed_dir)
                print(f"🗑️ 已清理临时图像目录: {processed_dir}")

            # 删除parseinfo.txt
            if parseinfo_path.exists():
                parseinfo_path.unlink()
                print(f"🗑️ 已清理parseinfo.txt: {parseinfo_path}")

        except Exception as e:
            print(f"⚠️ 清理临时文件时出错: {e}")
