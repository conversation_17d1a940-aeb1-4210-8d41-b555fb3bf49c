# DMS自动化分析与渲染平台

## 项目概述

DMS自动化分析与渲染平台是一个全自动化的C++算法验证平台，旨在彻底改造现有手动、繁琐、易错的C++算法验证流程。平台实现从视频素材准备、多版本C++批量处理、到最终结果渲染与对比的端到端自动化。

## 核心目标

- **减少重复性动作，解放人力**
- **确保测试过程的稳定性和可复现性**
- **提供高效的多版本算法对比能力**

## 技术架构

### 三阶段模块化工作流

1. **阶段一：素材准备** (`prepare_assets.py`)
   - 视频片段裁剪
   - 帧序列提取
   - 元数据管理

2. **阶段二：核心处理** (`run_cpp_analysis.py` + `setup_and_verify.sh`)
   - 环境配置与验证
   - 批量C++程序执行
   - 结果收集与存储

3. **阶段三：按需渲染** (`render_video.py` + `create_comparison.py`)
   - 单版本结果渲染
   - 多版本对比视频生成

## 目录结构

```
dms_automation/
├── assets/          # 帧资产包存储
├── results/         # JSON结果存储
├── final_videos/    # 最终渲染视频
├── cpp_kits/        # C++工具包版本管理
├── scripts/         # 核心脚本集合
└── README.md        # 本文档
```

## 技术栈

- **编排语言**: Python 3.x
- **环境配置**: Shell Script (.sh)
- **核心计算**: C++可执行程序及动态链接库
- **配置管理**: JSON文件
- **视频处理**: FFmpeg

## 开发状态

当前处于**开发阶段**，正在按照MVP原则逐步实现各个模块。

## 快速开始

### 环境要求

- Python 3.x
- FFmpeg
- SSH免密登录到目标服务器的配置

### 基本使用流程

1. **素材准备**
   ```bash
   python scripts/prepare_assets.py --source video.mp4 --start 00:01:00 --end 00:01:30 --name test_asset
   ```

2. **批量分析**
   ```bash
   python scripts/run_cpp_analysis.py
   ```

3. **结果渲染**
   ```bash
   python scripts/render_video.py --asset test_asset --version v1
   python scripts/create_comparison.py --asset test_asset --versions v1,v2,v3
   ```

## 注意事项

- 所有脚本设计为幂等性，支持断点续传
- 配置文件驱动，避免硬编码
- 严格的模块化分离，各阶段职责单一
