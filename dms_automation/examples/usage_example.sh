#!/bin/bash
# DMS自动化分析与渲染平台 - 使用示例

echo "=== DMS自动化分析与渲染平台使用示例 ==="

# 设置脚本目录
SCRIPT_DIR="../scripts"
ASSETS_DIR="../assets"

# 示例1: 基本用法
echo "示例1: 基本用法"
echo "从视频中提取1分钟到1分30秒的帧序列，目标帧率10fps"
echo ""
echo "命令:"
echo "python3 $SCRIPT_DIR/prepare_assets.py \\"
echo "    --source /path/to/your/video.mp4 \\"
echo "    --start 00:01:00 \\"
echo "    --end 00:01:30 \\"
echo "    --name highway_scene_001"
echo ""

# 示例2: 高帧率提取
echo "示例2: 高帧率提取"
echo "提取更高帧率的帧序列用于详细分析"
echo ""
echo "命令:"
echo "python3 $SCRIPT_DIR/prepare_assets.py \\"
echo "    --source /path/to/your/video.mp4 \\"
echo "    --start 00:02:15 \\"
echo "    --end 00:02:45 \\"
echo "    --name detailed_analysis_001 \\"
echo "    --fps 30"
echo ""

# 示例3: ROI裁剪
echo "示例3: ROI裁剪"
echo "只提取视频中的特定区域"
echo ""
echo "命令:"
echo "python3 $SCRIPT_DIR/prepare_assets.py \\"
echo "    --source /path/to/your/video.mp4 \\"
echo "    --start 00:00:30 \\"
echo "    --end 00:01:00 \\"
echo "    --name roi_test_001 \\"
echo "    --fps 15 \\"
echo "    --roi 1280:800:320:140"
echo ""

# 示例4: 使用配置文件
echo "示例4: 使用配置文件"
echo "使用自定义配置文件进行处理"
echo ""
echo "命令:"
echo "python3 $SCRIPT_DIR/prepare_assets.py \\"
echo "    --source /path/to/your/video.mp4 \\"
echo "    --start 00:03:00 \\"
echo "    --end 00:03:20 \\"
echo "    --name config_test_001 \\"
echo "    --config $SCRIPT_DIR/prepare_assets_config.json"
echo ""

# 示例5: 参数验证
echo "示例5: 参数验证（dry-run）"
echo "在实际处理前验证所有参数"
echo ""
echo "命令:"
echo "python3 $SCRIPT_DIR/prepare_assets.py \\"
echo "    --source /path/to/your/video.mp4 \\"
echo "    --start 00:01:00 \\"
echo "    --end 00:01:30 \\"
echo "    --name validation_test \\"
echo "    --dry-run"
echo ""

# 查看帮助
echo "查看完整帮助信息:"
echo "python3 $SCRIPT_DIR/prepare_assets.py --help"
echo ""

# 查看生成的资产包
echo "=== 查看生成的资产包 ==="
echo "资产包将保存在: $ASSETS_DIR/{asset_name}/"
echo ""
echo "目录结构:"
echo "assets/"
echo "└── {asset_name}/"
echo "    ├── frames/           # 帧图片文件"
echo "    │   ├── 000000.png"
echo "    │   ├── 000001.png"
echo "    │   └── ..."
echo "    └── metadata.json     # 元数据文件"
echo ""

echo "=== 下一步 ==="
echo "1. 生成帧资产包后，可以使用阶段二脚本进行C++批量处理"
echo "2. 最后使用阶段三脚本进行结果渲染和对比"
echo ""
echo "完整工作流："
echo "视频文件 → [阶段一] → 帧资产包 → [阶段二] → JSON结果 → [阶段三] → 渲染视频"
