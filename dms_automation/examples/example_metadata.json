{"asset_info": {"name": "highway_scene_001", "version": "1.0", "created_at": "2025-07-10T10:30:00Z", "created_by": "DMS Automation Platform"}, "source_info": {"video_path": "/home/<USER>/data/dms/videos/highway_test.mp4", "video_size": {"width": 1920, "height": 1080}, "video_fps": 30.0, "video_duration": 300.5, "video_codec": "h264"}, "extraction_info": {"time_range": {"start_time": "00:01:00", "end_time": "00:01:30", "duration": 30.0}, "roi": {"x": 0, "y": 0, "width": 1920, "height": 1080}, "target_fps": 10.0, "output_format": "png", "quality_settings": {"compression": "lossless", "color_space": "RGB"}}, "frame_info": {"total_frames": 300, "frame_size": {"width": 1920, "height": 1080}, "frame_list": [{"index": 0, "filename": "000000.png", "timestamp": 60.0, "size_bytes": 1024000}, {"index": 1, "filename": "000001.png", "timestamp": 60.1, "size_bytes": 1025600}, {"index": 2, "filename": "000002.png", "timestamp": 60.2, "size_bytes": 1023400}]}, "processing_info": {"ffmpeg_version": "4.4.2", "processing_time": 45.2, "total_size_bytes": 307200000, "system_info": {"platform": "Linux", "python_version": "3.8.10", "hostname": "dms-workstation"}}, "validation": {"checksum_type": "md5", "checksum_value": "a1b2c3d4e5f6789012345678901234567890abcd", "frame_checksums": {"000000.png": "1a2b3c4d5e6f7890", "000001.png": "2b3c4d5e6f7890a1", "000002.png": "3c4d5e6f7890a1b2"}}, "custom_fields": {"scene_type": "highway", "weather_condition": "sunny", "time_of_day": "afternoon", "driver_id": "test_driver_001", "test_purpose": "algorithm_validation"}}