# 帧资产包数据结构规范

## 概述

帧资产包是DMS自动化分析与渲染平台的核心数据格式，实现"一次准备，多次使用"的设计目标。每个帧资产包包含从源视频提取的帧序列及其完整的元数据信息。

## 1. 目录结构

### 1.1 标准目录布局

```
assets/
└── {asset_name}/
    ├── frames/                 # 帧图片文件目录
    │   ├── 000000.png         # 第0帧
    │   ├── 000001.png         # 第1帧
    │   ├── 000002.png         # 第2帧
    │   └── ...                # 更多帧文件
    ├── metadata.json          # 元数据文件
    └── checksum.md5           # 校验文件(可选)
```

### 1.2 命名规范

#### 资产名称 (asset_name)
- **格式**: `[a-zA-Z0-9_-]+`
- **示例**: `test_video_001`, `scene_highway_20250710`
- **限制**: 不超过64字符，不包含特殊字符和空格

#### 帧文件命名
- **格式**: `{frame_index:06d}.{ext}`
- **示例**: `000000.png`, `000123.jpg`
- **说明**: 6位数字编号，从000000开始递增

## 2. metadata.json 格式规范

### 2.1 完整Schema

```json
{
  "asset_info": {
    "name": "string",           // 资产名称
    "version": "1.0",           // 资产包格式版本
    "created_at": "ISO8601",    // 创建时间
    "created_by": "string"      // 创建者/工具信息
  },
  "source_info": {
    "video_path": "string",     // 源视频文件路径
    "video_size": {             // 源视频分辨率
      "width": 1920,
      "height": 1080
    },
    "video_fps": 30.0,          // 源视频帧率
    "video_duration": 120.5,    // 源视频总时长(秒)
    "video_codec": "string"     // 视频编码格式
  },
  "extraction_info": {
    "time_range": {
      "start_time": "00:01:00", // 提取开始时间
      "end_time": "00:01:30",   // 提取结束时间
      "duration": 30.0          // 提取时长(秒)
    },
    "roi": {                    // 感兴趣区域(可选)
      "x": 0,
      "y": 0, 
      "width": 1920,
      "height": 1080
    },
    "target_fps": 10.0,         // 目标帧率
    "output_format": "png",     // 输出图片格式
    "quality_settings": {       // 质量设置
      "compression": "lossless",
      "color_space": "RGB"
    }
  },
  "frame_info": {
    "total_frames": 300,        // 总帧数
    "frame_size": {             // 帧图片分辨率
      "width": 1920,
      "height": 1080
    },
    "frame_list": [             // 帧文件列表
      {
        "index": 0,
        "filename": "000000.png",
        "timestamp": 60.0,      // 在源视频中的时间戳(秒)
        "size_bytes": 1024000   // 文件大小(字节)
      }
    ]
  },
  "processing_info": {
    "ffmpeg_version": "string", // FFmpeg版本信息
    "processing_time": 45.2,    // 处理耗时(秒)
    "system_info": {            // 系统信息
      "platform": "Linux",
      "python_version": "3.8.10"
    }
  },
  "validation": {
    "checksum_type": "md5",     // 校验类型
    "checksum_value": "string", // 整体校验值
    "frame_checksums": {}       // 各帧校验值(可选)
  },
  "custom_fields": {}           // 自定义扩展字段
}
```

### 2.2 最小化Schema (MVP版本)

```json
{
  "asset_info": {
    "name": "test_asset",
    "version": "1.0",
    "created_at": "2025-07-10T10:30:00Z"
  },
  "source_info": {
    "video_path": "/path/to/source.mp4",
    "video_size": {"width": 1920, "height": 1080}
  },
  "extraction_info": {
    "time_range": {
      "start_time": "00:01:00",
      "end_time": "00:01:30"
    },
    "target_fps": 10.0
  },
  "frame_info": {
    "total_frames": 300,
    "frame_size": {"width": 1920, "height": 1080}
  }
}
```

## 3. 数据验证机制

### 3.1 完整性验证

```python
def validate_asset_package(asset_path):
    """验证帧资产包的完整性"""
    checks = [
        check_directory_structure(asset_path),
        check_metadata_schema(asset_path),
        check_frame_files_exist(asset_path),
        check_frame_count_consistency(asset_path),
        check_checksums(asset_path)  # 可选
    ]
    return all(checks)
```

### 3.2 Schema验证

使用JSON Schema验证metadata.json的格式正确性：

```python
import jsonschema

METADATA_SCHEMA = {
    "type": "object",
    "required": ["asset_info", "source_info", "extraction_info", "frame_info"],
    "properties": {
        "asset_info": {
            "type": "object",
            "required": ["name", "version", "created_at"],
            "properties": {
                "name": {"type": "string", "pattern": "^[a-zA-Z0-9_-]+$"},
                "version": {"type": "string"},
                "created_at": {"type": "string", "format": "date-time"}
            }
        }
        # ... 更多schema定义
    }
}
```

## 4. 使用示例

### 4.1 创建帧资产包

```bash
python scripts/prepare_assets.py \
    --source /path/to/video.mp4 \
    --start 00:01:00 \
    --end 00:01:30 \
    --name test_scene_001 \
    --fps 10 \
    --roi 1920:1080:0:0
```

### 4.2 读取帧资产包

```python
import json
from pathlib import Path

def load_asset_package(asset_name):
    asset_path = Path("assets") / asset_name
    metadata_path = asset_path / "metadata.json"
    
    with open(metadata_path, 'r') as f:
        metadata = json.load(f)
    
    frames_dir = asset_path / "frames"
    frame_files = sorted(frames_dir.glob("*.png"))
    
    return metadata, frame_files
```

## 5. 兼容性和扩展性

### 5.1 版本兼容性

- 使用语义化版本号 (Semantic Versioning)
- 向后兼容性保证
- 版本升级迁移机制

### 5.2 扩展字段

- `custom_fields` 支持用户自定义数据
- 预留扩展接口
- 插件化处理支持

### 5.3 多格式支持

- 图片格式：PNG (默认), JPG, TIFF
- 压缩选项：无损、有损、自适应
- 色彩空间：RGB, YUV, Grayscale

## 6. 最佳实践

### 6.1 性能优化

- 使用适当的图片压缩
- 批量I/O操作
- 内存使用优化

### 6.2 存储管理

- 定期清理临时文件
- 磁盘空间监控
- 自动归档机制

### 6.3 错误处理

- 详细的错误日志
- 优雅的失败恢复
- 用户友好的错误信息
