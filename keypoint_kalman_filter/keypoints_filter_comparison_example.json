{"comment": "关键点滤波效果对比测试配置", "description": "此配置展示如何同时评估原始关键点和滤波后关键点的质量", "pipeline_example": {"face_keypoints_decoder": {"class_name": "HeatmapKeypointsDecoder", "config": {"stride": 8, "offset": 4, "scale": 2}, "name": "face_keypoints_raw"}, "raw_quality_evaluator": {"class_name": "KeypointsQualityEvaluator", "config": {"num_keypoints": 19, "window_size": 50, "score_threshold": 0.1, "enable_logging": true, "log_file_path": "/tmp/face_keypoints_raw_quality.csv"}, "name": "raw_evaluator", "inbound_nodes": [["face_keypoints_raw", 0, 0, {}]]}, "kalman_filter": {"class_name": "Keypoints<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config": {"num_keypoints": 19, "process_noise": 0.0001, "measurement_noise": 0.1, "score_threshold": 0.1}, "name": "face_keypoints_filtered", "inbound_nodes": [["raw_evaluator", 0, 0, {}]]}, "filtered_quality_evaluator": {"class_name": "KeypointsQualityEvaluator", "config": {"num_keypoints": 19, "window_size": 50, "score_threshold": 0.1, "enable_logging": true, "log_file_path": "/tmp/face_keypoints_filtered_quality.csv"}, "name": "filtered_evaluator", "inbound_nodes": [["face_keypoints_filtered", 0, 0, {}]]}}, "evaluation_metrics": {"stability_metrics": {"mean_std_dev": "关键点分散度的平均值，越小越稳定", "max_std_dev": "关键点分散度的最大值，反映最不稳定的时刻", "stability_score": "综合稳定性分数 (0-1)，1表示最稳定"}, "motion_metrics": {"mean_velocity": "关键点平均移动速度，像素/帧", "max_velocity": "关键点最大移动速度，反映最剧烈的运动", "smoothness_score": "运动平滑度分数 (0-1)，1表示最平滑"}, "overall_metrics": {"valid_frames": "有效帧数，置信度高于阈值的帧", "total_frames": "总帧数", "valid_rate": "有效率 = 有效帧数 / 总帧数"}}, "comparison_analysis": {"expected_improvements": {"stability_score": "滤波后应该提高", "smoothness_score": "滤波后应该提高", "mean_std_dev": "滤波后应该减小", "mean_velocity": "滤波后应该减小（更平稳的运动）"}, "analysis_script": {"description": "可以使用Python脚本分析生成的CSV文件", "example_code": "import pandas as pd\nraw_data = pd.read_csv('/tmp/face_keypoints_raw_quality.csv')\nfiltered_data = pd.read_csv('/tmp/face_keypoints_filtered_quality.csv')\nprint('原始数据平均稳定性:', raw_data['稳定性'].mean())\nprint('滤波后平均稳定性:', filtered_data['稳定性'].mean())"}}, "parameter_tuning_guide": {"if_too_smooth": {"problem": "滤波后关键点过于平滑，响应性差", "solution": "增大process_noise或减小measurement_noise"}, "if_not_smooth_enough": {"problem": "滤波后仍然有抖动", "solution": "减小process_noise或增大measurement_noise"}, "if_lag_too_much": {"problem": "滤波后有明显延迟", "solution": "减小measurement_noise，增强对观测的信任"}}}