#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
坐标系问题诊断工具
用于验证坐标系选择对滤波效果的影响
"""

import numpy as np
import matplotlib.pyplot as plt
import argparse
import json
from typing import List, Tuple
from datetime import datetime

def load_keypoints_data(json_file: str) -> List[np.ndarray]:
    """加载关键点数据"""
    with open(json_file, 'r') as f:
        data = json.load(f)
    
    keypoints_sequence = []
    for frame_data in data:
        if 'keypoints' in frame_data:
            keypoints = np.array(frame_data['keypoints'])
            keypoints_sequence.append(keypoints)
    
    return keypoints_sequence

def analyze_motion_components(keypoints_sequence: List[np.ndarray]) -> dict:
    """分析运动成分"""
    if len(keypoints_sequence) < 2:
        return {}
    
    # 计算每帧的质心（人脸中心）
    centroids = []
    for keypoints in keypoints_sequence:
        centroid = np.mean(keypoints, axis=0)
        centroids.append(centroid)
    
    centroids = np.array(centroids)
    
    # 分析全局运动（人脸整体移动）
    global_displacements = []
    for i in range(1, len(centroids)):
        displacement = np.linalg.norm(centroids[i] - centroids[i-1])
        global_displacements.append(displacement)
    
    # 分析局部运动（关键点相对于质心的变化）
    local_displacements = []
    for i in range(1, len(keypoints_sequence)):
        curr_centroid = centroids[i]
        prev_centroid = centroids[i-1]
        
        # 计算相对于质心的坐标
        curr_relative = keypoints_sequence[i] - curr_centroid
        prev_relative = keypoints_sequence[i-1] - prev_centroid
        
        # 计算局部位移
        local_displacement = np.linalg.norm(curr_relative - prev_relative, axis=1)
        local_displacements.append(local_displacement.mean())
    
    # 计算总运动（原始全图坐标系）
    total_displacements = []
    for i in range(1, len(keypoints_sequence)):
        total_displacement = np.linalg.norm(keypoints_sequence[i] - keypoints_sequence[i-1], axis=1)
        total_displacements.append(total_displacement.mean())
    
    return {
        'global_motion': np.array(global_displacements),
        'local_motion': np.array(local_displacements), 
        'total_motion': np.array(total_displacements),
        'centroids': centroids
    }

def simulate_local_coordinate_filtering(keypoints_sequence: List[np.ndarray], 
                                      alpha: float = 0.8) -> List[np.ndarray]:
    """模拟局部坐标系滤波效果"""
    if len(keypoints_sequence) == 0:
        return []
    
    # 使用第一帧作为参考建立局部坐标系
    reference_centroid = np.mean(keypoints_sequence[0], axis=0)
    reference_scale = np.std(keypoints_sequence[0], axis=0).mean()
    
    filtered_sequence = []
    prev_local_filtered = None
    
    for i, keypoints in enumerate(keypoints_sequence):
        # 转换到局部坐标系（相对于参考质心和尺度）
        current_centroid = np.mean(keypoints, axis=0)
        current_scale = np.std(keypoints, axis=0).mean()
        
        # 归一化坐标
        local_coords = (keypoints - current_centroid) / (current_scale + 1e-6)
        
        if prev_local_filtered is None:
            # 第一帧
            local_filtered = local_coords
        else:
            # 简单的指数移动平均滤波
            local_filtered = alpha * prev_local_filtered + (1 - alpha) * local_coords
        
        # 转换回全局坐标系
        global_filtered = local_filtered * (current_scale + 1e-6) + current_centroid
        
        filtered_sequence.append(global_filtered)
        prev_local_filtered = local_filtered
    
    return filtered_sequence

def diagnose_coordinate_system_impact(original_seq: List[np.ndarray], 
                                    filtered_seq: List[np.ndarray]) -> dict:
    """诊断坐标系选择的影响"""
    
    # 分析原始数据的运动成分
    original_analysis = analyze_motion_components(original_seq)
    filtered_analysis = analyze_motion_components(filtered_seq)
    
    # 模拟局部坐标系滤波
    local_filtered_seq = simulate_local_coordinate_filtering(original_seq)
    local_analysis = analyze_motion_components(local_filtered_seq)
    
    diagnosis = {}
    
    # 1. 运动成分比例分析
    if len(original_analysis) > 0:
        global_ratio = np.mean(original_analysis['global_motion']) / (np.mean(original_analysis['total_motion']) + 1e-6)
        local_ratio = np.mean(original_analysis['local_motion']) / (np.mean(original_analysis['total_motion']) + 1e-6)
        
        diagnosis['全局运动占比'] = global_ratio * 100
        diagnosis['局部运动占比'] = local_ratio * 100
        
        # 2. 判断主要运动类型
        if global_ratio > 0.7:
            diagnosis['主要运动类型'] = "人脸整体运动为主"
            diagnosis['建议'] = "强烈建议使用局部坐标系"
        elif global_ratio > 0.4:
            diagnosis['主要运动类型'] = "混合运动"
            diagnosis['建议'] = "建议尝试局部坐标系"
        else:
            diagnosis['主要运动类型'] = "关键点局部变化为主"
            diagnosis['建议'] = "全局坐标系可能适用"
    
    # 3. 滤波效果对比
    if len(filtered_analysis) > 0 and len(local_analysis) > 0:
        # 全局坐标系滤波效果
        global_filter_reduction = (np.mean(original_analysis['total_motion']) - 
                                  np.mean(filtered_analysis['total_motion'])) / np.mean(original_analysis['total_motion'])
        
        # 局部坐标系滤波效果
        local_filter_reduction = (np.mean(original_analysis['total_motion']) - 
                                 np.mean(local_analysis['total_motion'])) / np.mean(original_analysis['total_motion'])
        
        diagnosis['全局坐标系滤波减噪率'] = global_filter_reduction * 100
        diagnosis['局部坐标系滤波减噪率'] = local_filter_reduction * 100
        
        # 判断哪种方法更好
        if local_filter_reduction > global_filter_reduction * 1.2:
            diagnosis['推荐方案'] = "局部坐标系滤波效果更好"
        elif global_filter_reduction > local_filter_reduction * 1.2:
            diagnosis['推荐方案'] = "全局坐标系滤波效果更好"
        else:
            diagnosis['推荐方案'] = "两种方案效果相近"
    
    return diagnosis, local_filtered_seq

def plot_motion_analysis(original_seq: List[np.ndarray], 
                        filtered_seq: List[np.ndarray],
                        local_filtered_seq: List[np.ndarray],
                        output_dir: str = "./"):
    """绘制运动分析图表"""
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['Noto Sans CJK JP', 'AR PL UMing CN', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 分析各种情况的运动
    original_analysis = analyze_motion_components(original_seq)
    filtered_analysis = analyze_motion_components(filtered_seq) 
    local_analysis = analyze_motion_components(local_filtered_seq)
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('坐标系影响诊断分析', fontsize=16)
    
    frames = range(len(original_analysis['total_motion']))
    
    # 1. 运动成分分解
    axes[0, 0].plot(frames, original_analysis['global_motion'], label='人脸整体运动', alpha=0.8)
    axes[0, 0].plot(frames, original_analysis['local_motion'], label='关键点局部变化', alpha=0.8)
    axes[0, 0].set_title('原始数据运动成分分解')
    axes[0, 0].set_ylabel('位移 (像素)')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 不同坐标系下的总运动对比
    axes[0, 1].plot(frames, original_analysis['total_motion'], label='原始数据', alpha=0.8)
    axes[0, 1].plot(frames, filtered_analysis['total_motion'], label='全局坐标系滤波', alpha=0.8)
    axes[0, 1].plot(frames, local_analysis['total_motion'], label='局部坐标系滤波', alpha=0.8)
    axes[0, 1].set_title('不同坐标系滤波效果对比')
    axes[0, 1].set_ylabel('总位移 (像素)')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 人脸中心轨迹
    centroids = original_analysis['centroids']
    filtered_centroids = filtered_analysis['centroids']
    local_centroids = local_analysis['centroids']
    
    axes[1, 0].plot(centroids[:, 0], centroids[:, 1], 'o-', label='原始轨迹', alpha=0.7, markersize=2)
    axes[1, 0].plot(filtered_centroids[:, 0], filtered_centroids[:, 1], 's-', label='全局滤波轨迹', alpha=0.7, markersize=2)
    axes[1, 0].plot(local_centroids[:, 0], local_centroids[:, 1], '^-', label='局部滤波轨迹', alpha=0.7, markersize=2)
    axes[1, 0].set_title('人脸中心移动轨迹')
    axes[1, 0].set_xlabel('X坐标 (像素)')
    axes[1, 0].set_ylabel('Y坐标 (像素)')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    axes[1, 0].axis('equal')
    
    # 4. 运动幅度统计
    methods = ['原始', '全局坐标系\n滤波', '局部坐标系\n滤波']
    global_motions = [np.mean(original_analysis['global_motion']), 
                     np.mean(filtered_analysis['global_motion']),
                     np.mean(local_analysis['global_motion'])]
    local_motions = [np.mean(original_analysis['local_motion']),
                    np.mean(filtered_analysis['local_motion']),
                    np.mean(local_analysis['local_motion'])]
    
    x = np.arange(len(methods))
    width = 0.35
    
    axes[1, 1].bar(x - width/2, global_motions, width, label='整体运动', alpha=0.8)
    axes[1, 1].bar(x + width/2, local_motions, width, label='局部变化', alpha=0.8)
    axes[1, 1].set_title('运动幅度对比')
    axes[1, 1].set_ylabel('平均位移 (像素)')
    axes[1, 1].set_xticks(x)
    axes[1, 1].set_xticklabels(methods)
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图表
    output_file = f"coordinate_diagnosis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
    output_path = f"{output_dir}/{output_file}"
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"诊断图表已保存到: {output_path}")
    
    plt.show()

def generate_diagnosis_report(diagnosis: dict, output_dir: str = "./"):
    """生成诊断报告"""
    
    report_file = f"{output_dir}/coordinate_system_diagnosis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("坐标系问题诊断报告\n")
        f.write("=" * 60 + "\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("问题诊断结果\n")
        f.write("=" * 60 + "\n\n")
        
        if '全局运动占比' in diagnosis:
            f.write(f"运动成分分析:\n")
            f.write(f"├─ 人脸整体运动占比: {diagnosis['全局运动占比']:.1f}%\n")
            f.write(f"└─ 关键点局部变化占比: {diagnosis['局部运动占比']:.1f}%\n\n")
            
            f.write(f"主要运动类型: {diagnosis['主要运动类型']}\n")
            f.write(f"初步建议: {diagnosis['建议']}\n\n")
        
        if '全局坐标系滤波减噪率' in diagnosis:
            f.write(f"滤波效果对比:\n")
            f.write(f"├─ 全局坐标系滤波减噪率: {diagnosis['全局坐标系滤波减噪率']:+.1f}%\n")
            f.write(f"└─ 局部坐标系滤波减噪率: {diagnosis['局部坐标系滤波减噪率']:+.1f}%\n\n")
            
            f.write(f"推荐方案: {diagnosis['推荐方案']}\n\n")
        
        f.write("=" * 60 + "\n")
        f.write("详细分析与建议\n")
        f.write("=" * 60 + "\n\n")
        
        if diagnosis.get('全局运动占比', 0) > 70:
            f.write("🚨 发现问题！\n")
            f.write("您的数据中人脸整体运动占主导地位。这说明：\n")
            f.write("• 关键点的大部分运动来自于人脸在图像中的位移\n")
            f.write("• 卡尔曼滤波器错误地将正常的人脸运动当作噪声进行平滑\n")
            f.write("• 这解释了为什么数值指标改善但视觉效果很差\n\n")
            
            f.write("💡 解决方案：\n")
            f.write("1. 使用局部坐标系（人脸或眼部）进行滤波\n")
            f.write("2. 先进行人脸对齐，再对关键点进行滤波\n")
            f.write("3. 分别处理全局运动和局部变化\n\n")
            
        elif diagnosis.get('全局运动占比', 0) > 40:
            f.write("⚠️  注意！\n")
            f.write("您的数据包含较多的全局运动成分，建议：\n")
            f.write("• 尝试使用局部坐标系进行滤波\n")
            f.write("• 比较不同坐标系的滤波效果\n\n")
            
        else:
            f.write("✅ 坐标系选择可能合理\n")
            f.write("全局运动成分较少，当前的全局坐标系方法可能适用\n\n")
        
        f.write("具体改进步骤：\n")
        f.write("1. 使用提供的coordinate_transform.py工具转换坐标系\n")
        f.write("2. 在局部坐标系下重新进行卡尔曼滤波\n")
        f.write("3. 使用improved_quality_assessment.py进行感知质量评估\n")
        f.write("4. 对比不同方案的实际视觉效果\n")
    
    print(f"诊断报告已保存到: {report_file}")

def main():
    parser = argparse.ArgumentParser(description='坐标系问题诊断工具')
    parser.add_argument('--original', required=True, help='原始关键点序列文件(JSON)')
    parser.add_argument('--filtered', required=True, help='滤波后关键点序列文件(JSON)')
    parser.add_argument('--output', default='./', help='输出目录')
    
    args = parser.parse_args()
    
    print("正在加载关键点数据...")
    original_seq = load_keypoints_data(args.original)
    filtered_seq = load_keypoints_data(args.filtered)
    
    print("正在进行坐标系影响诊断...")
    diagnosis, local_filtered_seq = diagnose_coordinate_system_impact(original_seq, filtered_seq)
    
    print("正在生成可视化分析...")
    plot_motion_analysis(original_seq, filtered_seq, local_filtered_seq, args.output)
    
    print("正在生成诊断报告...")
    generate_diagnosis_report(diagnosis, args.output)
    
    print("\n" + "="*60)
    print("诊断完成！主要发现：")
    print("="*60)
    
    if '全局运动占比' in diagnosis:
        print(f"• 人脸整体运动占比: {diagnosis['全局运动占比']:.1f}%")
        print(f"• 关键点局部变化占比: {diagnosis['局部运动占比']:.1f}%")
        print(f"• {diagnosis['建议']}")
    
    if '推荐方案' in diagnosis:
        print(f"• {diagnosis['推荐方案']}")

if __name__ == '__main__':
    main() 