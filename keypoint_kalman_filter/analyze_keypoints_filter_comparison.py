#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关键点滤波效果对比分析工具
用于分析卡尔曼滤波前后关键点质量的改善效果
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import os
import argparse
from datetime import datetime

# 配置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['Noto Sans CJK JP', 'AR PL UMing CN', 'DejaVu Sans', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

def load_quality_data(csv_file):
    """加载质量评估数据"""
    if not os.path.exists(csv_file):
        print(f"警告: 文件 {csv_file} 不存在")
        return None
    
    try:
        # 先读取文件前几行，找到包含列名的注释行
        with open(csv_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 查找包含列名的注释行（通常是第二行）
        column_names = None
        data_start_line = 0
        
        for i, line in enumerate(lines):
            if line.strip().startswith('#'):
                # 检查是否包含逗号分隔的列名
                if ',' in line and any(keyword in line for keyword in ['标准差', '速度', '平滑度', '稳定性']):
                    # 提取列名，去掉开头的#和空格
                    column_line = line.strip().lstrip('#').strip()
                    column_names = [col.strip() for col in column_line.split(',')]
                    break
            else:
                data_start_line = i
                break
        
        # 读取CSV文件，跳过注释行
        df = pd.read_csv(csv_file, comment='#')
        
        # 如果找到了列名，设置DataFrame的列名
        if column_names and len(column_names) == len(df.columns):
            df.columns = column_names
        
        return df
    except Exception as e:
        print(f"读取文件 {csv_file} 时出错: {e}")
        return None

def calculate_improvement(before_data, after_data, metric_name):
    """计算改善百分比"""
    if before_data is None or after_data is None:
        return None
    
    before_mean = before_data[metric_name].mean()
    after_mean = after_data[metric_name].mean()
    
    if before_mean == 0:
        return 0
    
    improvement = ((before_mean - after_mean) / before_mean) * 100
    return improvement

def plot_comparison(before_data, after_data, metric_name, title, output_dir):
    """绘制对比图表 - 单个指标版本，用于兼容现有调用"""
    # 这个函数保持原有接口，但实际调用新的综合对比函数
    plot_comprehensive_comparison(before_data, after_data, title, output_dir)

def plot_comprehensive_comparison(before_data, after_data, title, output_dir):
    """绘制综合对比图表 - 所有指标在同一幅图中"""
    if before_data is None or after_data is None:
        return
    
    # 创建子图
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle(f'{title} - 卡尔曼滤波前后质量对比分析', fontsize=16)
    
    # 要绘制的指标
    metrics = [
        ('平均标准差', '标准差 (像素)', 0, 0),
        ('最大标准差', '标准差 (像素)', 0, 1), 
        ('平均速度', '速度 (像素/帧)', 0, 2),
        ('最大速度', '速度 (像素/帧)', 1, 0),
        ('平滑度', '平滑度分数 (0-1)', 1, 1),
        ('稳定性', '稳定性分数 (0-1)', 1, 2)
    ]
    
    for metric_name, ylabel, row, col in metrics:
        ax = axes[row, col]
        
        # 绘制滤波前的数据
        if before_data is not None and metric_name in before_data.columns:
            ax.plot(before_data.index, before_data[metric_name], 
                   label='滤波前', alpha=0.7, color='red', linewidth=1.5)
        
        # 绘制滤波后的数据
        if after_data is not None and metric_name in after_data.columns:
            ax.plot(after_data.index, after_data[metric_name], 
                   label='滤波后', alpha=0.7, color='blue', linewidth=1.5)
        
        ax.set_title(f'{metric_name}对比')
        ax.set_xlabel('帧数')
        ax.set_ylabel(ylabel)
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 计算并显示改善率
        if (before_data is not None and metric_name in before_data.columns and 
            after_data is not None and metric_name in after_data.columns):
            
            before_mean = before_data[metric_name].mean()
            after_mean = after_data[metric_name].mean()
            
            if before_mean != 0:
                if metric_name in ['平均标准差', '最大标准差', '平均速度', '最大速度']:
                    # 这些指标越小越好
                    improvement = (before_mean - after_mean) / before_mean * 100
                else:
                    # 平滑度和稳定性越大越好
                    improvement = (after_mean - before_mean) / before_mean * 100
                
                # 在图上添加改善率文本
                ax.text(0.02, 0.98, f'改善率: {improvement:+.1f}%', 
                       transform=ax.transAxes, fontsize=10,
                       verticalalignment='top',
                       bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    
    # 保存图表
    output_file = os.path.join(output_dir, f'{title}_comprehensive_comparison_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"保存综合对比图表: {output_file}")

def generate_report(results, output_dir):
    """生成分析报告"""
    report_file = os.path.join(output_dir, 'filter_comparison_report.txt')
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("关键点滤波效果分析报告\n")
        f.write("=" * 50 + "\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        for keypoint_type, metrics in results.items():
            f.write(f"{keypoint_type} 分析结果:\n")
            f.write("-" * 30 + "\n")
            
            for metric_name, improvement in metrics.items():
                if improvement is not None:
                    if improvement > 0:
                        f.write(f"  {metric_name}: 改善 {improvement:.2f}%\n")
                    else:
                        f.write(f"  {metric_name}: 恶化 {abs(improvement):.2f}%\n")
                else:
                    f.write(f"  {metric_name}: 无法计算\n")
            f.write("\n")
        
        f.write("评估建议:\n")
        f.write("-" * 30 + "\n")
        f.write("1. 如果平均标准差改善 > 10%，说明滤波效果良好\n")
        f.write("2. 如果平滑度分数改善 > 5%，说明运动更加平滑\n")
        f.write("3. 如果稳定性分数改善 > 5%，说明关键点更加稳定\n")
        f.write("4. 如果某项指标恶化，建议调整卡尔曼滤波器参数\n")
    
    print(f"生成报告: {report_file}")

def main():
    parser = argparse.ArgumentParser(description='关键点滤波效果对比分析')
    parser.add_argument('--data_dir', default='./tmp', help='数据文件目录')
    parser.add_argument('--output_dir', default='./filter_analysis', help='输出目录')
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 定义文件映射
    file_pairs = {
        '面部关键点': {
            'before': os.path.join(args.data_dir, 'face_keypoints_quality_before_filter.csv'),
            'after': os.path.join(args.data_dir, 'face_keypoints_quality.csv')
        },
        '左眼关键点': {
            'before': os.path.join(args.data_dir, 'left_eye_keypoints_quality_before_filter.csv'),
            'after': os.path.join(args.data_dir, 'left_eye_keypoints_quality.csv')
        },
        '右眼关键点': {
            'before': os.path.join(args.data_dir, 'right_eye_keypoints_quality_before_filter.csv'),
            'after': os.path.join(args.data_dir, 'right_eye_keypoints_quality.csv')
        }
    }
    
    # 要分析的指标
    metrics = ['平均标准差', '最大标准差', '平均速度', '最大速度', '平滑度', '稳定性']
    
    results = {}
    
    for keypoint_type, files in file_pairs.items():
        print(f"\n分析 {keypoint_type}...")
        
        before_data = load_quality_data(files['before'])
        after_data = load_quality_data(files['after'])
        
        if before_data is None and after_data is None:
            print(f"  跳过 {keypoint_type}，无可用数据")
            continue
        
        results[keypoint_type] = {}
        
        # 分析各项指标
        for metric in metrics:
            improvement = calculate_improvement(before_data, after_data, metric)
            results[keypoint_type][metric] = improvement
            
            if improvement is not None:
                if improvement > 0:
                    print(f"  {metric}: 改善 {improvement:.2f}%")
                else:
                    print(f"  {metric}: 恶化 {abs(improvement):.2f}%")
        
        # 为每个关键点类型生成一个综合对比图表
        if before_data is not None or after_data is not None:
            plot_comprehensive_comparison(before_data, after_data, keypoint_type, args.output_dir)
    
    # 生成报告
    generate_report(results, args.output_dir)
    
    print(f"\n分析完成！结果保存在: {args.output_dir}")

if __name__ == '__main__':
    main() 