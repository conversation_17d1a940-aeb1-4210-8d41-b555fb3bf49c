#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关键点质量分析工具
用于对比分析卡尔曼滤波前后的关键点质量
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import argparse
import os
from datetime import datetime

def load_quality_data(csv_file):
    """加载质量评估数据"""
    try:
        # 读取CSV文件，跳过前两行注释，手动设置列名
        data = pd.read_csv(csv_file, skiprows=2, 
                          names=['时间戳', '帧ID', '平均标准差', '最大标准差', '平均速度', '最大速度', '平滑度', '稳定性'])
        return data
    except Exception as e:
        print(f"读取文件 {csv_file} 失败: {e}")
        return None

def calculate_statistics(data):
    """计算统计指标"""
    if data is None or data.empty:
        return None
    
    stats = {}
    
    # 基本统计
    numeric_columns = data.select_dtypes(include=[np.number]).columns
    for col in numeric_columns:
        stats[col] = {
            'mean': data[col].mean(),
            'std': data[col].std(),
            'min': data[col].min(),
            'max': data[col].max(),
            'median': data[col].median()
        }
    
    return stats

def compare_quality(raw_data, filtered_data):
    """对比原始数据和滤波后数据的质量"""
    if raw_data is None or filtered_data is None:
        print("数据加载失败，无法进行对比")
        return
    
    print("=" * 60)
    print("关键点质量对比分析报告")
    print("=" * 60)
    print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 计算统计指标
    raw_stats = calculate_statistics(raw_data)
    filtered_stats = calculate_statistics(filtered_data)
    
    if raw_stats is None or filtered_stats is None:
        print("统计计算失败")
        return
    
    # 对比关键指标
    key_metrics = ['平均标准差', '最大标准差', '平均速度', '最大速度', '平滑度', '稳定性']
    
    print("关键指标对比:")
    print("-" * 60)
    print(f"{'指标':<15} {'原始数据':<12} {'滤波后':<12} {'改善率':<10}")
    print("-" * 60)
    
    for metric in key_metrics:
        if metric in raw_stats and metric in filtered_stats:
            raw_val = raw_stats[metric]['mean']
            filtered_val = filtered_stats[metric]['mean']
            
            # 计算改善率
            if metric in ['平均标准差', '最大标准差', '平均速度', '最大速度']:
                # 这些指标越小越好
                improvement = (raw_val - filtered_val) / raw_val * 100 if raw_val != 0 else 0
            else:
                # 平滑度和稳定性越大越好
                improvement = (filtered_val - raw_val) / raw_val * 100 if raw_val != 0 else 0
            
            print(f"{metric:<15} {raw_val:<12.4f} {filtered_val:<12.4f} {improvement:>+7.1f}%")
    
    print()
    
    # 数据有效性分析
    if len(raw_data) > 0 and len(filtered_data) > 0:
        print("数据有效性分析:")
        print("-" * 30)
        print(f"原始数据帧数: {len(raw_data)}")
        print(f"滤波后帧数: {len(filtered_data)}")
        
        if '有效帧数' in raw_data.columns and '总帧数' in raw_data.columns:
            raw_valid_rate = raw_data['有效帧数'].iloc[-1] / raw_data['总帧数'].iloc[-1] * 100
            filtered_valid_rate = filtered_data['有效帧数'].iloc[-1] / filtered_data['总帧数'].iloc[-1] * 100
            print(f"原始数据有效率: {raw_valid_rate:.1f}%")
            print(f"滤波后有效率: {filtered_valid_rate:.1f}%")
    
    print()

def plot_comparison(raw_data, filtered_data, output_dir="./"):
    """绘制对比图表"""
    if raw_data is None or filtered_data is None:
        return
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['Noto Sans CJK JP', 'AR PL UMing CN', 'DejaVu Sans', 'SimHei']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建子图
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('关键点质量对比分析', fontsize=16)
    
    # 1. 标准差对比
    if '平均标准差' in raw_data.columns and '平均标准差' in filtered_data.columns:
        axes[0, 0].plot(raw_data.index, raw_data['平均标准差'], label='原始数据', alpha=0.7)
        axes[0, 0].plot(filtered_data.index, filtered_data['平均标准差'], label='滤波后', alpha=0.7)
        axes[0, 0].set_title('关键点标准差对比')
        axes[0, 0].set_ylabel('标准差 (像素)')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 速度对比
    if '平均速度' in raw_data.columns and '平均速度' in filtered_data.columns:
        axes[0, 1].plot(raw_data.index, raw_data['平均速度'], label='原始数据', alpha=0.7)
        axes[0, 1].plot(filtered_data.index, filtered_data['平均速度'], label='滤波后', alpha=0.7)
        axes[0, 1].set_title('关键点移动速度对比')
        axes[0, 1].set_ylabel('速度 (像素/帧)')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 平滑度对比
    if '平滑度' in raw_data.columns and '平滑度' in filtered_data.columns:
        axes[1, 0].plot(raw_data.index, raw_data['平滑度'], label='原始数据', alpha=0.7)
        axes[1, 0].plot(filtered_data.index, filtered_data['平滑度'], label='滤波后', alpha=0.7)
        axes[1, 0].set_title('运动平滑度对比')
        axes[1, 0].set_ylabel('平滑度分数 (0-1)')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
    
    # 4. 稳定性对比
    if '稳定性' in raw_data.columns and '稳定性' in filtered_data.columns:
        axes[1, 1].plot(raw_data.index, raw_data['稳定性'], label='原始数据', alpha=0.7)
        axes[1, 1].plot(filtered_data.index, filtered_data['稳定性'], label='滤波后', alpha=0.7)
        axes[1, 1].set_title('关键点稳定性对比')
        axes[1, 1].set_ylabel('稳定性分数 (0-1)')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图表
    output_file = os.path.join(output_dir, f"keypoints_quality_comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"对比图表已保存到: {output_file}")
    
    # 显示图表
    plt.show()

def generate_report(raw_data, filtered_data, output_dir="./"):
    """生成详细的分析报告"""
    report_file = os.path.join(output_dir, f"keypoints_quality_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("关键点质量分析报告\n")
        f.write("=" * 50 + "\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        if raw_data is not None and filtered_data is not None:
            raw_stats = calculate_statistics(raw_data)
            filtered_stats = calculate_statistics(filtered_data)
            
            f.write("统计摘要:\n")
            f.write("-" * 30 + "\n")
            
            key_metrics = ['平均标准差', '最大标准差', '平均速度', '最大速度', '平滑度', '稳定性']
            
            for metric in key_metrics:
                if metric in raw_stats and metric in filtered_stats:
                    f.write(f"\n{metric}:\n")
                    f.write(f"  原始数据: 均值={raw_stats[metric]['mean']:.4f}, 标准差={raw_stats[metric]['std']:.4f}\n")
                    f.write(f"  滤波后:   均值={filtered_stats[metric]['mean']:.4f}, 标准差={filtered_stats[metric]['std']:.4f}\n")
                    
                    # 计算改善率
                    raw_val = raw_stats[metric]['mean']
                    filtered_val = filtered_stats[metric]['mean']
                    
                    if metric in ['平均标准差', '最大标准差', '平均速度', '最大速度']:
                        improvement = (raw_val - filtered_val) / raw_val * 100 if raw_val != 0 else 0
                    else:
                        improvement = (filtered_val - raw_val) / raw_val * 100 if raw_val != 0 else 0
                    
                    f.write(f"  改善率:   {improvement:+.1f}%\n")
            
            f.write("\n\n建议:\n")
            f.write("-" * 20 + "\n")
            
            # 基于结果给出调优建议
            if 'stability_score' in filtered_stats:
                stability = filtered_stats['稳定性']['mean']
                if stability < 0.7:
                    f.write("- 稳定性较低，建议调整卡尔曼滤波器参数\n")
                elif stability > 0.9:
                    f.write("- 稳定性良好\n")
            
            if 'smoothness_score' in filtered_stats:
                smoothness = filtered_stats['平滑度']['mean']
                if smoothness < 0.7:
                    f.write("- 平滑度较低，建议减小process_noise参数\n")
                elif smoothness > 0.9:
                    f.write("- 平滑度良好\n")
    
    print(f"详细报告已保存到: {report_file}")

def main():
    parser = argparse.ArgumentParser(description='关键点质量分析工具')
    parser.add_argument('--raw', required=True, help='原始关键点质量数据CSV文件路径')
    parser.add_argument('--filtered', required=True, help='滤波后关键点质量数据CSV文件路径')
    parser.add_argument('--output', default='./', help='输出目录 (默认: ./)')
    parser.add_argument('--no-plot', action='store_true', help='不生成图表')
    parser.add_argument('--no-report', action='store_true', help='不生成详细报告')
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not os.path.exists(args.raw):
        print(f"错误: 原始数据文件不存在: {args.raw}")
        return
    
    if not os.path.exists(args.filtered):
        print(f"错误: 滤波后数据文件不存在: {args.filtered}")
        return
    
    # 创建输出目录
    os.makedirs(args.output, exist_ok=True)
    
    # 加载数据
    print("正在加载数据...")
    raw_data = load_quality_data(args.raw)
    filtered_data = load_quality_data(args.filtered)
    
    # 进行对比分析
    compare_quality(raw_data, filtered_data)
    
    # 生成图表
    if not args.no_plot:
        print("\n正在生成对比图表...")
        plot_comparison(raw_data, filtered_data, args.output)
    
    # 生成详细报告
    if not args.no_report:
        print("\n正在生成详细报告...")
        generate_report(raw_data, filtered_data, args.output)
    
    print("\n分析完成!")

if __name__ == "__main__":
    main() 