#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
坐标系转换工具
将全图坐标系下的关键点转换为局部坐标系（人脸或眼睛区域）
"""

import numpy as np
import json
import argparse
from typing import Dict, List, Tuple, Optional

class CoordinateTransformer:
    """坐标系转换类"""
    
    def __init__(self, transform_type='face'):
        """
        初始化坐标转换器
        
        Args:
            transform_type: 转换类型 ('face' 或 'eye')
        """
        self.transform_type = transform_type
        
    def get_face_bbox(self, keypoints: np.ndarray) -> Tuple[float, float, float, float]:
        """
        根据关键点估算人脸边界框
        
        Args:
            keypoints: 关键点数组 [N, 2]
            
        Returns:
            (x_min, y_min, width, height)
        """
        x_coords = keypoints[:, 0]
        y_coords = keypoints[:, 1]
        
        x_min, x_max = np.min(x_coords), np.max(x_coords)
        y_min, y_max = np.min(y_coords), np.max(y_coords)
        
        # 添加一些边距
        margin = 0.1
        width = x_max - x_min
        height = y_max - y_min
        
        x_min -= width * margin
        y_min -= height * margin
        width *= (1 + 2 * margin)
        height *= (1 + 2 * margin)
        
        return x_min, y_min, width, height
    
    def get_eye_region(self, keypoints: np.ndarray, eye_indices: List[int]) -> Tuple[float, float, float, float]:
        """
        根据眼部关键点获取眼部区域
        
        Args:
            keypoints: 关键点数组 [N, 2]
            eye_indices: 眼部关键点索引列表
            
        Returns:
            (x_min, y_min, width, height)
        """
        eye_points = keypoints[eye_indices]
        
        x_coords = eye_points[:, 0]
        y_coords = eye_points[:, 1]
        
        x_min, x_max = np.min(x_coords), np.max(x_coords)
        y_min, y_max = np.min(y_coords), np.max(y_coords)
        
        # 眼部区域添加更大边距
        margin = 0.2
        width = x_max - x_min
        height = y_max - y_min
        
        x_min -= width * margin
        y_min -= height * margin
        width *= (1 + 2 * margin)
        height *= (1 + 2 * margin)
        
        return x_min, y_min, width, height
    
    def transform_to_local(self, keypoints: np.ndarray, reference_frame: Optional[np.ndarray] = None) -> np.ndarray:
        """
        将关键点转换为局部坐标系
        
        Args:
            keypoints: 当前帧关键点 [N, 2]
            reference_frame: 参考帧关键点（用于稳定局部坐标系）
            
        Returns:
            转换后的局部坐标
        """
        if reference_frame is None:
            reference_frame = keypoints
            
        if self.transform_type == 'face':
            # 使用人脸边界框进行归一化
            x_min, y_min, width, height = self.get_face_bbox(reference_frame)
            
            # 转换为相对坐标 [0, 1]
            local_coords = np.copy(keypoints)
            local_coords[:, 0] = (keypoints[:, 0] - x_min) / width
            local_coords[:, 1] = (keypoints[:, 1] - y_min) / height
            
        elif self.transform_type == 'eye':
            # 使用眼部中心作为原点
            # 这里假设前几个点是眼部关键点，实际应根据具体关键点定义调整
            eye_indices = list(range(min(6, len(keypoints))))  # 假设前6个是眼部点
            
            if len(eye_indices) > 0:
                eye_center = np.mean(reference_frame[eye_indices], axis=0)
                
                # 计算眼部区域的平均尺度
                eye_points = reference_frame[eye_indices]
                scale = np.std(eye_points, axis=0).mean()
                
                # 转换为以眼部中心为原点的相对坐标
                local_coords = (keypoints - eye_center) / (scale + 1e-6)
            else:
                local_coords = keypoints
                
        else:
            raise ValueError(f"不支持的转换类型: {self.transform_type}")
            
        return local_coords
    
    def transform_back_to_global(self, local_coords: np.ndarray, reference_frame: np.ndarray) -> np.ndarray:
        """
        将局部坐标转换回全局坐标
        
        Args:
            local_coords: 局部坐标 [N, 2]
            reference_frame: 参考帧关键点
            
        Returns:
            全局坐标
        """
        if self.transform_type == 'face':
            x_min, y_min, width, height = self.get_face_bbox(reference_frame)
            
            global_coords = np.copy(local_coords)
            global_coords[:, 0] = local_coords[:, 0] * width + x_min
            global_coords[:, 1] = local_coords[:, 1] * height + y_min
            
        elif self.transform_type == 'eye':
            eye_indices = list(range(min(6, len(reference_frame))))
            
            if len(eye_indices) > 0:
                eye_center = np.mean(reference_frame[eye_indices], axis=0)
                eye_points = reference_frame[eye_indices]
                scale = np.std(eye_points, axis=0).mean()
                
                global_coords = local_coords * (scale + 1e-6) + eye_center
            else:
                global_coords = local_coords
                
        return global_coords

def convert_keypoints_file(input_file: str, output_file: str, transform_type: str = 'face'):
    """
    转换关键点文件到局部坐标系
    
    Args:
        input_file: 输入的关键点文件（JSON格式）
        output_file: 输出文件路径
        transform_type: 转换类型 ('face' 或 'eye')
    """
    transformer = CoordinateTransformer(transform_type)
    
    # 加载关键点数据
    with open(input_file, 'r') as f:
        data = json.load(f)
    
    converted_data = []
    reference_frame = None
    
    for frame_data in data:
        if 'keypoints' in frame_data:
            keypoints = np.array(frame_data['keypoints'])
            
            # 使用第一帧作为参考帧
            if reference_frame is None:
                reference_frame = keypoints
            
            # 转换到局部坐标系
            local_coords = transformer.transform_to_local(keypoints, reference_frame)
            
            # 更新数据
            frame_data_copy = frame_data.copy()
            frame_data_copy['keypoints'] = local_coords.tolist()
            frame_data_copy['original_keypoints'] = keypoints.tolist()  # 保存原始坐标
            frame_data_copy['coordinate_system'] = transform_type
            
            converted_data.append(frame_data_copy)
    
    # 保存转换后的数据
    with open(output_file, 'w') as f:
        json.dump(converted_data, f, indent=2)
    
    print(f"已将关键点转换为{transform_type}局部坐标系")
    print(f"转换后数据保存到: {output_file}")
    print(f"处理了 {len(converted_data)} 帧数据")

def analyze_coordinate_stability(keypoints_sequence: List[np.ndarray], transform_type: str = 'face'):
    """
    分析不同坐标系下的稳定性
    
    Args:
        keypoints_sequence: 关键点序列
        transform_type: 转换类型
    """
    transformer = CoordinateTransformer(transform_type)
    
    # 全局坐标系下的运动
    global_velocities = []
    for i in range(1, len(keypoints_sequence)):
        velocity = np.linalg.norm(keypoints_sequence[i] - keypoints_sequence[i-1], axis=1)
        global_velocities.append(velocity.mean())
    
    # 局部坐标系下的运动
    reference_frame = keypoints_sequence[0]
    local_velocities = []
    prev_local = transformer.transform_to_local(keypoints_sequence[0], reference_frame)
    
    for i in range(1, len(keypoints_sequence)):
        current_local = transformer.transform_to_local(keypoints_sequence[i], reference_frame)
        velocity = np.linalg.norm(current_local - prev_local, axis=1)
        local_velocities.append(velocity.mean())
        prev_local = current_local
    
    print(f"\n=== {transform_type}坐标系分析 ===")
    print(f"全局坐标系平均速度: {np.mean(global_velocities):.4f}")
    print(f"局部坐标系平均速度: {np.mean(local_velocities):.4f}")
    print(f"速度降幅: {(1 - np.mean(local_velocities) / np.mean(global_velocities)) * 100:.1f}%")
    
    return {
        'global_velocity': np.mean(global_velocities),
        'local_velocity': np.mean(local_velocities),
        'reduction_ratio': 1 - np.mean(local_velocities) / np.mean(global_velocities)
    }

def main():
    parser = argparse.ArgumentParser(description='关键点坐标系转换工具')
    parser.add_argument('--input', required=True, help='输入关键点文件路径')
    parser.add_argument('--output', required=True, help='输出文件路径')
    parser.add_argument('--type', choices=['face', 'eye'], default='face', 
                       help='转换类型: face(人脸坐标系) 或 eye(眼部坐标系)')
    parser.add_argument('--analyze', action='store_true', help='分析坐标系稳定性')
    
    args = parser.parse_args()
    
    if args.analyze:
        # 这里需要根据实际数据格式调整
        print("坐标系稳定性分析功能需要根据具体数据格式实现")
    else:
        convert_keypoints_file(args.input, args.output, args.type)

if __name__ == '__main__':
    main() 