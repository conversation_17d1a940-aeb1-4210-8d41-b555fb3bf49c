#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的坐标系诊断工具
快速验证坐标系选择对滤波效果的影响
"""

import numpy as np
import pandas as pd
import argparse
from datetime import datetime

def load_quality_data(csv_file):
    """加载质量评估数据"""
    try:
        data = pd.read_csv(csv_file, skiprows=2, 
                          names=['时间戳', '帧ID', '平均标准差', '最大标准差', '平均速度', '最大速度', '平滑度', '稳定性'])
        return data
    except Exception as e:
        print(f"读取文件 {csv_file} 失败: {e}")
        return None

def analyze_motion_patterns(data):
    """分析运动模式"""
    if data is None or data.empty:
        return None
    
    # 速度分析
    avg_speed = data['平均速度'].mean()
    max_speed = data['最大速度'].mean()
    speed_std = data['平均速度'].std()
    
    # 判断运动特征
    motion_analysis = {
        '平均速度': avg_speed,
        '最大速度': max_speed,  
        '速度标准差': speed_std,
        '速度变异系数': speed_std / (avg_speed + 1e-6)
    }
    
    return motion_analysis

def diagnose_coordinate_system_problem(original_data, filtered_data):
    """诊断坐标系问题"""
    
    print("=" * 60)
    print("坐标系问题快速诊断")
    print("=" * 60)
    print(f"诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 分析原始数据
    orig_analysis = analyze_motion_patterns(original_data)
    filt_analysis = analyze_motion_patterns(filtered_data)
    
    if orig_analysis is None or filt_analysis is None:
        print("❌ 数据分析失败")
        return
    
    print("1. 运动特征分析")
    print("-" * 30)
    print(f"原始数据平均速度: {orig_analysis['平均速度']:.2f} 像素/帧")
    print(f"原始数据最大速度: {orig_analysis['最大速度']:.2f} 像素/帧")
    print(f"速度变异系数: {orig_analysis['速度变异系数']:.3f}")
    print()
    
    # 判断是否存在大幅度整体运动
    high_speed_threshold = 5.0  # 5像素/帧
    very_high_speed_threshold = 15.0  # 15像素/帧
    
    print("2. 坐标系问题诊断")
    print("-" * 30)
    
    if orig_analysis['平均速度'] > very_high_speed_threshold:
        print("🚨 严重问题：检测到大幅度运动！")
        print(f"   平均速度 {orig_analysis['平均速度']:.1f} 像素/帧 远超正常范围")
        print("   这很可能是人脸整体运动导致的")
        problem_severity = "严重"
    elif orig_analysis['平均速度'] > high_speed_threshold:
        print("⚠️  可能问题：运动幅度较大")
        print(f"   平均速度 {orig_analysis['平均速度']:.1f} 像素/帧 偏高")
        print("   可能包含较多人脸整体运动")
        problem_severity = "中等"
    else:
        print("✅ 运动幅度正常")
        print(f"   平均速度 {orig_analysis['平均速度']:.1f} 像素/帧 在合理范围内")
        problem_severity = "轻微"
    
    print()
    
    # 分析滤波效果
    speed_reduction = (orig_analysis['平均速度'] - filt_analysis['平均速度']) / orig_analysis['平均速度'] * 100
    
    print("3. 滤波效果分析")
    print("-" * 30)
    print(f"速度减少率: {speed_reduction:.1f}%")
    
    if speed_reduction > 60:
        print("📊 数值指标：显著改善")
        print("   滤波大幅降低了运动幅度")
    elif speed_reduction > 30:
        print("📊 数值指标：明显改善") 
    else:
        print("📊 数值指标：改善有限")
    
    print()
    
    # 综合诊断
    print("4. 综合诊断结果")
    print("-" * 30)
    
    if problem_severity == "严重" and speed_reduction > 50:
        print("🎯 问题确认：您的猜测很可能正确！")
        print()
        print("原因分析：")
        print("• 关键点运动幅度过大，主要来自人脸整体移动")
        print("• 卡尔曼滤波将正常的人脸运动误认为噪声")
        print("• 数值上看起来改善了，但实际破坏了真实运动")
        print()
        print("💡 强烈建议：")
        print("1. 转换为人脸局部坐标系进行滤波")
        print("2. 或使用眼部局部坐标系（如果主要关注眼部）")
        print("3. 先进行人脸对齐，消除整体运动影响")
        
    elif problem_severity in ["严重", "中等"]:
        print("⚠️  可能存在坐标系问题")
        print()
        print("建议尝试：")
        print("1. 使用局部坐标系进行滤波")
        print("2. 对比不同坐标系的视觉效果")
        print("3. 调整滤波参数减小过度平滑")
        
    else:
        print("✅ 坐标系选择可能合理")
        print("   问题可能出在其他方面：")
        print("   • 滤波参数设置")
        print("   • 评估指标与视觉感知的差异")
        print("   • 滤波算法的时间延迟")
    
    print()
    
    # 输出数值证据
    print("5. 关键数值证据")
    print("-" * 30)
    print(f"平均运动幅度: {orig_analysis['平均速度']:.2f} 像素/帧")
    print(f"最大运动幅度: {orig_analysis['最大速度']:.2f} 像素/帧")
    print(f"滤波后平均幅度: {filt_analysis['平均速度']:.2f} 像素/帧")
    print(f"减少幅度: {speed_reduction:.1f}%")
    
    # 速度阈值参考
    print()
    print("参考标准：")
    print("• < 3像素/帧：正常关键点微调")
    print("• 3-8像素/帧：中等幅度运动") 
    print("• > 8像素/帧：可能包含人脸整体运动")
    print("• > 15像素/帧：很可能是坐标系问题")
    
    return {
        '问题严重程度': problem_severity,
        '速度减少率': speed_reduction,
        '平均速度': orig_analysis['平均速度'],
        '坐标系问题可能性': problem_severity in ["严重", "中等"]
    }

def main():
    parser = argparse.ArgumentParser(description='快速坐标系问题诊断')
    parser.add_argument('--raw', required=True, help='原始质量数据CSV文件')
    parser.add_argument('--filtered', required=True, help='滤波后质量数据CSV文件')
    
    args = parser.parse_args()
    
    # 加载数据
    print("正在加载数据...")
    original_data = load_quality_data(args.raw)
    filtered_data = load_quality_data(args.filtered)
    
    # 进行诊断
    diagnosis = diagnose_coordinate_system_problem(original_data, filtered_data)
    
    if diagnosis and diagnosis['坐标系问题可能性']:
        print("\n" + "="*60)
        print("🎯 结论：坐标系问题的可能性很高！")
        print("建议立即尝试局部坐标系方案")
        print("="*60)

if __name__ == '__main__':
    main() 