{"comment": "卡尔曼滤波器配置示例", "examples": {"face_keypoints_filter": {"class_name": "Keypoints<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config": {"num_keypoints": 19, "process_noise": 0.0001, "measurement_noise": 0.1, "score_threshold": 0.1}, "name": "face_keypoints_kalman", "inbound_nodes": [["face_keypoints_decoder", 0, 0, {}]]}, "left_eye_keypoints_filter": {"class_name": "Keypoints<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config": {"num_keypoints": 17, "process_noise": 5e-05, "measurement_noise": 0.08, "score_threshold": 0.15}, "name": "left_eye_kalman", "inbound_nodes": [["left_eye_keypoints_decoder", 0, 0, {}]]}, "right_eye_keypoints_filter": {"class_name": "Keypoints<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config": {"num_keypoints": 17, "process_noise": 5e-05, "measurement_noise": 0.08, "score_threshold": 0.15}, "name": "right_eye_kalman", "inbound_nodes": [["right_eye_keypoints_decoder", 0, 0, {}]]}}, "parameter_description": {"num_keypoints": "关键点数量 - 人脸19个点，眼睛17个点", "process_noise": "过程噪声 - 控制模型预测的不确定性，值越小越信任模型", "measurement_noise": "测量噪声 - 控制对观测值的信任度，值越小越信任观测", "score_threshold": "置信度阈值 - 低于此值的检测结果将被忽略，使用预测值"}, "tuning_tips": {"smooth_tracking": "如需更平滑的跟踪，减小process_noise", "responsive_tracking": "如需更快响应，增大process_noise或减小measurement_noise", "noisy_detection": "如果检测结果噪声大，增大measurement_noise", "stable_detection": "如果检测结果稳定，减小measurement_noise"}}