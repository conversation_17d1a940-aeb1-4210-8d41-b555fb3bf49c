# 关键点卡尔曼滤波器使用指南

## 概述

本项目提供了一套完整的关键点卡尔曼滤波解决方案，包括：
- **CcKeypointsKalmanFilter**: 卡尔曼滤波器模块，用于平滑关键点轨迹
- **CcKeypointsQualityEvaluator**: 质量评估模块，用于评估滤波效果
- **analyze_keypoints_quality.py**: Python分析工具，用于对比滤波前后的效果

## 功能特点

### 卡尔曼滤波器 (CcKeypointsKalmanFilter)
- ✅ 支持任意数量的关键点（人脸19个点，眼睛17个点）
- ✅ 恒定速度模型，适合跟踪平滑运动
- ✅ 可配置的过程噪声和测量噪声参数
- ✅ 自动处理低置信度检测结果
- ✅ 符合项目标准的CcModule格式

### 质量评估器 (CcKeypointsQualityEvaluator)
- ✅ 实时计算关键点稳定性指标
- ✅ 运动平滑度分析
- ✅ 自动生成CSV日志文件
- ✅ 透传模式，不影响数据流

## 快速开始

### 1. 配置卡尔曼滤波器

在你的配置JSON文件中添加：

```json
{
    "class_name": "KeypointsKalmanFilter",
    "config": {
        "num_keypoints": 19,           // 关键点数量
        "process_noise": 1e-4,         // 过程噪声
        "measurement_noise": 1e-1,     // 测量噪声  
        "score_threshold": 0.1         // 置信度阈值
    },
    "name": "face_keypoints_kalman",
    "inbound_nodes": [
        ["face_keypoints_decoder", 0, 0, {}]
    ]
}
```

### 2. 配置质量评估器

```json
{
    "class_name": "KeypointsQualityEvaluator",
    "config": {
        "num_keypoints": 19,
        "window_size": 50,             // 滑动窗口大小
        "score_threshold": 0.1,
        "enable_logging": true,        // 启用日志记录
        "log_file_path": "/tmp/keypoints_quality.csv"
    },
    "name": "quality_evaluator",
    "inbound_nodes": [
        ["face_keypoints_kalman", 0, 0, {}]
    ]
}
```

### 3. 对比测试配置

参考 `keypoints_filter_comparison_example.json` 文件，配置同时评估原始和滤波后的关键点：

```
原始关键点 → 质量评估器1 → 卡尔曼滤波器 → 质量评估器2
```

## 参数调优指南

### 过程噪声 (process_noise)
- **作用**: 控制对模型预测的信任度
- **默认值**: 1e-4
- **调优建议**:
  - 减小 → 更平滑，但响应性差
  - 增大 → 响应性好，但可能不够平滑

### 测量噪声 (measurement_noise)  
- **作用**: 控制对观测值的信任度
- **默认值**: 1e-1
- **调优建议**:
  - 减小 → 更信任观测，响应快
  - 增大 → 更依赖预测，更平滑

### 置信度阈值 (score_threshold)
- **作用**: 低于此值的检测结果将被忽略
- **默认值**: 0.1
- **调优建议**: 根据检测算法的置信度分布调整

## 效果评估

### 使用Python分析工具

```bash
# 安装依赖
pip install pandas numpy matplotlib

# 运行分析
python analyze_keypoints_quality.py \
    --raw /tmp/face_keypoints_raw_quality.csv \
    --filtered /tmp/face_keypoints_filtered_quality.csv \
    --output ./results/
```

### 评估指标说明

| 指标 | 说明 | 期望变化 |
|------|------|----------|
| 平均标准差 | 关键点分散度 | 滤波后减小 ⬇️ |
| 最大标准差 | 最不稳定时刻 | 滤波后减小 ⬇️ |
| 平均速度 | 关键点移动速度 | 滤波后减小 ⬇️ |
| 最大速度 | 最剧烈运动 | 滤波后减小 ⬇️ |
| 平滑度分数 | 运动平滑度(0-1) | 滤波后提高 ⬆️ |
| 稳定性分数 | 整体稳定性(0-1) | 滤波后提高 ⬆️ |

## 常见问题

### Q: 滤波后关键点过于平滑，响应性差？
**A**: 增大 `process_noise` 或减小 `measurement_noise`

### Q: 滤波后仍然有抖动？
**A**: 减小 `process_noise` 或增大 `measurement_noise`

### Q: 滤波后有明显延迟？
**A**: 减小 `measurement_noise`，增强对观测的信任

### Q: 如何处理遮挡情况？
**A**: 卡尔曼滤波器会自动使用预测值，当置信度低于阈值时

## 实际应用案例

### 人脸关键点 (19个点)
```json
{
    "num_keypoints": 19,
    "process_noise": 1e-4,
    "measurement_noise": 1e-1,
    "score_threshold": 0.1
}
```

### 眼睛关键点 (17个点)
```json
{
    "num_keypoints": 17,
    "process_noise": 5e-5,      // 眼睛运动更精细，噪声更小
    "measurement_noise": 8e-2,
    "score_threshold": 0.15     // 眼睛检测要求更高置信度
}
```

## 性能考虑

- **内存使用**: 每个关键点需要4个状态变量 (x, y, vx, vy)
- **计算复杂度**: O(n³) 其中n为状态维度 (关键点数×4)
- **实时性**: 适合实时应用，单帧处理时间 < 1ms

## 编译和集成

1. 确保文件已添加到 `CMakeLists.txt`:
```cmake
tongxing_util/src/module/postprocess/keypoints_kalman_filter.cpp
tongxing_util/src/module/postprocess/keypoints_quality_evaluator.cpp
```

2. 编译项目:
```bash
make -j4
```

3. 在配置文件中使用模块名:
- `KeypointsKalmanFilter`
- `KeypointsQualityEvaluator`

## 技术细节

### 状态向量
对于n个关键点，状态向量为：
```
x = [x₁, y₁, vx₁, vy₁, x₂, y₂, vx₂, vy₂, ..., xₙ, yₙ, vxₙ, vyₙ]ᵀ
```

### 状态转移模型
```
x(k+1) = F × x(k) + w(k)
```
其中F为状态转移矩阵，w(k)为过程噪声

### 观测模型
```
z(k) = H × x(k) + v(k)
```
其中H为观测矩阵，v(k)为测量噪声

## 更新日志

- **v1.0**: 初始版本，支持基本卡尔曼滤波
- **v1.1**: 添加质量评估模块
- **v1.2**: 添加Python分析工具
- **v1.3**: 完善文档和示例配置

## 联系方式

如有问题或建议，请联系开发团队。 