#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的关键点质量评估工具
增加更贴近人眼感知的评估指标
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import argparse
import os
import json
from datetime import datetime
from typing import List, Dict, Tuple
from scipy import signal
from scipy.spatial.distance import pdist, squareform

class ImprovedQualityAssessment:
    """改进的质量评估类"""
    
    def __init__(self):
        self.metrics = {}
        
    def calculate_perceptual_jitter(self, keypoints_sequence: List[np.ndarray], fps: float = 30.0) -> float:
        """
        计算感知抖动 - 基于人眼对高频运动的敏感性
        
        Args:
            keypoints_sequence: 关键点序列
            fps: 帧率
            
        Returns:
            感知抖动分数 (0-1，越低越好)
        """
        if len(keypoints_sequence) < 3:
            return 0.0
            
        # 计算每个关键点的位移
        displacements = []
        for i in range(1, len(keypoints_sequence)):
            displacement = np.linalg.norm(keypoints_sequence[i] - keypoints_sequence[i-1], axis=1)
            displacements.append(displacement)
        
        displacements = np.array(displacements)  # [frames-1, num_keypoints]
        
        # 对每个关键点计算高频成分
        jitter_scores = []
        for kp_idx in range(displacements.shape[1]):
            signal_data = displacements[:, kp_idx]
            
            # 使用高通滤波器检测高频抖动
            if len(signal_data) > 10:
                # 设计高通滤波器 (截止频率为5Hz，对于30fps)
                cutoff_freq = 5.0 / (fps / 2.0)  # 归一化频率
                b, a = signal.butter(3, cutoff_freq, btype='high')
                
                # 应用滤波器
                high_freq_component = signal.filtfilt(b, a, signal_data)
                
                # 计算高频成分的能量
                jitter_energy = np.sqrt(np.mean(high_freq_component**2))
                jitter_scores.append(jitter_energy)
            else:
                jitter_scores.append(np.std(signal_data))
        
        # 返回平均抖动分数
        return np.mean(jitter_scores)
    
    def calculate_motion_continuity(self, keypoints_sequence: List[np.ndarray]) -> float:
        """
        计算运动连续性 - 评估运动的自然流畅度
        
        Args:
            keypoints_sequence: 关键点序列
            
        Returns:
            连续性分数 (0-1，越高越好)
        """
        if len(keypoints_sequence) < 4:
            return 1.0
            
        # 计算二阶差分（加速度）
        accelerations = []
        for i in range(2, len(keypoints_sequence)):
            vel_curr = keypoints_sequence[i] - keypoints_sequence[i-1]
            vel_prev = keypoints_sequence[i-1] - keypoints_sequence[i-2]
            acceleration = vel_curr - vel_prev
            
            # 计算加速度的幅值
            acc_magnitude = np.linalg.norm(acceleration, axis=1)
            accelerations.append(acc_magnitude.mean())
        
        # 连续性与加速度变化成反比
        acc_std = np.std(accelerations)
        continuity_score = 1.0 / (1.0 + acc_std * 10)  # 归一化到0-1
        
        return continuity_score
    
    def calculate_natural_frequency_preservation(self, keypoints_sequence: List[np.ndarray], fps: float = 30.0) -> float:
        """
        计算自然频率保持度 - 评估是否保持了面部运动的自然频率
        
        Args:
            keypoints_sequence: 关键点序列
            fps: 帧率
            
        Returns:
            频率保持度分数 (0-1，越高越好)
        """
        if len(keypoints_sequence) < 20:
            return 1.0
            
        # 计算关键点中心的运动轨迹
        centers = [np.mean(kp, axis=0) for kp in keypoints_sequence]
        trajectory = np.array(centers)
        
        # 分别分析X和Y方向的频率特性
        freq_scores = []
        for dim in range(2):
            signal_data = trajectory[:, dim]
            
            # 计算功率谱密度
            freqs, psd = signal.welch(signal_data, fs=fps, nperseg=min(64, len(signal_data)//2))
            
            # 面部运动的自然频率范围通常在0.5-8Hz
            natural_freq_mask = (freqs >= 0.5) & (freqs <= 8.0)
            total_power = np.sum(psd)
            natural_power = np.sum(psd[natural_freq_mask])
            
            if total_power > 0:
                freq_ratio = natural_power / total_power
                freq_scores.append(freq_ratio)
            else:
                freq_scores.append(1.0)
        
        return np.mean(freq_scores)
    
    def calculate_spatial_consistency(self, keypoints_sequence: List[np.ndarray]) -> float:
        """
        计算空间一致性 - 评估关键点之间的相对位置关系是否保持
        
        Args:
            keypoints_sequence: 关键点序列
            
        Returns:
            空间一致性分数 (0-1，越高越好)
        """
        if len(keypoints_sequence) < 2:
            return 1.0
            
        # 计算每帧关键点之间的距离矩阵
        distance_matrices = []
        for keypoints in keypoints_sequence:
            if len(keypoints) > 1:
                distances = pdist(keypoints)
                distance_matrices.append(distances)
        
        if len(distance_matrices) < 2:
            return 1.0
            
        # 计算距离矩阵的变化
        distance_variations = []
        for i in range(1, len(distance_matrices)):
            # 归一化距离矩阵以处理尺度变化
            curr_dist = distance_matrices[i]
            prev_dist = distance_matrices[i-1]
            
            if np.mean(prev_dist) > 0 and np.mean(curr_dist) > 0:
                curr_normalized = curr_dist / np.mean(curr_dist)
                prev_normalized = prev_dist / np.mean(prev_dist)
                
                variation = np.abs(curr_normalized - prev_normalized)
                distance_variations.append(np.mean(variation))
        
        # 一致性与变化成反比
        if distance_variations:
            avg_variation = np.mean(distance_variations)
            consistency_score = 1.0 / (1.0 + avg_variation * 5)
        else:
            consistency_score = 1.0
            
        return consistency_score
    
    def calculate_response_delay(self, original_sequence: List[np.ndarray], 
                               filtered_sequence: List[np.ndarray]) -> float:
        """
        计算响应延迟 - 评估滤波引入的时间延迟
        
        Args:
            original_sequence: 原始关键点序列
            filtered_sequence: 滤波后关键点序列
            
        Returns:
            延迟帧数
        """
        if len(original_sequence) != len(filtered_sequence) or len(original_sequence) < 10:
            return 0.0
            
        # 计算两个序列的互相关
        orig_movement = []
        filt_movement = []
        
        for i in range(1, len(original_sequence)):
            orig_move = np.linalg.norm(original_sequence[i] - original_sequence[i-1])
            filt_move = np.linalg.norm(filtered_sequence[i] - filtered_sequence[i-1])
            orig_movement.append(orig_move)
            filt_movement.append(filt_move)
        
        # 计算互相关来检测延迟
        correlation = np.correlate(filt_movement, orig_movement, mode='full')
        delay = len(orig_movement) - 1 - np.argmax(correlation)
        
        return abs(delay)
    
    def comprehensive_assessment(self, original_sequence: List[np.ndarray], 
                               filtered_sequence: List[np.ndarray], 
                               fps: float = 30.0) -> Dict[str, float]:
        """
        综合质量评估
        
        Args:
            original_sequence: 原始关键点序列
            filtered_sequence: 滤波后关键点序列
            fps: 帧率
            
        Returns:
            评估结果字典
        """
        results = {}
        
        # 1. 感知抖动
        orig_jitter = self.calculate_perceptual_jitter(original_sequence, fps)
        filt_jitter = self.calculate_perceptual_jitter(filtered_sequence, fps)
        results['感知抖动_原始'] = orig_jitter
        results['感知抖动_滤波'] = filt_jitter
        results['抖动改善率'] = (orig_jitter - filt_jitter) / (orig_jitter + 1e-6) * 100
        
        # 2. 运动连续性
        orig_continuity = self.calculate_motion_continuity(original_sequence)
        filt_continuity = self.calculate_motion_continuity(filtered_sequence)
        results['运动连续性_原始'] = orig_continuity
        results['运动连续性_滤波'] = filt_continuity
        results['连续性变化率'] = (filt_continuity - orig_continuity) / (orig_continuity + 1e-6) * 100
        
        # 3. 自然频率保持
        orig_freq = self.calculate_natural_frequency_preservation(original_sequence, fps)
        filt_freq = self.calculate_natural_frequency_preservation(filtered_sequence, fps)
        results['频率保持_原始'] = orig_freq
        results['频率保持_滤波'] = filt_freq
        results['频率保持变化率'] = (filt_freq - orig_freq) / (orig_freq + 1e-6) * 100
        
        # 4. 空间一致性
        orig_consistency = self.calculate_spatial_consistency(original_sequence)
        filt_consistency = self.calculate_spatial_consistency(filtered_sequence)
        results['空间一致性_原始'] = orig_consistency
        results['空间一致性_滤波'] = filt_consistency
        results['一致性变化率'] = (filt_consistency - orig_consistency) / (orig_consistency + 1e-6) * 100
        
        # 5. 响应延迟
        delay = self.calculate_response_delay(original_sequence, filtered_sequence)
        results['响应延迟_帧数'] = delay
        results['响应延迟_毫秒'] = delay * 1000 / fps
        
        # 6. 综合感知质量分数
        # 权重：抖动减少(30%) + 连续性保持(25%) + 频率保持(20%) + 一致性保持(15%) + 延迟惩罚(10%)
        jitter_score = min(1.0, max(0.0, results['抖动改善率'] / 50))  # 50%改善为满分
        continuity_score = max(0.0, 1.0 - abs(results['连续性变化率']) / 20)  # 变化小于20%为满分
        freq_score = max(0.0, 1.0 - abs(results['频率保持变化率']) / 20)
        consistency_score = max(0.0, 1.0 - abs(results['一致性变化率']) / 20)
        delay_score = max(0.0, 1.0 - delay / 5)  # 延迟5帧以上为0分
        
        overall_score = (jitter_score * 0.3 + 
                        continuity_score * 0.25 + 
                        freq_score * 0.2 + 
                        consistency_score * 0.15 + 
                        delay_score * 0.1)
        
        results['综合感知质量分数'] = overall_score * 100  # 转换为0-100分
        
        return results

def load_keypoints_from_json(json_file: str) -> List[np.ndarray]:
    """从JSON文件加载关键点序列"""
    with open(json_file, 'r') as f:
        data = json.load(f)
    
    keypoints_sequence = []
    for frame_data in data:
        if 'keypoints' in frame_data:
            keypoints = np.array(frame_data['keypoints'])
            keypoints_sequence.append(keypoints)
    
    return keypoints_sequence

def generate_improved_report(original_seq: List[np.ndarray], 
                           filtered_seq: List[np.ndarray],
                           output_dir: str = "./",
                           fps: float = 30.0):
    """生成改进的质量评估报告"""
    
    assessor = ImprovedQualityAssessment()
    results = assessor.comprehensive_assessment(original_seq, filtered_seq, fps)
    
    # 生成报告
    report_file = os.path.join(output_dir, f"improved_quality_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("改进的关键点质量评估报告\n")
        f.write("=" * 60 + "\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"评估帧数: {len(original_seq)} 帧\n")
        f.write(f"视频帧率: {fps} FPS\n\n")
        
        f.write("=" * 60 + "\n")
        f.write("详细评估结果\n")
        f.write("=" * 60 + "\n\n")
        
        # 感知抖动
        f.write("1. 感知抖动分析\n")
        f.write("-" * 30 + "\n")
        f.write(f"原始抖动程度: {results['感知抖动_原始']:.4f}\n")
        f.write(f"滤波后抖动:   {results['感知抖动_滤波']:.4f}\n")
        f.write(f"抖动改善率:   {results['抖动改善率']:+.1f}%\n")
        if results['抖动改善率'] > 30:
            f.write("✅ 抖动改善显著\n")
        elif results['抖动改善率'] > 10:
            f.write("⚠️  抖动改善一般\n")
        else:
            f.write("❌ 抖动改善不明显\n")
        f.write("\n")
        
        # 运动连续性
        f.write("2. 运动连续性分析\n")
        f.write("-" * 30 + "\n")
        f.write(f"原始连续性: {results['运动连续性_原始']:.4f}\n")
        f.write(f"滤波后连续性: {results['运动连续性_滤波']:.4f}\n")
        f.write(f"连续性变化: {results['连续性变化率']:+.1f}%\n")
        if abs(results['连续性变化率']) < 10:
            f.write("✅ 连续性保持良好\n")
        elif abs(results['连续性变化率']) < 20:
            f.write("⚠️  连续性轻微变化\n")
        else:
            f.write("❌ 连续性变化较大\n")
        f.write("\n")
        
        # 自然频率保持
        f.write("3. 自然频率保持分析\n")
        f.write("-" * 30 + "\n")
        f.write(f"原始频率特性: {results['频率保持_原始']:.4f}\n")
        f.write(f"滤波后频率:   {results['频率保持_滤波']:.4f}\n")
        f.write(f"频率保持变化: {results['频率保持变化率']:+.1f}%\n")
        if abs(results['频率保持变化率']) < 10:
            f.write("✅ 自然频率保持良好\n")
        elif abs(results['频率保持变化率']) < 25:
            f.write("⚠️  频率特性轻微变化\n")
        else:
            f.write("❌ 频率特性变化较大，可能过度滤波\n")
        f.write("\n")
        
        # 空间一致性
        f.write("4. 空间一致性分析\n")
        f.write("-" * 30 + "\n")
        f.write(f"原始一致性: {results['空间一致性_原始']:.4f}\n")
        f.write(f"滤波后一致性: {results['空间一致性_滤波']:.4f}\n")
        f.write(f"一致性变化: {results['一致性变化率']:+.1f}%\n")
        if abs(results['一致性变化率']) < 5:
            f.write("✅ 空间一致性保持优秀\n")
        elif abs(results['一致性变化率']) < 15:
            f.write("⚠️  空间一致性轻微变化\n")
        else:
            f.write("❌ 空间关系变化较大\n")
        f.write("\n")
        
        # 响应延迟
        f.write("5. 响应延迟分析\n")
        f.write("-" * 30 + "\n")
        f.write(f"延迟帧数: {results['响应延迟_帧数']:.1f} 帧\n")
        f.write(f"延迟时间: {results['响应延迟_毫秒']:.1f} 毫秒\n")
        if results['响应延迟_帧数'] < 1:
            f.write("✅ 响应延迟很低\n")
        elif results['响应延迟_帧数'] < 3:
            f.write("⚠️  有轻微延迟\n")
        else:
            f.write("❌ 延迟较明显，影响实时性\n")
        f.write("\n")
        
        # 综合评分
        f.write("=" * 60 + "\n")
        f.write("综合评估结果\n")
        f.write("=" * 60 + "\n")
        f.write(f"综合感知质量分数: {results['综合感知质量分数']:.1f}/100\n\n")
        
        score = results['综合感知质量分数']
        if score >= 80:
            f.write("🎉 优秀！滤波效果很好，明显改善了关键点质量\n")
        elif score >= 60:
            f.write("👍 良好！滤波有一定效果，但还有改进空间\n")
        elif score >= 40:
            f.write("⚠️  一般！滤波效果不够理想，建议调整参数\n")
        else:
            f.write("❌ 较差！滤波可能产生了负面影响，建议重新评估方案\n")
        
        f.write("\n" + "=" * 60 + "\n")
        f.write("改进建议\n")
        f.write("=" * 60 + "\n")
        
        if results['抖动改善率'] < 20:
            f.write("• 抖动改善不明显，考虑增大process_noise参数或使用局部坐标系\n")
        
        if results['频率保持变化率'] < -20:
            f.write("• 自然频率损失较大，可能过度滤波，建议减小measurement_noise参数\n")
        
        if results['响应延迟_帧数'] > 2:
            f.write("• 响应延迟较大，考虑使用在线滤波算法或减小滤波强度\n")
        
        if abs(results['连续性变化率']) > 15:
            f.write("• 运动连续性变化较大，建议调整状态转移模型\n")
        
        f.write("• 如果整体效果不佳，强烈建议转换为局部坐标系（人脸或眼部）进行滤波\n")
    
    print(f"改进的质量评估报告已保存到: {report_file}")
    return results

def main():
    parser = argparse.ArgumentParser(description='改进的关键点质量评估工具')
    parser.add_argument('--original', required=True, help='原始关键点序列文件(JSON)')
    parser.add_argument('--filtered', required=True, help='滤波后关键点序列文件(JSON)')
    parser.add_argument('--fps', type=float, default=30.0, help='视频帧率')
    parser.add_argument('--output', default='./', help='输出目录')
    
    args = parser.parse_args()
    
    # 加载数据
    print("正在加载关键点数据...")
    original_seq = load_keypoints_from_json(args.original)
    filtered_seq = load_keypoints_from_json(args.filtered)
    
    if len(original_seq) != len(filtered_seq):
        print("警告：原始序列和滤波序列长度不匹配")
        min_len = min(len(original_seq), len(filtered_seq))
        original_seq = original_seq[:min_len]
        filtered_seq = filtered_seq[:min_len]
    
    # 进行改进的质量评估
    print("正在进行改进的质量评估...")
    results = generate_improved_report(original_seq, filtered_seq, args.output, args.fps)
    
    print(f"\n评估完成！综合感知质量分数: {results['综合感知质量分数']:.1f}/100")

if __name__ == '__main__':
    main() 