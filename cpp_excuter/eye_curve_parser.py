import re
import ast
import csv
import argparse
from typing import Dict, List
from pathlib import Path

# 类型定义
EyeData = Dict[str, List[float]]  # {'upper': [], 'lower': []}
FrameData = Dict[str, EyeData]    # {'left': {}, 'right': {}}
ResultData = Dict[int, FrameData] # {frame_id: {}}

def parse_eye_curve_data(file_path: str) -> ResultData:
    """解析眼睛曲线数据文件"""
    result: ResultData = {}
    current_frame = None
    current_eye = None
    # Removed global image_path and eye_inner_dist
    try:
        with open(file_path, 'r') as f:
            for line in f:
                line = line.strip()
                if not line:
                    continue
                
                # Combined frame_id and file path parsing
                if frame_match := re.match(r'frame id:(\d+)', line):
                    current_frame = int(frame_match.group(1))
                    # print(f"{current_frame}=") # Optional: for debugging
                    result[current_frame] = {'left': {}, 'right': {}, 'image_path': None, 'eye_inner_dist': None}
                    
                    # Search for file path in the same line
                    if file_in_line_match := re.search(r'file:(.*)', line):
                        image_path_val = file_in_line_match.group(1).strip()
                        result[current_frame]['image_path'] = image_path_val
                        # print(f"Image path for frame {current_frame}: {image_path_val}") # Optional: for debugging
                    continue # Continue to next line after processing frame id and file
                    
                if eye_match := re.match(r'is_(left|right)eye', line):
                    current_eye = eye_match.group(1)
                    continue

                if eye_inner_dist_match := re.match(r'eye_inner_dist:(.*)', line):
                    if current_frame is not None: # Ensure current_frame is set
                        eye_inner_dist_val = eye_inner_dist_match.group(1).strip()
                        result[current_frame]['eye_inner_dist'] = eye_inner_dist_val

                    continue

                if curve_match := re.match(r'(upper|lower) curve score list:(\[.*?\])', line):
                    if current_frame and current_eye:
                        curve_type = curve_match.group(1)
                        try:
                            scores = ast.literal_eval(curve_match.group(2))
                            result[current_frame][current_eye][curve_type] = scores
                        except (ValueError, SyntaxError):
                            print(f"Warning: Invalid score format in line: {line}")
    
    except FileNotFoundError:
        print(f"Error: File not found: {file_path}")
        return {}
    except Exception as e:
        print(f"Unexpected error: {str(e)}")
        return {}
    
    return result

def save_to_csv(data: ResultData, output_path: str) -> bool:
    """将解析结果保存为CSV文件"""
    # Removed global image_path and eye_inner_dist
    try:
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', newline='') as f:
            writer = csv.writer(f)
            # 写入表头
            writer.writerow([
                'frame_id', 'eye_type','image_path', 'eye_inner_dist',
                'upper_curve_score_1', 'upper_curve_score_2', 'upper_curve_score_3','upper_curve_score_4','upper_curve_score_5'
                # Add lower_curve_scores if needed, current header only has upper
            ])
            # 写入数据
            for frame_id, frame_content in sorted(data.items()): # Renamed eye_data to frame_content for clarity
                current_image_path = frame_content.get('image_path', '')
                current_eye_inner_dist = frame_content.get('eye_inner_dist', '')
                for eye_type in ['left', 'right']:
                    if eye_type in frame_content: # Check in frame_content, not eye_data
                        eye_specific_data = frame_content[eye_type]
                        upper_scores = eye_specific_data.get('upper', [])
                        # lower_scores = eye_specific_data.get('lower', []) # Uncomment if using lower scores
                        writer.writerow([
                            frame_id,
                            eye_type,
                            current_image_path,
                            current_eye_inner_dist,
                            upper_scores[0] if len(upper_scores) > 0 else '',
                            upper_scores[1] if len(upper_scores) > 1 else '',
                            upper_scores[2] if len(upper_scores) > 2 else '',
                            upper_scores[3] if len(upper_scores) > 3 else '',
                            upper_scores[4] if len(upper_scores) > 4 else ''
                            # lower_scores[0] if len(lower_scores) > 0 else '',
                            # lower_scores[1] if len(lower_scores) > 1 else '',
                            # lower_scores[2] if len(lower_scores) > 2 else '',
                            # lower_scores[3] if len(lower_scores) > 3 else '',
                            # lower_scores[4] if len(lower_scores) > 4 else ''
                        ])
        return True
    except Exception as e:
        print(f"Error saving to CSV: {str(e)}")
        return False

def main():
    """主函数，支持命令行参数"""
    parser = argparse.ArgumentParser(description='眼睛曲线数据解析器')
    parser.add_argument('-i', '--input', required=True, help='输入日志文件路径')
    parser.add_argument('-o', '--output', required=True, help='输出CSV文件路径')
    args = parser.parse_args()

    try:
        # 解析数据
        data = parse_eye_curve_data(args.input)
        
        # 保存到CSV
        if save_to_csv(data, args.output):
            print(f"数据已成功保存到: {args.output}")
        
    except Exception as e:
        print(f"Error occurred: {str(e)}")

if __name__ == '__main__':
    main()
