import re
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path

def analyze_curvatures(file_path: str):
    """提取、分析并可视化upper_curvature值"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取所有upper_curvature值
        curvatures = re.findall(r"upper_curvature:([0-9.]+)", content)
        
        if not curvatures:
            print("未找到upper_curvature数据")
            return

        # 转换为数值类型
        curvatures = [float(val) for val in curvatures]
        
        # 创建DataFrame
        df = pd.DataFrame(curvatures, columns=['upper_curvature'])
        
        # 输出原始数据
        # print("提取到的upper_curvature值(按出现顺序):")
        # for i, val in enumerate(curvatures, 1):
        #     print(f"{i}: {val}")
            
        # 输出统计信息
        print("\n统计分析结果:")
        print(df.describe())
        
        # 初步分析数据分布范围
        print("\n初步数据分布分析:")
        min_val = df['upper_curvature'].min()
        max_val = df['upper_curvature'].max()
        range_val = max_val - min_val
        print(f"最小值: {min_val:.6f}, 最大值: {max_val:.6f}, 范围: {range_val:.6f}")
        
        # 超精细分段统计(0.0,0.0003]分1000段
        fine_bins = [i*0.00003 for i in range(10)]
        print("\n超精细分布区间统计(0.0,0.0003]分1000段):")
        fine_dist = pd.cut(df['upper_curvature'], bins=fine_bins).value_counts().sort_index()
        
        # 只输出有数据的区间
        filtered_dist = fine_dist[fine_dist > 0]
        print(filtered_dist)
        
        # 同时提供标准统计量
        print("\n标准统计量:")
        print(df['upper_curvature'].describe(percentiles=[.01, .05, .25, .5, .75, .95, .99]))
        
        # 保存详细数据到CSV
        output_dir = Path(file_path).parent / 'analysis_results'
        output_dir.mkdir(exist_ok=True)
        csv_path = output_dir / 'detailed_distribution.csv'
        filtered_dist.to_csv(csv_path)
        print(f"\n详细分布数据已保存至: {csv_path}")
        
        # 创建输出目录
        output_dir = Path(file_path).parent / 'analysis_results'
        output_dir.mkdir(exist_ok=True)

        # 绘制直方图
        plt.figure(figsize=(12, 6))
        
        # 主图 - 直方图
        plt.subplot(1, 2, 1)
        plt.hist(curvatures, bins=bins, color='steelblue', edgecolor='black')
        plt.title('Upper Curvature 分布直方图')
        plt.xlabel('Upper Curvature 值')
        plt.ylabel('频数')
        plt.xticks(rotation=45)
        
        # 副图 - 箱线图
        plt.subplot(1, 2, 2)
        plt.boxplot(curvatures, vert=False)
        plt.title('数值分布箱线图')
        plt.xlabel('Upper Curvature 值')
        plt.yticks([])
        
        plt.tight_layout()
        
        # 保存图表
        plot_path = output_dir / 'curvature_distribution.png'
        plt.savefig(plot_path)
        print(f"\n分布图已保存至: {plot_path}")
            
    except FileNotFoundError:
        print(f"错误：文件 {file_path} 不存在")
    except Exception as e:
        print(f"处理过程中发生错误：{str(e)}")

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    else:
        input_file = input("请输入日志文件路径：")
    
    analyze_curvatures(input_file)