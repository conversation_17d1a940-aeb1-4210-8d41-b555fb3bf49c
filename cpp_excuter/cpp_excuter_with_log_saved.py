import time
import subprocess
import sys
import os

import hashlib
import threading

def call_cpp_program(cpp_excuter, parsetxt_paths, print_cpp_info=True):
    """执行C++程序并保存日志
    
    参数:
        cpp_excuter: C++可执行文件路径
        parsetxt_paths: 可以是字符串(单路径)或列表/元组(多路径)
        print_cpp_info: 是否打印程序输出
    """
    # 统一处理参数类型
    if isinstance(parsetxt_paths, str):
        parsetxt_paths = [parsetxt_paths]
    try:
        if not parsetxt_paths or not all(parsetxt_paths):
            raise ValueError("至少需要指定一个parsetxt路径参数")

        save_folder = "./log/"
        os.makedirs(save_folder, exist_ok=True)

        # 生成参数特征值（取前3个参数+MD5前6位）
        param_hash = hashlib.md5(str(parsetxt_paths).encode()).hexdigest()[:6]
        param_signature = "".join([os.path.basename(p)[:8] for p in parsetxt_paths[:3]]) + f"{param_hash}"
        
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        output_file = os.path.join(save_folder, f"{cpp_excuter}_{timestamp}_{param_signature}.txt")

        print(f"执行C++程序: {cpp_excuter} {' '.join(parsetxt_paths)}")

        # 构造参数列表
        cmd_args = [cpp_excuter] + list(parsetxt_paths)
        
        process = subprocess.Popen(
            cmd_args,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=print_cpp_info
        )

        with open(output_file, 'w') as f:
            if print_cpp_info:
                # 并行读取stdout和stderr
                def handle_stream(stream, handler):
                    for line in stream:
                        handler(line)
                        f.write(line)

                stdout_thread = threading.Thread(
                    target=handle_stream,
                    args=(process.stdout, sys.stdout.write)
                )
                stderr_thread = threading.Thread(
                    target=handle_stream,
                    args=(process.stderr, sys.stderr.write)
                )

                stdout_thread.start()
                stderr_thread.start()
                stdout_thread.join()
                stderr_thread.join()

        process.wait()
        print(f"\nC++程序输出已保存至: {output_file}")

    except subprocess.CalledProcessError as e:
        print(f"执行C++程序错误: {e}")
        print(f"错误信息: {e.stderr.decode()}")
    except Exception as e:  # 更通用的异常捕获
        print(f"发生未预期错误: {str(e)}")
        
def main():
    import csv
    import sys
    
    # if len(sys.argv) < 4:
    #     print("Usage: python cpp_excuter_with_log_saved.py <cpp_excuter> <csv_file> <col1_value> <col2_value>")
    #     return
    
    cpp_excuter = './test_tx_dms_tmp'#sys.argv[1]
    csv_file = './data.csv'#sys.argv[2]
    col1_val = 'ha6'#sys.argv[3]
    col2_val = '目视前方'#sys.argv[4]
    
    try:
        # 读取CSV文件并匹配第1列和第2列
        paths = []
        with open(csv_file, 'r') as f:
            reader = csv.reader(f)
            for row in reader:
                if len(row) >= 3 and row[0] == col1_val and row[1] == col2_val:
                    path = row[2]
                    # 确保路径以斜杠结尾
                    if path and not path.endswith(os.path.sep):
                        path += os.path.sep
                    paths.append(path)
        
        if not paths:
            print(f"No matching rows found for {col1_val} and {col2_val}")
            return
            
        print(f"Found {len(paths)} matching paths:")
        for path in paths:
            print(f" - {path}")
            
        call_cpp_program(cpp_excuter, paths)
        
    except FileNotFoundError:
        print(f"CSV file not found: {csv_file}")
    except Exception as e:
        print(f"Error processing CSV: {str(e)}")
    
if __name__ == "__main__":
    main()