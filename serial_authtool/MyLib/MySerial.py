from time import sleep
from Log import myprint
import serial
import serial.tools.list_ports


class SerialAchieve:
    def __init__(self, band=115200, check="无校验位", data=8, stop=1):
        self.port = None
        # 获取可用串口
        self.port_list = list(serial.tools.list_ports.comports())
        # assert (len(self.port_list) != 0), "无可用串口" 

        self.bandRate = band
        self.checkbit = check
        self.databit = data
        self.stopbit = stop

        # 读写的数据
        self.read_data = None
        self.write_data = None

    def show_port(self):
        for i in range(0, len(self.port_list)):
            print(self.port_list[i])

    def show_other(self):
        myprint("波特率：" + self.bandRate)
        myprint("校验位：" + self.checkbit)
        myprint("数据位：" + self.databit)
        myprint("停止位：" + self.stopbit)

    # 返回串口
    def get_port(self):
        # 获取可用串口
        self.port_list = list(serial.tools.list_ports.comports())
        # assert (len(self.port_list) != 0), "无可用串口"
        return self.port_list

    # 打开串口
    def open_port(self, port):
        self.port = serial.Serial(port, self.bandRate, timeout=1)
        print('打开串口 '+port)

    def delete_port(self):
        if self.port is not None:
            self.port.close()
            print("关闭串口完成")
        else:
            pass

    def get_portnum(self):
        if self.port is not None:
            return self.port.port
        return None

    # def Read_data(self):  # self.port.read(self.port.in_waiting) 表示全部接收串口中的数据
    #     recv_num = self.port.in_waiting
    #     if recv_num > 0:
    #         sleep(0.1)
    #         recv_num = self.port.in_waiting
    #         print('recv_num:', recv_num)
    #     self.read_data = self.port.read(recv_num)
    #     # self.read_data = self.port.readline()
    #     # self.read_data = self.port.read_all()  # 读取数据
    #     # self.read_data = self.port.readlines()
    #     # if self.read_data.find("TT"):
    #     # if self.read_data.decode("utf-8") != '':
    #     #     print('self.read_data :', self.read_data.decode("utf-8"))
    #     return self.read_data.decode("utf-8", errors='ignore')

    def Read_data(self):
        try:
            # self.port.flushInput()  # 清空输入缓冲区
            recv_num = self.port.in_waiting
            if recv_num > 0:
                sleep(0.1)
                recv_num = self.port.in_waiting
                myprint('recv_num:', recv_num)
            else:
                return ""  # 如果没有数据可用，返回空字符串
            self.read_data = self.port.read(recv_num)
            self.port.flushInput()  # 清空输入缓冲区
            return self.read_data.decode("utf-8", errors='ignore')

        except serial.SerialException as e:
            myprint("串口异常:", str(e))
            return ""

    def Write_data(self, data):
        if not self.port.isOpen():
            myprint("串口打开错误")
        else:
            myprint('write_data:', data)
            self.port.write(data.encode("utf-8"))  # 返回的是写入的字节数


# 字符串异或校验运算
def Bcc_Encrypt(inputstr):
    bcc = 0
    for i in inputstr:
        tmp = i.encode('utf-8').hex()
        bcc = bcc ^ int(tmp, 16)
    return hex(bcc)  # 返回十六进制


def ParseSerData(data_in):
    arr_data = data_in.split('-')
    # print("len(arr_data):", len(arr_data))
    if len(arr_data) < 5:
        myprint('str length is error.')
        return False

    if arr_data[0] != 'TT':
        myprint('str start not equal TT.')
        return False

    if arr_data[1] == '1':
        pass
    elif arr_data[1] == '2':
        pass
    elif arr_data[1] == '3':
        return False
    elif arr_data[1] == '6':
        return False

    else:
        myprint('recv data is not need format.')
        return False

    if arr_data[2] == '01':
        pass

    uuid_len = int(arr_data[3])
    if uuid_len < 0:
        myprint('length of str is less than 0.')
        return False

    uuid = arr_data[4]
    uuid = uuid.replace('\n', '').replace('\r', '')
    
    myprint('uuid:', uuid)
    if len(uuid) != uuid_len:
        myprint('length of str is error.',len(uuid), uuid_len)
        return False

    if arr_data[5] is None:  # 串口读取会有校验码丢失的情况
        return True

    bcc_code = Bcc_Encrypt(uuid)
    print('bcc_code:', bcc_code.lower())
    print('bcc_code.lower():', len(bcc_code.lower()), type(bcc_code.lower()))
    print('arr_data[5]:', arr_data[5].lower())
    print('arr_data[5].lower():', len(arr_data[5].lower()), type(arr_data[5].lower()))

    # 检索数目
    # det_num = len(arr_data[5].lower()) - 2
    # if bcc_code.lower().find(arr_data[5].lower()[:det_num]) == -1:  # 忽略大小写不相同,忽略0x前缀的缺省
    #     print('bcc_code is not right, an error may have occurred during the transfer.')
    #     return False

    return True


def ComposeSerData(data_out, status):
    arr_data = ''
    arr_data += 'HT'
    arr_data += '-'
    arr_data += status
    arr_data += '-'
    arr_data += str(len(data_out))
    arr_data += '-'
    if status == '00':
        arr_data += data_out
    arr_data += '-'
    bcc = Bcc_Encrypt(data_out)
    bcc = bcc[2:]
    arr_data += bcc
    arr_data += '\r\n'

    return arr_data

# if __name__ == '__main__':
    # uuid = '0x6a11802341450043\n'
    # uuid = uuid.replace('\n', '')#.replace('\r', '')
    
    # print('uuid:', uuid, len(uuid))

    # print('arr_data[5]:', arr_data[5].lower())
    # print('arr_data[5].lower():', len(arr_data[5].lower()), type(arr_data[5].lower()))
#     myser = SerialAchieve()
#     myser.show_port()
#     myser.open_port("COM23")
#     rd = myser.Read_data()
#     print('rd:', rd)
#     ParseSerData("TT-1-01-21-mstar_172286541896343-1D")
