/*
	在linux board上没有互联网时与上位机app串口通讯获取授权码
*/
typedef enum CHIP_TYPE {
	CHIP_TYPE_MSTAR = 1,
	CHIP_TYPE_AMBER = 2,
	CHIP_TYPE_AX = 3
} CHIP_TYPE_;

typedef enum BUILD_TYPE {
	BUILD_TYPE_DEBUG = 1,
	BUILD_TYPE_RELEASE = 2
} BUILD_TYPE_;

#define MAKE_TYPE BUILD_TYPE_DEBUG//BUILD_TYPE_RELEASE//BUILD_TYPE_DEBUG
#define BOARD_CHIP CHIP_TYPE_MSTAR

#if BOARD_CHIP == CHIP_TYPE_MSTAR
#include "mi_sys.h"
#elif BOARD_CHIP == CHIP_TYPE_AMBER
// Include header or logic for CHIP_TYPE_AMBER
#elif BOARD_CHIP == CHIP_TYPE_AX
// Include header or logic for CHIP_TYPE_AX
#endif
#include "termios.h"
#include <fcntl.h>
#include <fstream>
#include <iostream>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <string>
#include <strstream>
#include <sys/stat.h>
#include <sys/types.h>
#include <unistd.h>

void uuidVerifyXor(std::string uuid, std::string &verify);


#if BOARD_CHIP == CHIP_TYPE_AX
//获取ax620a芯片id
int getaxid(std::string &ax_str) {
    const char *filePath = "/proc/ax_proc/uid";
    std::ifstream file(filePath);

    if (!file.is_open()) {
        std::cerr << "Failed to open file: " << filePath << std::endl;
        return -1;
    }
	std::string content((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
    file.close();
    // Remove the "ax_uid: " prefix
    const std::string prefix = "ax_uid: ";
    size_t pos = content.find(prefix);
    if (pos != std::string::npos) {
        ax_str = content.substr(pos + prefix.length());
    } else {
        ax_str = content;
    }

    return 0;
}
#endif

int main(int argc, char **argv)
{
	char buffer;
	int sun = 0;
	bool succeed = false;
	std::string state;
	std::string len;
	std::string secret_key;
	std::string verify;
	bool Activate_success = true;
	std::string send_data;
	system("stty echo");
	std::string host("TT");
	sleep(1);
	system("stty -echo");

	// std::string uuid("mstar_111186541896311");
	std::string data_state("2");
	std::string algorithm_type("01"); // 01 adas  02 dms 03 bsd
	std::string uuid_v;
	#if BOARD_CHIP == CHIP_TYPE_MSTAR
	MI_SYS_Init(0);
	MI_U64 sys_uuid;
	#else
	// unsigned long long sys_uuid;
	std::string sys_uuid;
	#endif
	std::string path;
	std::string algo_type;
	if (argc > 1)
	{
		algo_type = argv[1];
		path = argv[2];
	}
	// std::fstream _file;

	if (algo_type.compare("ADAS") == 0)
	{
		algorithm_type="01";
	}
	else if (algo_type.compare("DMS")==0)
	{
		algorithm_type="02";
	}else if (algo_type.compare("BSD")==0)
	{
		algorithm_type="04";
	}
	else if(algo_type.compare("BACKBSD") == 0)
	{
		algorithm_type="08";
	}
	// else if (algo_type.compare("ADAS_DMS")==0)
	// {
	// 	algorithm_type="03";
	// }
	// else if (algo_type.compare("ADAS_BSD")==0)
	// {
	// 	algorithm_type="05";
	// }
	// else if (algo_type.compare("DMS_BSD")==0)
	// {
	// 	algorithm_type="06";
	// }
	// else if (algo_type.compare("ALL")==0)
	// {
	// 	algorithm_type="07";
	// }
	else if (algo_type.compare("CMS")==0)
	{
		algorithm_type="16"; //cms取第4位
	}
	
	#if BOARD_CHIP == CHIP_TYPE_MSTAR
	int ret = MI_SYS_ReadUuid(0, &sys_uuid);
	#elif BOARD_CHIP == CHIP_TYPE_AX
	std::string ax_uuid("");
	int ret = getaxid(ax_uuid);
	#elif BOARD_CHIP == CHIP_TYPE_AMBER		
	int ret = 0; //add cms get uuid
	if(argc == 3)
		sys_uuid = argv[2]; //amber uuid从外部传进来
	else
		ret = -1;
	#endif
	if (ret != 0)
	{
		fprintf(stderr, "[func]:%s,[line]:%d,[info]:Failed to get uuid.\n",
				__FUNCTION__, __LINE__);
		return -1;
	}
	std::strstream stream_buffer;
	std::string hex_string;

	#if BOARD_CHIP == CHIP_TYPE_MSTAR
	stream_buffer << sys_uuid;
	stream_buffer >> hex_string;
	#elif BOARD_CHIP == CHIP_TYPE_AX
	hex_string = ax_uuid;
	#endif

	#if MAKE_TYPE == DEBUG
	// hex_string = "226512633913418";//"mstar_" + hex_string;
	//TODO:客户正式使用时记得注释测试uuid
	std::ifstream uuid_txt("/mnt/mmc/testuuid.txt");
    getline(uuid_txt, hex_string);
    uuid_txt.close();
	#else

	#endif
	//  hex_string = "mstar_555555555555555" ;
	uuidVerifyXor(hex_string, uuid_v);

	send_data = host + "-" + data_state + "-" + algorithm_type + "-" + std::to_string(hex_string.length()) + "-" + hex_string + "-" + uuid_v;

	while (1)
	{
		buffer = std::cin.get();
		if (sun == 0 && buffer == 'H' && succeed == false)
		{
			sun = 1;
		}
		else if (sun == 1)
		{
			if (buffer == 'T')
			{
				sun = 2;
			}
			else
			{
				sun = 0;
			}
		}
		if (sun >= 2)
		{
			if (buffer == '-')
			{
				sun++;
				continue;
			}
			if (sun == 3)
			{
				state = state + buffer;
			}
			else if (sun == 4)
			{
				int st = std::stoi(state);
				if (st != 0)
				{
					if (st == 91)
					{
						Activate_success = false;
					}
					std::cin.clear();
					succeed = false;
					sun = 0;
				}
				len = len + buffer;
			}
			else if (sun == 5)
			{
				secret_key = secret_key + buffer;
			}
			else if (sun == 6)
			{
				verify = verify + buffer;
				if (verify.length() == 2)
				{
					succeed = true;
					std::cin.clear();
					sun = 0;
				}
			}
		}
		if (sun == 0 && succeed == false)
		{
			state.clear();
			len.clear();
			secret_key.clear();
			verify.clear();
		}
		buffer = '\0';
		std::cin.clear();
		std::cin.sync();
		// std::cin.ignor();
		if (succeed) // 反馈成功
		{
			int len_ = std::stoi(len);
			int xor_ = 0;
			for (size_t i = 0; i < len_; i++)
			{
				xor_ = xor_ ^ secret_key[i];
			}
			if (std::stoi(verify, 0, 16) == xor_)
			{
				std::string reply = host + "-" + "6" + "-" + algorithm_type + "-" + std::to_string(hex_string.length()) + "-" + hex_string + "-" + uuid_v;
				std::cout << reply << std::endl;
				// std::ofstream key_file(path + "/signature.txt");
				// if (key_file)
				// {
				// 	std::string key = "{\"status\": 0, \"message\": \"success\", \"signature\":\""+secret_key+"\"}";
				// 	key_file << key << std::endl;
				// 	key_file.close();
				// }
				std::ofstream key_file(path + "/activate_code.txt");
				if (key_file)
				{
					std::string key = secret_key;
					key_file << key << std::endl;
					key_file.close();
				}
				std::cout << secret_key << std::endl;
				sleep(2);
				system("stty echo");

				return 0;
			}
			succeed = false;
			state.clear();
			len.clear();
			secret_key.clear();
			verify.clear();
		}
		if (!Activate_success)
		{
			// sleep(1);
			std::cout << send_data << std::endl;
			Activate_success = true;
		}
	}
	system("stty echo");
	return 0;
}

void uuidVerifyXor(std::string uuid, std::string &verify)
{

	int uuid_len = uuid.length();
	int uuid_xor = 0;
	for (size_t i = 0; i < uuid_len; i++)
	{
		uuid_xor = uuid_xor ^ uuid[i];
	}

	char buf1[2];
	sprintf(buf1, "%x", uuid_xor);
	verify = buf1;
}

