import threading
import time

import tkinter
from tkinter import ttk
from tkinter import messagebox

from MyLib import MySerial
from MyLib.MySerial import SerialAchieve  # 导入串口通讯类
from MyLib.MyTime import WhatTimeNow
from Log import myprint

import subprocess
import re
import requests
import os

g_rdstr = ''
g_wrstr = ''
g_uuid = ''
g_last_uuid = 'first_uuid'
g_funclist = 0
# g_signature = ''
g_signature = []
g_readflag = 0
g_redisstatus = -1
g_donglecnt = 0
g_usecnt = 0
g_sigflag = False
g_sermsg = ''
g_log = ''
g_serial_port = ''
g_tmp_serial_port = ''


def SendMesToMain(uuid, funclist):
    global g_uuid
    global g_funclist
    g_uuid = uuid
    g_funclist = funclist


def RecvMesFroMain():
    global g_signature
    # print('g_signature:', g_signature)
    return g_signature

class MainSerial:
    def __init__(self):
        # 定义串口变量
        self.port = None
        self.band = None
        self.check = None
        self.data = None
        self.stop = None
        self.myserial = None
        self.setflag = False
        self.startflag = 0  # False
        # 初始化窗体
        self.mainwin = tkinter.Tk()
        self.mainwin.title("Calmcar串口激活工具")
        self.mainwin.geometry("380x450")

        # 图标
        self.mainwin.iconphoto(False, tkinter.PhotoImage(file='./picture/images.png'))
        # self.mainwin.iconbitmap("C://Users//94047//PycharmProjects//Calmcar_AuthTool//picture//favicon.ico")

        # 标签
        self.label1 = tkinter.Label(self.mainwin, text="串口号:", font=("宋体", 15))
        self.label1.place(x=60, y=10)

        self.label6 = tkinter.Label(self.mainwin, text="日志:", font=("宋体", 15))
        self.label6.place(x=15, y=200)

        # self.label1 = tkinter.Label(self.mainwin, text="授权剩余个数:", font=("宋体", 15))
        # self.label1.place(x=60, y=40)

        self.label2 = tkinter.Label(self.mainwin, text="激活设备数:", font=("宋体", 15))
        self.label2.place(x=60, y=70)

        # 串口号
        self.com1value = tkinter.StringVar()  # 窗体中自带的文本，创建一个值
        self.combobox_port = ttk.Combobox(self.mainwin, textvariable=self.com1value,
                                        width=10, font=("宋体", 13))
        # 输入选定内容
        self.combobox_port["value"] = [""]  # 这里先选定
        self.combobox_port.place(x=180, y=10)  # 显示

        # 按键显示，打开串口
        self.button_OK = tkinter.Button(self.mainwin, text="打开串口",
                                        command=self.button_OK_click, font=("宋体", 13),
                                        width=10, height=1)
        self.button_OK.place(x=50, y=120)  # 显示控件
        # 关闭串口
        self.button_Cancel = tkinter.Button(self.mainwin, text="关闭串口",  # 显示文本
                                            command=self.button_Cancel_click, font=("宋体", 13),
                                            width=10, height=1)
        self.button_Cancel.place(x=200, y=120)  # 显示控件

        # self.button_serialinput = tkinter.Button(self.mainwin, text="查询激活数据",
        #                                         command=self.getactivationtimes, font=("宋体", 13),
        #                                         width=12, height=1)
        # self.button_serialinput.place(x=120, y=160)

        # self.button_Startdev = tkinter.Button(self.mainwin, text="启动设备",  # 显示文本
        #                                     command=self.button_Startdev_click, font=("宋体", 15),
        #                                     width=15, height=3)
        # self.button_Startdev.place(x=100, y=150)  # 显示控件

        # self.NumView = tkinter.Text(self.mainwin, width=6, height=1,
        #                             font=("宋体", 13))  # text实际上是一个文本编辑器
        # self.NumView.place(x=200, y=40)  # 显示
        # self.NumView.insert(tkinter.INSERT, " " + str(g_donglecnt))

        self.NumView_ = tkinter.Text(self.mainwin, width=6, height=1,
                                    font=("宋体", 13))
        self.NumView_.place(x=200, y=70)  # 显示
        self.NumView_.insert(tkinter.INSERT, " " + str(g_usecnt))

        self.LogoutView = tkinter.Text(self.mainwin, width=38, height=10,
                                    font=("宋体", 13))
        self.LogoutView.place(x=15, y=250)  # 显示

        # 获取界面的参数
        # band = 115200, check = "无校验位", data = 8, stop = 1
        self.band = 115200  # self.combobox_band.get()
        self.check = "无校验位"  # self.combobox_check.get()
        self.data = 8  # self.combobox_data.get()
        self.stop = 1  # self.combobox_stop.get()

        self.myserial = SerialAchieve(int(self.band), self.check, self.data, self.stop)

        # 处理串口值
        self.port_list = self.myserial.get_port()
        port_str_list = []  # 用来存储切割好的串口号
        for i in range(len(self.port_list)):
            # 将串口号切割出来
            lines = str(self.port_list[i])
            str_list = lines.split(" ")
            port_str_list.append(str_list[0])
        self.combobox_port["value"] = port_str_list
        if self.combobox_port["value"] != "":
            self.combobox_port.current(0)  # 默认选中第0个
        # self.combobox_port.current(0)  # 默认选中第0个

    def show(self):
        self.mainwin.protocol('WM_DELETE_WINDOW', self.on_closing)
        self.mainwin.mainloop()

    def on_closing(self):
        if messagebox.askokcancel("Quit", "Do you want to quit?"):
            self.mainwin.destroy()
            os._exit(0)

    def button_OK_click(self):
        global g_serial_port
        if self.port is None or self.port.isOpen() is False:
            now_port = self.combobox_port.get()
            new_pot_list = [now_port]
            self.myserial.open_port(now_port)
            g_serial_port = now_port  # self.combobox_port.get()
            # self.combobox_port["value"] = new_pot_list
            self.setflag = True
            self.startflag = 1  # True
            myprint("open serial success... 1", self.combobox_port.get())
            self.PrintToLogout("打开串口成功.", 'black')
            myprint("open serial success... 2", self.combobox_port.get())

    def button_Cancel_click(self):
        global g_serial_port
        g_serial_port = ""
        self.setflag = False
        self.startflag = 0  # False
        self.myserial.delete_port()

        tmp = WhatTimeNow()
        print("close serial success 1...", tmp)
        self.PrintToLogout("关闭串口成功.", 'black')
        print("close serial success 2...")

    # def getactivationtimes(self):
    #     str_ret = dllfunc_getactivationtimes()
    #     json_buf = json.loads(str_ret[7:])
    #     self.PrintToLogout(json_buf['msg'] + '\n', 'blue')


    def PrintToLogout(self, log, choice):
        tmp = WhatTimeNow()
        self.LogoutView.configure(state='normal')
        self.LogoutView.tag_add("color_black", "1.0")
        self.LogoutView.tag_config("color_black", foreground='black')
        self.LogoutView.tag_add("color_orange", "1.0")
        self.LogoutView.tag_config("color_orange", foreground='orange')
        self.LogoutView.tag_add("color_blue", "1.0")
        self.LogoutView.tag_config("color_blue", foreground='blue')
        # 添加显示文字颜色
        if choice == 'black':
            self.LogoutView.insert(tkinter.INSERT, "\n" + tmp + ' ' + log, 'color_black')
        else:
            if choice == 'orange':
                self.LogoutView.insert(tkinter.INSERT, "\n" + tmp + ' ' + log, 'color_orange')
            if choice == 'blue':
                self.LogoutView.insert(tkinter.INSERT, "\n" + tmp + ' ' + log, 'color_blue')

        self.LogoutView.yview_moveto(1)
        # self.LogoutView.insert(tkinter, "\n" + tmp + ' ' + log)

        self.LogoutView.configure(state='disabled')

    def SendMesToBoard(self):
        while True:
            try:
                self.NumView_.delete('1.0', 'end')
                self.NumView_.insert(tkinter.INSERT, " " + str(g_usecnt))
                print("----SendMesToBoard-----:self.setflag:", self.setflag, "self.startflag:", self.startflag)
                if self.setflag and self.startflag == 1:#self.startflag == 2:
                    wrstr = 'HT-91-0' + '\r\n'
                    self.myserial.Write_data(wrstr)
                    print('wrstr:', wrstr)
                    # self.PrintToLogout('writedata:' + wrstr)
                    self.PrintToLogout('查找设备...', 'black')
                time.sleep(5)
            except Exception as e:
                myprint("SendMesToBoard:", e)

    def RddataFilter(self, rddata):
        pos = rddata.find('TT')
        rddata = rddata[pos:]
        return rddata

    def PollSerialFunc(self):
        global g_readflag
        global g_usecnt
        global g_serial_port
        global g_tmp_serial_port
        cnt = 0
        thr = threading.Thread(target=self.SendMesToBoard)  # 添加间隔特定时间向board发送消息
        thr.start()
        while 1:
            time.sleep(0.5)
            # 更新串口值
            port_list = self.myserial.get_port()
            if port_list != self.port_list:
                self.myserial = SerialAchieve(int(self.band), self.check, self.data, self.stop)
                self.port_list = self.myserial.get_port()
                port_str_list = []  # 用来存储切割好的串口号
                for i in range(len(self.port_list)):
                    # 将串口号切割出来
                    lines = str(self.port_list[i])
                    str_list = lines.split(" ")
                    port_str_list.append(str_list[0])
                self.combobox_port["value"] = port_str_list  
                if self.combobox_port["value"] != "":
                    self.combobox_port.current(0)  # 默认选中第0个
                # self.combobox_port.current(0)
                
            #加入使用串口检测，当串口不存在时进行关闭，以及停止通讯
            if g_serial_port != '' and g_serial_port not in self.combobox_port["value"]:
                print('-------1----------')
                g_tmp_serial_port = g_serial_port
                self.button_Cancel_click()
            #为了产线生产效率，进行串口检测，当串口存在时继续进行通讯
            else:
                if g_tmp_serial_port != '' and g_tmp_serial_port in self.combobox_port["value"]:
                    print('-----------2---------')
                    g_serial_port = g_tmp_serial_port
                    g_tmp_serial_port = ''
                    self.button_OK_click()
            # #test
            # print('g_serial_port:', g_serial_port)
            # print('self.setflag:', self.setflag)
            
            if g_serial_port != '' and cnt == 4:
                self.myserial.delete_port()
                self.myserial.open_port(g_serial_port)
                cnt = 0
            # print('------------------------split--------------------------')
            # myprint('self.setflag:', self.setflag)
            if self.setflag:
                # self.SendMesToBoard()
                # myprint('read serial data start...')
                rdstr = self.myserial.Read_data()
                # myprint('read serial data end...')

                if rdstr != '':
                    # myprint('rdstr:', rdstr)
                    # self.PrintToLogout("readdata:" + rdstr)
                    # self.PrintToLogout("redis_status:" + str(GetRedisstatus()))
                    # rdstr = self.RddataFilter(rdstr)  # 滤除不正确的数据
                    pos = rdstr.find('TT')
                    myprint('pos:', pos)
                    if pos < 0:
                        continue
                    rdstr = rdstr[pos:]

                    data = rdstr.split('-')
                    myprint('data:', data, 'len(data):', len(data))

                    if MySerial.ParseSerData(rdstr):  # 根据文档规范解析接收到的数据
                        SendMesToMain(data[4], data[2])
                        myprint('data[4]:', data[4])
                        myprint('data[2]:', data[2])
                        self.PrintToLogout('获取uuid成功.', 'black')
                        # self.PrintToLogout('uuid:' + data[4])
                        g_readflag = 1

                    else:
                        if len(data) >= 2:
                            if data[1] == '6':
                                myprint('data[1]:', data[1])
                                self.startflag = 1
                                self.PrintToLogout('授权成功.', 'orange')
                                self.PrintToLogout('关闭设备.', 'black')
                                g_usecnt += 1
                            # elif data[1] == '3':
                            #     alth_type = ''
                            #     if data[2] == '01':
                            #         alth_type = 'ADAS'
                            #     if data[2] == '02':
                            #         alth_type = 'DMS'
                            #     if data[2] == '03':
                            #         alth_type = 'ADAS_DMS'
                            #     if data[2] == '04':
                            #         alth_type = 'BSD'
                            #     if data[2] == '05':
                            #         alth_type = 'ADAS_BSD'
                            #     if data[2] == '06':
                            #         alth_type = 'DMS_BSD'
                            #     if data[2] == '07':
                            #         alth_type = 'ADAS_DMS_BSD'
                            #     save_path = data[3]
                            #     # save_path = save_path.split('/')[0]
                            #     myprint('save_path:', save_path)
                            #     # send_cmd = '/mnt/sdnand/lib/app_tty' + ' ' + alth_type + ' ' + save_path + '\r\n'
                            #     send_cmd = save_path + 'app_tty' + ' ' + alth_type + ' ' + save_path + '\r\n'

                            #     self.myserial.Write_data(send_cmd)
                            #     self.PrintToLogout('start board client...', 'blue')
                            #     self.startflag = 2

                            else:
                                self.PrintToLogout('解密失败.', 'blue')
                        else:
                            myprint('recv data error.')
                            # self.PrintToLogout('解密失败.', 'red')
                    if cnt > 0:
                        cnt -= 1
                else:
                    cnt += 1
                    myprint('cnt:', cnt)

                if g_readflag == 1:
                    global g_sigflag
                    global g_sermsg
                    global g_signature
                    sig = ['', '']
                    if g_sigflag is True:
                        sig = RecvMesFroMain()
                    #     print('test sig:', sig)
                    # sig = RecvMesFroMain()
                    # print('test sig:', sig)
                    if sig[0] != '':
                        if sig[0] == 'FAIL':
                            self.PrintToLogout('解密失败:' + sig[1], 'blue')
                            g_signature = ['', '']
                        else:
                            if sig[0] == 'SUCCESS':
                                compdata = MySerial.ComposeSerData(sig[1], '00')
                                self.myserial.Write_data(compdata)
                                g_readflag = 0
                                g_sigflag = False

                                # print('sig:', sig)
                                # print('compdata:', compdata)
                                myprint('wrstr1:', compdata)
                                # self.PrintToLogout('writedata:' + compdata)
                                self.PrintToLogout(sig[1], 'orange')
                                self.PrintToLogout('发送密钥.', 'black')
                                g_signature = ['', '']
                                # self.PrintToLogout("servermsg="+g_sermsg)
                                # g_sermsg = ''


def serialprocess():
    ser = MainSerial()
    thr1 = threading.Thread(target=ser.PollSerialFunc)
    thr1.start()
    ser.show()


class MainGui:
    def __init__(self):
        self.tkmain = tkinter.Tk()
        self.tkmain.title("Calmcar激活工具")
        self.tkmain.geometry("350x350")
        self.tkmain.iconphoto(False, tkinter.PhotoImage(file='./picture/images.png'))

        self.button_handinput = tkinter.Button(self.tkmain, text="手动授权",
                                               command=self.handinput_process, font=("宋体", 13),
                                               width=10, height=1)
        self.button_handinput.place(x=50, y=120)

        self.button_serialinput = tkinter.Button(self.tkmain, text="串口授权",
                                                 command=self.serialinput_process, font=("宋体", 13),
                                                 width=10, height=1)
        self.button_serialinput.place(x=200, y=120)

    def handinput_process(self):
        self.tkmain.destroy()
        thread_handprocess = threading.Thread(target=handprocess)
        thread_handprocess.start()

    def serialinput_process(self):
        self.tkmain.destroy()
        thread_serialprocess = threading.Thread(target=serialprocess)
        thread_serialprocess.start()

    def show(self):
        self.tkmain.protocol('WM_DELETE_WINDOW', self.on_closing)
        self.tkmain.mainloop()

    def on_closing(self):
        if messagebox.askokcancel("Quit", "Do you want to quit?"):
            self.tkmain.destroy()
            os._exit(0)


def Mainprocess():
    maingui = MainGui()
    maingui.show()


def handprocess():
    handgui = Handgui()
    handgui.show() 


class Handgui:
    def __init__(self):
        self.tkmain = tkinter.Tk()
        self.tkmain.title("Calmcar手动激活工具")
        self.tkmain.geometry("350x350")
        # self.tkmain.iconbitmap("picture//favicon.ico")
        self.tkmain.iconphoto(False, tkinter.PhotoImage(file='./picture/images.png'))

        self.button_handinput = tkinter.Button(self.tkmain, text="生成密钥",
                                               command=self.handinput_process, font=("宋体", 13),
                                               width=8, height=1)
        self.button_handinput.place(x=250, y=60)

        self.inputview = tkinter.Text(self.tkmain, width=22, height=1,
                                      font=("宋体", 13))
        self.inputview.place(x=20, y=60)  # 显示
        self.inputview.insert(tkinter.INSERT, "")

        self.LogoutView = tkinter.Text(self.tkmain, width=30, height=12,
                                       font=("宋体", 13))
        self.LogoutView.place(x=30, y=110)  # 显示

    def handinput_process(self):
        global g_uuid
        global g_funclist
        global g_sigflag
        global g_signature
        content = self.inputview.get("1.0", tkinter.END)
        content_list = content.split(" ")
        if len(content_list) > 0:
            myprint(type(content_list[1]), content_list[1], len(content_list[1]))

            if content_list[1][:-1].lower() == "adas":
                g_funclist = 1
            elif content_list[1][:-1].lower() == "dms":
                g_funclist = 2
            elif content_list[1][:-1].lower() == "adas_dms":
                g_funclist = 3
            elif content_list[1][:-1].lower() == "bsd":
                g_funclist = 4
            elif content_list[1][:-1].lower() == "adas_bsd":
                g_funclist = 5
            elif content_list[1][:-1].lower() == "dms_bsd":
                g_funclist = 6
            elif content_list[1][:-1].lower() == "adas_dms_bsd":
                g_funclist = 7
            elif content_list[1][:-1].lower() == "cms":
                g_funclist = 16
            else:
                self.LogoutView.delete("1.0", tkinter.END)
                self.LogoutView.insert("1.0", "请输入正确的算法类型")
                return
            g_uuid = content_list[0]

            # 前缀如果没有mstar_，则进行补全
            # pos = g_uuid.find('mstar_')
            # if pos < 0:
            #     g_uuid = 'mstar_' + g_uuid

        time.sleep(1)
        if g_sigflag is True:
            g_sigflag = False
            self.LogoutView.delete("1.0", tkinter.END)
            self.LogoutView.insert("1.0", g_signature[0] + "," + g_signature[1])
            g_signature = ['', '']

    def show(self):
        self.tkmain.protocol('WM_DELETE_WINDOW', self.on_closing)
        self.tkmain.mainloop()

    def on_closing(self):
        if messagebox.askokcancel("Quit", "Do you want to quit?"):
            self.tkmain.destroy()
            os._exit(0)



def get_mac_address():
    try:
        output = subprocess.check_output("ipconfig /all", text=True)
        mac_address = re.findall(r'(?:[0-9A-Fa-f]{2}[:-]){5}(?:[0-9A-Fa-f]{2})', output)
        if mac_address:
            mac_addr = mac_address[0].replace('-','').lower()
            return mac_addr
        else:
            return None
    except Exception as e:
        myprint(e)
        return None

def do_activate_process(post_url, uuid, sn, version, ai, projectNum):
    try:
        mac_addr = get_mac_address()
        if mac_addr is None:
            print("mac address not found")
            return None
        
        url = post_url + "recordMacMode/activate"
        params = {
            "uuid": uuid,
            "sn": sn,
            "version": version,
            "mac": mac_addr,
            "ai": ai,
            "projectNum": projectNum
        }

        signature = ["",""]
        response = requests.post(url, params=params, timeout=30)    
        response.raise_for_status()
        myprint(response.text)
        json_data = response.json()
        if 'status' in json_data:
            if json_data['status'] == "SUCCESS":
                signature[0] = "SUCCESS"
                if 'ActivationCode' in json_data:
                    signature[1] = json_data['ActivationCode']
            else:
                signature[0] = "FAIL"
            return signature
        else:
            print('response json format error.')
            return None
                
    except requests.exceptions.ProxyError as e:
        myprint(f"Proxy error: {e}")
        return None
    except requests.exceptions.ConnectionError as e:
        myprint(f"Connection error: {e}")
        return None
    except requests.exceptions.Timeout as e:
        myprint(f"Timeout error: {e}")
        return None
    except requests.exceptions.RequestException as e:
        myprint(f"Request error: {e}")
        return None

def Loopprocess():
    global g_uuid
    global g_funclist
    global g_signature
    global g_sigflag
    global g_last_uuid

    while True:
        time.sleep(0.5)

        if g_uuid is not None and g_uuid != '' and g_uuid != g_last_uuid:
            myprint("---serialserver_handle func start---")
            myprint('server uuid:', g_uuid)
            # g_last_uuid = g_uuid
            #TODO:释放给客户时记得使用正式环境
            uuid=g_uuid#"01234567890"
            sn = "ax_"+uuid
            version="v0.1"
            ai="adas" #TODO:{"code":"F009","description":"ai Parameter error","status":"FAILURE"}
            #test_env
            # projectNum="testmac_project"
            # post_url = "http://**************:5002/"
            #formal_env
            projectNum="apkcar"
            post_url = "https://vis-vehicle.cn/"
            seroutbuf = do_activate_process(post_url, uuid, sn, version, ai, projectNum) 
            if seroutbuf is not None:
                g_signature = seroutbuf
                g_sigflag = True
                g_last_uuid = g_uuid # 在上一次激活成功后，uuid不变，则不需要再次激活

            # seroutbuf = UploadAuthorizationDataToServer(g_uuid, int(g_funclist))
            # myprint('seroutbuf:', seroutbuf)

            # myprint('-------------test 1---------------')
            # if seroutbuf[:7] == 'SUCCESS':
            #     myprint('-------------SUCCESS----------------------')
            #     json_buf = json.loads(seroutbuf[7:])
            #     g_signature = ['SUCCESS', json_buf["signature"]]
            #     g_sigflag = True
            # else:
            #     if seroutbuf[:4] == 'FAIL':
            #         myprint('-------------FAIL----------------------')
            #         g_signature = ['FAIL', seroutbuf[5:]]
            #         g_sigflag = True
            # myprint('-------------test 2---------------')

            g_uuid = ''


if __name__ == '__main__':
    thread_main = threading.Thread(target=Mainprocess)
    thread_main.start()

    thread_loop = threading.Thread(target=Loopprocess)
    thread_loop.start()
