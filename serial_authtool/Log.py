from time import strftime, localtime
import os

AppStartTime = strftime("%Y_%m_%d_%H_%M_%S", localtime())


def myprint(*values):
    global AppStartTime
    # print('AppStartTime:', AppStartTime)
    timestr = strftime("%Y_%m_%d %H:%M:%S", localtime())
    print(timestr, " | ", *values)
    loginfo = timestr + " | "

    for tmp in values:
        loginfo += str(tmp)
        loginfo += ' '
    # 获取当前文件的目录
    cur_path = os.path.abspath(os.path.dirname(__file__))
    # 获取根目录
    root_path = cur_path[:cur_path.find("Calmcar_AuthTool\\") + len("Calmcar_AuthTool\\")]
    # path = root_path+'/log/'
    path = './log/'
    file = 'Log_' + AppStartTime + '.txt'

    abspath = os.path.abspath(path + file)

    with open(abspath, "a") as fw:
        fw.write(loginfo+'\r\n')

#
# if __name__ == '__main__':
#     name = 'abc'
#     value = '123'
#     info("name=", name, "value=", value)
