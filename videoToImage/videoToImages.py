import os.path

import cv2

def extract_frame_from_video(video_path, dest_dir, filename, skip_fps=1, start=0.0, end=None, num=0, region=None):
    """
    视频剪图
    @param filename: 图片前缀
    @param region: 图片分辨率 (x, y, width, height)
    @param video_path: 视频路径
    @param dest_dir: 图片存放路径
    @param start: 视频开始时间（秒）,默认为0
    @param end: 视频结束时间（秒），默认为视频总时长
    @param skip_fps: 图片每隔几帧截取（默认1帧）
    @param num: 后缀从第几开始（默认从0开始）
    @return: 返回截取最后一张图的后缀加1，用于该方法的num参数，方便有多个视频、片段的图要放在一起
    """
    os.makedirs(dest_dir, exist_ok=True)
    # 创建一个VideoCapture对象
    cap = cv2.VideoCapture(video_path)

    # 检查是否成功打开视频文件
    if not cap.isOpened():
        print("无法打开视频文件，请检查路径是否正确以及是否有权限访问！")
        print("视频路径:", video_path)
        return False
    else:
        print("视频文件成功打开！")

    # 获取视频的帧率
    use_second = True
    fps = cap.get(cv2.CAP_PROP_FPS)
    if fps >= 10:
        skip_fps = round(fps) / 10
    print("视频的帧率:", fps)
    print(f"每{skip_fps}帧取一张图")
    if not end:
        end = cap.get(cv2.CAP_PROP_FRAME_COUNT) / fps
        print("视频时长：", end)
    if end < 0:
        use_second = False
    i = 0
    # print(dest_dir)
    os.makedirs(dest_dir, exist_ok=True)
    frame = None
    
    desired_fps = 30
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter("./test.mkv", fourcc, desired_fps, (1280, 720))
    while True:
        # 设置要跳转到的时间点
        # 注意：此处设置的位置是以秒为单位的，且是基于帧率进行计算的
        # 如果视频的帧率不是整数，或者与实际播放速度有差异，则可能会有误差
        if use_second:
            second = int(start * 1000 + (1000 / fps * (skip_fps * i)))
            # print(second,end,999999)
            if second > end * 1000:
                break
            cap.set(cv2.CAP_PROP_POS_MSEC, second)  # 转换成毫秒
            # 读取一帧
            ret, frame = cap.read()
        else:
            for i in range(int(skip_fps)):
                ret, frame = cap.read()
                # time.sleep(0.1)

        # 检查是否成功读取帧
        if ret:
            if region:
                x, y, width, height = region
                # print(x, y, width, height)
                frame = frame[y: y + height, x: x + width]

            # 保存图片
            out.write(frame)
            # cv2.imwrite(f"{dest_dir}/{filename}_{num}.jpg", frame)
            print("\r已保存%d张图(%s)" % (num + 1, dest_dir), end="")
        else:
            if use_second:
                print(f"未能找到第{second}毫秒的帧，请检查时间和视频长度。", end="\n")
            else:
                break
        i += 1
        num += 1

    # 释放资源
    cap.release()
    out.release()
    
    return num

if __name__ == '__main__':
    video_path = r'/home/<USER>/data/byd/byd_eq_r_realcar/subjectivetest_250212/2025-02-12 14-10-43.mkv'  # 替换为你的视频文件路径
    output_path = r'./'  # 输出图片的路径
    region = (0,0,1280,800)
    start = 8*60+36  # 你想获取的视频片段中开始的秒数
    end = start + 10 # 你想获取的视频片段中结束的秒数
    extract_frame_from_video(video_path,output_path,"a", skip_fps=0,start=start,end=end,region=region)