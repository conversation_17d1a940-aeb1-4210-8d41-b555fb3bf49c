# videoToImage使用
## 功能描述
1. 将指定路径的视频裁剪为图片
2. 可以指定某时间段内的视频进行裁剪
3. 可裁剪指定某区域的图
4. 可指定裁剪出来图片的初始序号
5. 可指定每隔几帧进行裁剪，默认为"当前视频帧率/10",也就是裁出来的视频默认为1秒10张
```python
def extract_frame_from_video(video_path, dest_dir, filename, skip_fps=1, start=0.0, end=None, num=0, region=None):
    """
    视频剪图
    @param filename: 图片前缀
    @param region: 图片分辨率 (x, y, width, height)
    @param video_path: 视频路径
    @param dest_dir: 图片存放路径
    @param start: 视频开始时间（秒）,默认为0
    @param end: 视频结束时间（秒），默认为视频总时长
    @param skip_fps: 默认为"当前视频帧率/10"
    @param num: 后缀从第几开始（默认从0开始）
    @return: 返回截取最后一张图的后缀加1，用于该方法的num参数，方便有多个视频、片段的图要放在一起
    """
```