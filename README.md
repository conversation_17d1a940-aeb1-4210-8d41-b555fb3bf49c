# Tool Kit - 各种小工具聚合

每个小工具需要创建自己对应文件夹以及给予说明。

## 🚀 DMS自动化分析与渲染平台

**最新更新**: 2025-07-10 - 三阶段开发全部完成

### 项目概述
DMS自动化分析与渲染平台是一个端到端的驾驶员监控系统分析工具，支持从视频素材准备到最终结果渲染的完整自动化流程。

### 核心功能
- **阶段一**: 视频素材准备 - 将视频转换为标准化帧资产包
- **阶段二**: C++批量处理 - 使用多种DMS算法工具包进行批量分析
- **阶段三**: 按需渲染 - 生成可视化分析视频和多版本对比视频

### 快速开始
```bash
# 进入项目目录
cd dms_automation

# 阶段一: 准备素材
python3 scripts/prepare_assets.py --source video.mkv --name test_001

# 阶段二: 批量处理
python3 scripts/run_cpp_analysis.py --asset test_001 --kit v1.0

# 阶段三: 渲染视频
python3 scripts/render_video.py results/test_001_v1.0_xxx -o output.mp4

# 运行测试验证
python3 scripts/test_stage2.py
python3 scripts/test_stage3.py
```

### 项目结构
```
dms_automation/
├── scripts/                    # 核心脚本
│   ├── prepare_assets.py      # 阶段一：素材准备
│   ├── run_cpp_analysis.py    # 阶段二：批量处理
│   ├── render_video.py        # 阶段三：单版本渲染
│   ├── create_comparison.py   # 阶段三：对比渲染
│   └── test_stage*.py         # 测试套件
├── assets/                     # 帧资产包存储
├── cpp_kits/                   # C++工具包存储
├── results/                    # 处理结果输出
├── rendered_videos/            # 渲染视频输出
└── mydoc/                      # 项目文档
```

---

## 其他工具

+ [**videoToImage**](http://*************:8880/root/tool_kit/-/tree/main/videoToImage):
  * 裁剪视频，分割成图片，可以指定时间片段。

#### 12_examine_tool:
读取算法josn结果，通过画面展示

#### dms_preview_tool：
实车测试DMS预览工具

#### serial_authtool：
算法sdk的串口激活工具

#### getyoursightregion：
调整视线区域工具

#### dms_postmortem工具
用于将问题视频制作成有详细报警信息渲染的分析视频