import asyncio
import websockets
import cv2
import tongxing_dms_pb2
import numpy as np
import json
import time
import sys
import os
from PIL import Image, ImageDraw, ImageFont
import queue
from threading import Thread
import math
from datetime import datetime

# url='ws://192.168.7.1:1181' # 板端实际使用时
# url='ws://192.168.3.229:1181'
url='ws://192.168.3.138:1181' # x86上x86测试时
# url='ws://192.168.3.138:4601' 
# url='ws://192.168.3.138:4600' # x86上4600测试时
# url = 'ws://192.168.231.5:1181'
bbox_color = (0,255,0)
point_color = (0,255,0)
auto_make_folder_time=600

save_flag = False
output_path = r"C:\HJH\testregion"
is_run_first = True
file_path = None

region_hulls = None
img = None
is_drawregion = False
last_cali_headangle = None
last_cali_eyeangle = None

color_list = [
        (255, 0, 0),   # 蓝色
        (0, 255, 0),   # 绿色
        (0, 0, 255),   # 红色
        (255, 255, 0), # 青色
        (255, 0, 255), # 洋红色
        (0, 255, 255), # 黄色
        (128, 0, 128), # 紫色
        (128, 128, 0), # 橄榄色
        (0, 128, 128), # 蓝绿色
        (128, 128, 128), # 灰色
        (255, 165, 0),  # 橙色
        (75, 0, 130),   # 靛蓝
        (240, 230, 140),# 卡其色
        (64, 224, 208), # 绿松石色
        (220, 20, 60),  # 猩红
        (255, 20, 147), # 深粉红
        (50, 205, 50),  # 酸橙绿
        (210, 105, 30), # 巧克力色
        (32, 178, 170), # 浅海蓝色
        (139, 69, 19)   # 棕色
    ]

preview_queue=queue.Queue(10)
def create_rotate_matrix(angle, axis):
    if axis == 'x':
        matrix = np.array([[1.0, 0.0, 0.0, 0.0], [0.0, np.cos(angle), -np.sin(angle), 0.0],
                           [0.0, np.sin(angle), np.cos(angle), 0.0], [0.0, 0.0, 0.0, 1.0]], dtype=np.float32)
        return matrix
    elif axis == 'y':
        matrix = np.array([[np.cos(angle), 0.0, -np.sin(angle), 0.0], [0.0, 1.0, 0.0, 0.0],
                           [np.sin(angle), 0.0, np.cos(angle), 0.0], [0.0, 0.0, 0.0, 1.0]], dtype=np.float32)
        return matrix
    elif axis == 'z':
        matrix = np.array([[np.cos(angle), -np.sin(angle), 0.0, 0.0], [np.sin(angle), np.cos(angle), 0.0, 0.0],
                           [0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 1.0]], dtype=np.float32)
        return matrix
    else:
        print("wrong axis")
        return False

def create_perspective_matrix(fovy, aspect, n, f):
    q = 1.0 / np.tan((0.5 * fovy) / 180.0 * np.pi)
    A = q / aspect
    B = (n + f) / (n - f)
    C = (2.0 * n * f) / (n - f)
    return np.array([[A, 0.0, 0.0, 0.0], [0.0, q, 0.0, 0.0], [0.0, 0.0, B, C], [0.0, 0.0, -1.0, 0.0]], dtype=np.float32)

def create_translate_matrix(vec):
    return np.array([[1.0, 0.0, 0.0, vec[0]], [0.0, 1.0, 0.0, vec[1]],
                     [0.0, 0.0, 1.0, vec[2]], [0.0, 0.0, 0.0, 1.0]], dtype=np.float32)

def create_lookat_matrix(eye, center, up):
    f = (center - eye)/np.linalg.norm(eye-center, axis=0, keepdims=False)
    up= up/np.linalg.norm(up, axis=0, keepdims=False)
    s = np.cross(f, up)
    u = np.cross(s, f)
    M = np.array([[s[0], s[1], s[2], 0], [u[0], u[1], u[2], 0],
                 [-f[0], -f[1], -f[2], 0], [0, 0, 0, 1]], dtype=np.float32)
    return np.dot(M, create_translate_matrix(-eye))

def calculate_coordinate(roll, pitch, yaw, length):
    x_line = np.array([[1], [0], [0], [1]], dtype=np.float32)
    y_line = np.array([[0], [1], [0], [1]], dtype=np.float32)
    z_line = np.array([[0], [0], [1], [1]], dtype=np.float32)
    X_rotate = create_rotate_matrix(roll, 'x')
    Y_rotate = create_rotate_matrix(pitch, 'y')
    Z_rotate = create_rotate_matrix(yaw, 'z')
    lookat = create_lookat_matrix(np.array([5.0, 0.0, 0.0]), np.array([-1.0, 0.0, 0.0]), np.array([0.0, 0.0, 1.0]))
    perspective = create_perspective_matrix(60.0, 1.0, 0.1, 100.0)
    x_out = np.dot(perspective, np.dot(lookat, np.dot(X_rotate, np.dot(Z_rotate, np.dot(Y_rotate, x_line)))))
    y_out = np.dot(perspective, np.dot(lookat, np.dot(X_rotate, np.dot(Z_rotate, np.dot(Y_rotate, y_line)))))
    z_out = np.dot(perspective, np.dot(lookat, np.dot(X_rotate, np.dot(Z_rotate, np.dot(Y_rotate, z_line)))))
    return length * np.array([[x_out[0]/x_out[3], x_out[1]/x_out[3]], [y_out[0]/y_out[3], y_out[1]/y_out[3]],
                     [z_out[0]/z_out[3], z_out[1]/z_out[3]]], dtype=np.float32)


def draw_axis(img, axis, center, bbox=None):
    # center = center.astype(np.int32)
    axis.astype(np.int32)
    if bbox is not None:
        cv2.rectangle(img, (bbox[0], bbox[1]), (bbox[0] + bbox[2], bbox[1] + bbox[3]), (0, 0, 255), 5)
    # cv2.line(img, (center[0], center[1]), (int(center[0] + axis[1][0]), int(center[1] - axis[1][1])), (0, 102, 80), 1)
    # cv2.line(img, (center[0], center[1]), (int(center[0] + axis[2][0]), int(center[1] - axis[2][1])), (255, 0, 0), 1)
    cv2.line(img, (center[0], center[1]), (int(center[0] + axis[0][0]), int(center[1] - axis[0][1])), (0, 0, 255), 1)
    return img


def cv2ImgAddText(img, text, left, top, textColor=(0, 255, 0), textSize=20):    
    if (isinstance(img, np.ndarray)):  #判断是否OpenCV图片类型        
        img = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))    
        draw = ImageDraw.Draw(img)    
        # fontText = ImageFont.truetype("simhei.ttf", textSize, encoding="utf-8")    
        draw.text((left, top), text, textColor)    
        return cv2.cvtColor(np.asarray(img), cv2.COLOR_RGB2BGR)

def display_distraction_params(image, params, face_params, start_x=10, start_y=270, line_height=20):
    try:
        cv2.putText(image, "head angle info", (start_x, start_y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 0, 0), 1)
        cv2.rectangle(image, (start_x, start_y), (start_x + 180, start_y + 180), (255, 0, 0), 1)
        center_position = (start_x + 90, start_y + 90)
        head_yaw_min = (params["head_yaw_min"])
        head_yaw_max = (params["head_yaw_max"])
        head_pitch_min = (params["head_pitch_min"])
        head_pitch_max = (params["head_pitch_max"])
        
        inner_rect = [(center_position[0] + (head_yaw_min), center_position[1] + (head_pitch_min)), 
                    (center_position[0] + (head_yaw_max), center_position[1] + (head_pitch_max))]
        cv2.rectangle(image, (int(inner_rect[0][0]),int(inner_rect[0][1])), (int(inner_rect[1][0]),int(inner_rect[1][1])), (255, 255, 255), 1)
        cv2.putText(image, "min:("+ str(round(head_yaw_min, 2))+ ","+str(round(head_pitch_min, 2)) +")", 
                    (int(inner_rect[0][0]), int(inner_rect[0][1]-10)), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
        cv2.putText(image, "max:("+ str(round(head_yaw_max, 2))+ ","+str(round(head_pitch_max, 2)) +")", 
                    (int(inner_rect[1][0]+10), int(inner_rect[1][1])), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
        
        face_point = (center_position[0]+(face_params["yaw"]), center_position[1]+(face_params["pitch"]))

        face_angle_color = (255, 255, 0)

        # if face_point[0] not in range(inner_rect[0][0], inner_rect[1][0]) or face_point[1] not in range(inner_rect[0][1], inner_rect[1][1]):
        #     # print("not in rectangle")
        #     face_angle_color = (0, 0, 255)
        if face_point[0] < inner_rect[0][0] or face_point[0] > inner_rect[1][0] or face_point[1] < inner_rect[0][1] or face_point[1] > inner_rect[1][1]:
            # print("not in rectangle")
            face_angle_color = (0, 0, 255)
        cv2.putText(image, "("+str(round(face_params["yaw"], 2))+","+str(round(face_params["pitch"], 2))+","+str(round(face_params["roll"], 2))+")", 
                    (int(face_point[0]+10), int(face_point[1])), cv2.FONT_HERSHEY_SIMPLEX, 0.5, face_angle_color, 1)
        cv2.circle(image, (int(face_point[0]), int(face_point[1])), 2, face_angle_color, -1)

    except Exception as e:
        print("display_distraction_params:", e)
        
    return image

# def draw_face_info(image, face_info):
#     bbox_color = (0, 255, 127)  
#     point_color = (3, 97, 255)  
#     thickness = 2
#     cnt = 0
#     cv2.rectangle(image, 
#                 (2*face_info["xmin"], 2*face_info["ymin"]-40),
#                 (2*face_info["xmax"], 2*face_info["ymax"]-40), 
#                 bbox_color, thickness)

#     for landmark in face_info["landmarks"]:
#         cv2.circle(image, (2*landmark["x"], 2*landmark["y"]-40), 2, point_color, -1)

#         if cnt == 5:
#             cv2.circle(image, (2*landmark["x"], 2*landmark["y"]-40), 2, (255, 0, 0), -1)
#         if cnt == 6:
#             cv2.circle(image, (2*landmark["x"], 2*landmark["y"]-40), 2, (0, 255, 0), -1)
#         if cnt == 7:
#             cv2.circle(image, (2*landmark["x"], 2*landmark["y"]-40), 2, (0, 0, 255), -1)
#         if cnt == 8:
#             cv2.circle(image, (2*landmark["x"], 2*landmark["y"]-40), 2, (0, 255, 255), -1)

#         cnt+=1
#     return image
def draw_face_info(image, face_info):
    bbox_color = (0, 255, 127)  
    point_color = (3, 97, 255)  
    thickness = 2
    cnt = 0
    cv2.rectangle(image, 
                (2*face_info["xmin"], 2*face_info["ymin"]),
                (2*face_info["xmax"], 2*face_info["ymax"]), 
                bbox_color, thickness)

    for landmark in face_info["landmarks"]:
        cv2.circle(image, (2*landmark["x"], 2*landmark["y"]), 2, point_color, -1)

        if cnt == 5:
            cv2.circle(image, (2*landmark["x"], 2*landmark["y"]), 2, (255, 0, 0), -1)
        if cnt == 6:
            cv2.circle(image, (2*landmark["x"], 2*landmark["y"]), 2, (0, 255, 0), -1)
        if cnt == 7:
            cv2.circle(image, (2*landmark["x"], 2*landmark["y"]), 2, (0, 0, 255), -1)
        if cnt == 8:
            cv2.circle(image, (2*landmark["x"], 2*landmark["y"]), 2, (0, 255, 255), -1)

        cnt+=1
    return image


def mapping_point(show, distraction_params, region_label):
    global region_hulls
    global img
    global is_drawregion

    if region_hulls is None:
        hulls = distraction_params["region_hulls"]
        region_hulls = hulls
        print("set_region_hulls...")

    if img is None:
        img = np.ones((800, 800, 3), dtype=np.uint8) * 255

    if img is not None and region_hulls is not None and is_drawregion is False:
        contours = []
        for hull in region_hulls:
            scaled_hull = [(coord["x"], coord["y"]) for coord in hull]
            coords = np.array(scaled_hull, dtype=np.float32).reshape((-1, 2))
            coords = np.round(coords).astype(np.int32)
            coords = coords.reshape((-1, 1, 2))
            contours.append(coords)
        cv2.drawContours(img, contours, -1, (0, 255, 0), 1)
        is_drawregion = True
        
        line_color = (0, 0, 0)  
        line_thickness = 1      
        dash_length = 10        
        gap_length = 10         
        for y in range(0, 800, dash_length + gap_length):
            cv2.line(img, (400, y), (400, y + dash_length), line_color, line_thickness)
        for x in range(0, 800, dash_length + gap_length):
            cv2.line(img, (x, 400), (x + dash_length, 400), line_color, line_thickness)

    if img is not None:
        show_img = img.copy()
        mapping_x = int(distraction_params["mapping_x"])
        mapping_y = int(distraction_params["mapping_y"])
        if mapping_x > 0 and mapping_y > 0:
            color = (255, 255, 0)
            if int(distraction_params["predict_result"]) != 0:
                color = (0, 0, 255)
            cv2.circle(show_img, (mapping_x, mapping_y), 3, color, -1)
            cv2.putText(show_img, "("+str(mapping_x)+","+str(mapping_y)+")", (mapping_x+10, mapping_y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
            
            if region_label != -1:           
                color = color_list[region_label]
                cv2.circle(img, (mapping_x, mapping_y), 1, color, -1)
        
        crop_x, crop_width, crop_y, crop_height = 200 ,400 ,200 ,400
        crop_x_end = min(crop_x + crop_width, show_img.shape[1])
        crop_y_end = min(crop_y + crop_height, show_img.shape[0])
        
        cropped_region = show_img[crop_y:crop_y_end, crop_x:crop_x_end]

        overlay_height, overlay_width = cropped_region.shape[:2]
        target_x = show.shape[1] - overlay_width  # 右上角 x 坐标
        target_y = 0  # 右上角 y 坐标

        show[target_y:target_y + overlay_height, target_x:target_x + overlay_width] = cropped_region
        
        cv2.imwrite("region_mapping.png", img)

    return show

def show_progress_bar(image, progress, total, start_pos, bar_length=20, default_total=3500, default_percent=0.8):
    if total > 0:
        compare_total = default_total
        if total >= compare_total:
            compare_total = total
            
        bar_color = (140, 90, 60)
        percent = (progress / compare_total) 
        bar_width = int(bar_length * percent) 
        
        if percent >= default_percent and total >= default_total: # 在占比和长度都符合时才表示满足条件
            bar_color = (0, 255, 0) 
        
        cv2.rectangle(image, start_pos, (start_pos[0] + bar_length * 10, start_pos[1] + 20), (255, 255, 255), -1)  
        cv2.rectangle(image, start_pos, (start_pos[0] + bar_width * 10, start_pos[1] + 20), bar_color, -1)  
        
        cv2.putText(image, f'{round((percent * 100), 2)}%', (start_pos[0] + bar_length * 5, start_pos[1] + 15),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 255), 1, cv2.LINE_AA)
        
        total_text = f'{compare_total}'
        cv2.putText(image, total_text, (start_pos[0] + bar_length * 10 + 10, start_pos[1] + 15),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 255), 1, cv2.LINE_AA)
    
    return image

def showinfo_linebyline(image, params, start_x=10, start_y=270, line_height=20):
    y = start_y
    for key, value in params.items():
        text = f"{key}: {value}"
        cv2.putText(image, text, (start_x, y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
        y += line_height
    return image

def add_text(image, text, position, color=(255, 0, 0), font_scale=0.6, thickness=1):    
    cv2.putText(image, text, position, cv2.FONT_HERSHEY_SIMPLEX, font_scale, color, thickness, cv2.LINE_AA)
    return image

def draw_eye_info(image, eye_info):
    eye_coutours_color = (0, 255, 0)
    iris_coutours_color = (0, 0, 255)
    pupil_color = (255, 0, 0)

    for landmark in eye_info["eye_coutours"]:
        cv2.circle(image, (2*landmark["x"], 2*landmark["y"]), 2, eye_coutours_color, -1)
        
    for landmark in eye_info["iris_coutours"]:
        cv2.circle(image, (2*landmark["x"], 2*landmark["y"]), 2, iris_coutours_color, -1)
    
    # cv2.circle(image, (2*eye_info["pupil"]["x"], 2*eye_info["pupil"]["y"]), 5, pupil_color, -1)
    
    return image

def process_frame(image, message, region_label=-1):
    global last_cali_headangle
    global last_cali_eyeangle
    # print(message)
    show = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB) 
    face_info = message["dms_result"]["face_info"]
    leye_info = message["dms_result"]["face_info"]["left_eye_landmark"]
    reye_info = message["dms_result"]["face_info"]["right_eye_landmark"]
    
    # show = cv2.resize(show, (1280, 800))
    show = cv2.resize(show, (1280, 720))

    show = draw_face_info(show, face_info)

    show = draw_eye_info(show, leye_info)
    show = draw_eye_info(show, reye_info)
    
    distraction_params_str = message["dms_result"].get("distraction_params", "{}")
    distraction_params = {}
    try:
        distraction_params = json.loads(distraction_params_str)
    except json.JSONDecodeError as e:
        print(f"Error parsing distraction_params: {e}")
    
    tired_params = message["tiredInfo"]     
    # tired_params_str = message["tiredInfo"]
    # tired_params = {}
    # try:
    #     tired_params = json.loads(tired_params_str)
    # except json.JSONDecodeError as e:
    #     print(f"Error parsing tired_params: {e}")
        
    normal_color = (255, 0, 0)
    unnormal_color = (0, 255, 255)
    warning_color = (0, 0, 255)
    
    text_info = []
    
    color = normal_color

    # hardware status
    if message["dms_result"]["camera_status"] != 0:
        color = warning_color
    text_info.append(("cam_status: " + str(message["dms_result"]["camera_status"]), (770,30), color))
    color = normal_color
    text_info.append(("car_gear: " + str(message["dms_result"]["car_gear"]), (770, 50), color))
    text_info.append(("car_speed: " + str(message["dms_result"]["car_speed"]), (770, 70), color))
    text_info.append(("steer_rad: " + str(message["dms_result"]["car_steer_whl_snsr_rad"])[:6], (770, 90), color))
    text_info.append(("turn_light: " + str(message["dms_result"]["turn_light"]), (770, 110), color))
    
    # 眼睛信息
    eyeinfo_start_pos = (5, 20)
    cv2.putText(show, "EYEINFO", eyeinfo_start_pos, cv2.FONT_HERSHEY_SIMPLEX, 0.8, color, 1, cv2.LINE_AA)
    text_info.append(("         left     right", (5, 35), color))
    text_info.append(("eye:", (5, 55), color))
    text_info.append(("iris:", (5, 70), color))
    text_info.append(("pupil:", (5, 85), color))
    text_info.append(("closed:", (5, 100), color))
    text_info.append(("up/down:", (5, 115), color))
    text_info.append(("yaw:", (5, 130), color))
    text_info.append(("pitch:", (5, 145), color))
    text_info.append(("opening:", (5, 160), color))

    text_info.append((str(message["dms_result"]["result_frame_id"]), (800, 700), color))
    
    if face_info["left_eye_landmark"]["eye_score"] < 0.1:
        color = unnormal_color
    text_info.append((str(face_info["left_eye_landmark"]["eye_score"]), (100, 55), color))
    color = normal_color
    
    if face_info["left_eye_landmark"]["iris_score"] < 0.1:
        color = unnormal_color
    text_info.append((str(face_info["left_eye_landmark"]["iris_score"]), (100, 70), color))
    color = normal_color
    
    if face_info["left_eye_landmark"]["pupil_score"] < 0.1:
        color = unnormal_color
    text_info.append((str(face_info["left_eye_landmark"]["pupil_score"]), (100, 85), color))
    color = normal_color

    if face_info["right_eye_landmark"]["eye_score"] < 0.1:
        color = unnormal_color
    text_info.append((str(face_info["right_eye_landmark"]["eye_score"]), (180, 55), color))
    color = normal_color
    
    if face_info["right_eye_landmark"]["iris_score"] < 0.1:
        color = unnormal_color
    text_info.append((str(face_info["right_eye_landmark"]["iris_score"]), (180, 70), color))
    color = normal_color
    
    if face_info["right_eye_landmark"]["pupil_score"] < 0.1:
        color = unnormal_color
    text_info.append((str(face_info["right_eye_landmark"]["pupil_score"]), (180, 85), color))
    color = normal_color

    if face_info["left_close_eye_score"] > 0.7:
        color = unnormal_color
    text_info.append((str(round(face_info["left_close_eye_score"], 2)), (100, 100), color))
    color = normal_color
    if face_info["right_close_eye_score"] > 0.7:
        color = unnormal_color
    text_info.append((str(round(face_info["right_close_eye_score"], 2)), (180, 100), color))
    color = normal_color
    
    text_info.append((str(round(message["dms_result"]["left_up_down_proportion"], 2)), (100, 115), color))
    text_info.append((str(round(message["dms_result"]["right_up_down_proportion"], 2)), (180, 115), color))
    
    text_info.append((str(int(face_info["left_eye_landmark"]["yaw"])), (100, 130), color))
    text_info.append((str(int(face_info["left_eye_landmark"]["pitch"])), (100, 145), color))
    text_info.append((str(int(face_info["right_eye_landmark"]["yaw"])), (180, 130), color))
    text_info.append((str(int(face_info["right_eye_landmark"]["pitch"])), (180, 145), color))

    # text_info.append((str(round((face_info["left_eye_landmark"]["opening"]), 2)), (100, 160), color))
    # text_info.append((str(round((face_info["right_eye_landmark"]["opening"]), 2)), (180, 160), color))
    
    # show = draw_eye_info(show, int(distraction_params["mapping_x"])-400, int(distraction_params["mapping_y"])-400)
    # text_info.append((distraction_params[""]))
    
    if face_info["mouth_opening"] >= 0.5:
        color = unnormal_color
    text_info.append(("mouthopen: " + str(round(face_info["mouth_opening"], 2)), (5, 180), color))
    color = normal_color
    
    # calibration
    cv2.putText(show, "CALIBRATION", (300, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.8, color, 1, cv2.LINE_AA)
    calibrate_status = message["dms_result"]["calibrate_status"]
    if calibrate_status == 2:
        color = warning_color
    else:
        if calibrate_status == 1:
            color = unnormal_color  
    text_info.append(("status: " + str(calibrate_status), (300, 35), color))
    color = normal_color
    
    if calibrate_status == 0:
        cali_head_yaw = round(distraction_params["headpose_yaw_"], 2)
        cali_head_pitch = round(distraction_params["headpose_pitch_"], 2)
        cali_head_roll = round(distraction_params["headpose_roll_"], 2)
        cali_eye_yaw = round(distraction_params["gaze_yaw_mean"], 2)
        cali_eye_pitch = round(distraction_params["gaze_pitch_mean"], 2)
        
        if last_cali_headangle != [cali_head_yaw, cali_head_pitch, cali_head_roll]:
            last_cali_headangle = [cali_head_yaw, cali_head_pitch, cali_head_roll]
            color = unnormal_color
            
        if last_cali_eyeangle != [cali_eye_yaw, cali_eye_pitch]:
            last_cali_eyeangle = [cali_eye_yaw, cali_eye_pitch]
            color = unnormal_color
        
        text_info.append(("head:["+str(cali_head_yaw)+"," +
                        str(cali_head_pitch) + "," + 
                        str(cali_head_roll) + "]", (300, 55), color))
        
        text_info.append(("eye:["+str(cali_eye_yaw)+"," +
                        str(cali_eye_pitch) + "]" , (300, 75), color))
        color = normal_color    

    # face important info
    # text_info.append(("face_yaw: " + str(int(face_info["yaw"])), (550, 90), color))
    # text_info.append(("face_pitch: " + str(int(face_info["pitch"])), (550, 105), color))
    # text_info.append(("face_roll: " + str(int(face_info["roll"])), (550, 180), color))
    # face chaos info
    # text_info.append(("is_mask: " + str(face_info["isMask"]), (5, 10), color))
    # text_info.append(("is_glass: " + str(face_info["isGlass"]), (5, 25), color))
    # text_info.append(("is_irblock: " + str(face_info["isIRBlock"]), (5, 40), color))
    
    # warning: distraction
    cv2.putText(show, "DISTRACTION", (550, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.8, color, 1, cv2.LINE_AA)
    if message["dms_result"]["distraction_type"] != 0:
        color = warning_color
    text_info.append(("type: " + str(message["dms_result"]["distraction_type"]), (550, 40), color))
    color = normal_color
    
    show = display_distraction_params(show, distraction_params, face_info)
    if distraction_params != {}:
        show = mapping_point(show, distraction_params, region_label)
        
    if str(message["dms_result"]["distraction_reason"]) != "no_distraction":
        color = warning_color
    text_info.append(("reason:"+str(message["dms_result"]["distraction_reason"]), (550, 60), color))
    color = normal_color

    text_info.append(("headyawbias:"+str(round(distraction_params["current_head_yaw_bias"], 2)), (550, 80), color))
    show = show_progress_bar(show, (message["distraction_info"]["distraction_continue_time"]), 
                            message["distraction_info"]["time_gap"], (550, 100))
    
    # fatigue
    cv2.putText(show, "FATIGUE", (800, 350), cv2.FONT_HERSHEY_SIMPLEX, 0.8, color, 1, cv2.LINE_AA)
    if message["dms_result"]["drowsiness_type"] != 0:
        color = warning_color
    text_info.append(("type: " + str(message["dms_result"]["drowsiness_type"]), (800, 370), color))
    color = normal_color
    show = showinfo_linebyline(show, tired_params, 800, 390)
    
    # show = showinfo_linebyline(show, distraction_params, 800, 290)
    # text_info.append(("fatigue:"+str(message["distraction_info"]["fatigue_level"]), (550, 170), color))

    
    text_info.append(("distraction_info:" , (350, 580), color)) #+ str(message["distraction_info"])
    text_info.append(("continue_percent:" + str(message["distraction_info"]["distraction_continue_percent"]) , (350, 600), color))
    text_info.append(("continue_time:"+ str(message["distraction_info"]["distraction_continue_time"]) , (350, 620), color))
    text_info.append(("sum_time:" + str(message["distraction_info"]["distraction_sum_time"]), (350, 640), color))
    text_info.append(("front_continue_time:" + str(message["distraction_info"]["distraction_front_continue_time"]), (350, 660), color))
    # text_info.append(("time_gap:" + str(message["distraction_info"]["time_gap"]), (350, 680), color))
    
    for text, position, color in text_info:
        show = add_text(show, text, position, color)
        
    return show


def update_distraction_data(file_path, message, label_):
    distraction_params_str = message["dms_result"].get("distraction_params", "{}")
    distraction_params = {}
    try:
        distraction_params = json.loads(distraction_params_str)
    except json.JSONDecodeError as e:
        print(f"Error parsing distraction_params: {e}")
    if display_distraction_params != {}:
        mapping_x = distraction_params["mapping_x"]
        mapping_y = distraction_params["mapping_y"]
        region_hulls = distraction_params["region_hulls"]
        
        cali_head_pitch = distraction_params["headpose_pitch_"]
        cali_head_yaw = distraction_params["headpose_yaw_"]
        cali_head_roll = distraction_params["headpose_roll_"]
        cali_eye_pitch = distraction_params["gaze_pitch_mean"]
        cali_eye_yaw = distraction_params["gaze_yaw_mean"]
        
        head_pitch = message["dms_result"]["face_info"]["pitch"]
        head_yaw = message["dms_result"]["face_info"]["yaw"]
        head_roll = message["dms_result"]["face_info"]["roll"]
        
        reye_pitch = message["dms_result"]["face_info"]["right_eye_landmark"]["pitch"]
        reye_yaw = message["dms_result"]["face_info"]["right_eye_landmark"]["yaw"]
        leye_pitch = message["dms_result"]["face_info"]["left_eye_landmark"]["pitch"]
        leye_yaw = message["dms_result"]["face_info"]["left_eye_landmark"]["yaw"]


        new_data = {
            "mapping_x": mapping_x,
            "mapping_y": mapping_y,
            "label": label_,
            "head_yaw": head_yaw,
            "head_pitch": head_pitch,
            "head_roll": head_roll,
            "reye_yaw": reye_yaw,
            "reye_pitch": reye_pitch,
            "leye_yaw": leye_yaw,
            "leye_pitch": leye_pitch,
            "cali_head_yaw": cali_head_yaw,
            "cali_head_pitch": cali_head_pitch,
            "cali_head_roll": cali_head_roll,
            "cali_eye_pitch": cali_eye_pitch,
            "cali_eye_yaw": cali_eye_yaw
        }

        if not os.path.exists(file_path):
            data = {
                "region_hulls": region_hulls,
                "created_at": datetime.now().strftime("%Y%m%d_%H%M%S"),
                "info": [new_data]
            }
        else:
            with open(file_path, "r") as file:
                data = json.load(file)
            
            if "info" in data:
                data["info"].append(new_data)
            else:
                data["info"] = [new_data]

        with open(file_path, "w") as file:
            json.dump(data, file, indent=4)
    
def preview_thread_enter():
    global save_flag
    global is_run_first
    global file_path
    region_label = -1
    
    while(True):
        image_data,message_data= preview_queue.get()
        message=json.loads(message_data)
        image=cv2.imdecode(image_data,0)

        # 处理主要的可视化逻辑
        processed_image = process_frame(image, message, region_label)
        # # save raw data                    
        # if is_run_first:
        #     date_str = datetime.now().strftime("%Y%m%d_%H%M%S")
        #     file_path = "./distractiondata_"+date_str+".json"
        #     is_run_first = False
        # if file_path is not None:
        #     if region_label != -1:
        #         update_distraction_data(file_path, message, region_label)
            
            
        key = cv2.waitKey(2) & 0xFF
        if ord('a') <= key <= ord('o'):
            region_label = key - ord('a')
            print(f"Key '{chr(key)}' pressed: {region_label}")
        elif key == 27:  
            region_label = -1
            print("ESC key pressed. Exiting...")
        elif key == ord('q'):
            sys.exit(0)
        elif key == ord('r'):
            save_flag = True
        elif key == ord('p'):
            save_flag = False
    
        if save_flag:
            processed_image = cv2ImgAddText(processed_image, "recording:" + str(save_flag), 500, 300, (255, 0, 0), 18)

        if processed_image is not None:
            cv2.imshow("DMS", processed_image)
        



async def recv_ws_data(ptr):
    last_save_path=""
    async with websockets.connect(url) as websocket:
        while websocket.open and ptr.is_alive():
            try:
                response = await websocket.recv()
                dms_frame_data = tongxing_dms_pb2.DMS_Frame()
                dms_frame_data.ParseFromString(response)
                print(f'Received: {dms_frame_data.image_type},{dms_frame_data.image_height},{dms_frame_data.image_width}')
                if(dms_frame_data.image_type=="JPG"):
                    image_data=np.frombuffer(dms_frame_data.image_data, dtype=np.uint8)
                    # image=np.reshape(image,[dms_frame_data.image_height,dms_frame_data.image_width])
                    #自动保存数据
                    timestamp = time.time()
                    date = datetime.fromtimestamp(int(timestamp/auto_make_folder_time)*auto_make_folder_time)
                    date_str=date.strftime("%Y-%m-%d-%H-%M-%S")
                    output_folder_name=output_path+"/"+date_str
                    output_image_file_name=output_folder_name+"/"+str(int(timestamp*1000))+".jpg"
                    output_image_json_name=output_folder_name+"/"+str(int(timestamp*1000))+".json"
                    # cv2.imwrite(output_image_file_name,image)
                    # print(dms_frame_data.message_data)
                    try:
                        message=json.loads(dms_frame_data.message_data)
                        # print(message)
                    except Exception as e:
                        print('recv_ws_data error:', e, message)
                        continue
                    
                    if save_flag and message["dms_result"]["face_info"]["score"]>0.5:
                        if(last_save_path != output_folder_name):
                            os.makedirs(output_folder_name, exist_ok=True)
                        with open(output_image_json_name, 'w') as f:
                            f.write(dms_frame_data.message_data)
                        with open(output_image_file_name, 'wb') as f:
                            print(output_image_file_name, 'save img data...........................')
                            f.write(image_data)
                    if(preview_queue.full()):
                        preview_queue.get()
                    preview_queue.put((image_data,dms_frame_data.message_data))           
            except websockets.exceptions.ConnectionClosedError as e:
                print(f'Connection closed by server: {e}')
                break
            except Exception as e:
                print(f'Error receiving data: {e}')
                break
            
if __name__ == "__main__":
    preview_thread = Thread(target=preview_thread_enter,args=())
    preview_thread.start()
    
    asyncio.run(recv_ws_data(preview_thread))
    preview_thread.join()
