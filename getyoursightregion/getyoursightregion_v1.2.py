import tkinter as tk
from tkinter import simpledialog, filedialog, messagebox
from tkinterdnd2 import TkinterDnD, DND_FILES
from PIL import Image, ImageTk, ImageChops
import json
import os
from datetime import datetime
import cv2 as cv

class PolygonDrawer:
    def __init__(self, root):
        self.root = root
        self.canvas = tk.Canvas(root, width=800, height=800, bg='white')
        self.canvas.pack(fill=tk.BOTH, expand=True)

        self.scale = 1.0  
        self.polygons = []  
        self.current_points = []  
        self.dragging_point_index = None
        self.selected_polygon_index = None 
        self.image_path = None
        self.image = None
        self.image_id = None
        self.isadded = False

        self.load_button = tk.Button(root, text="选择图片", command=self.load_image)
        self.load_button.place(x=10, y=10)

        self.undo_button = tk.Button(root, text="撤回", command=self.undo)
        self.undo_button.place(x=80, y=10)

        self.finish_button = tk.Button(root, text="完成轮廓", command=self.finish_polygon)
        self.finish_button.place(x=120, y=10)

        self.save_button = tk.Button(root, text="保存数据", command=self.save_polygons)
        self.save_button.place(x=180, y=10)

        # 绑定鼠标和拖拽事件
        self.canvas.bind("<Button-1>", self.on_click)
        self.canvas.bind("<B1-Motion>", self.on_drag)
        self.canvas.bind("<ButtonRelease-1>", self.on_release)
        self.canvas.bind("<MouseWheel>", self.zoom_image)  # 绑定滚轮缩放事件
        root.drop_target_register(DND_FILES)
        root.dnd_bind('<<Drop>>', self.on_drop)

    def load_image(self):
        filepath = filedialog.askopenfilename(filetypes=[("Image Files", "*.png;*.jpg;*.jpeg")])
        if filepath:
            self.image_path = filepath
            self.polygons.clear()  
            self.current_points.clear()  
            json_filepath = os.path.splitext(filepath)[0] + ".json"
            if os.path.exists(json_filepath):
                self.load_polygons(json_filepath)
                
            self.add_image(filepath, is_restart=True)

    def add_image(self, filepath, is_restart=False):
        img = Image.open(filepath).convert("RGBA")  
        if self.image is None or is_restart:
            self.isadded = False
            self.image = img
        else:
            self.isadded = True
            img = self.remove_white_background(img)
            self.image = Image.alpha_composite(self.image, img)

        self.display_image()

    def zoom_image(self, event):
        # 控制缩放的变化步长
        scale_step = 0.1
        # 判断滚轮方向，调整缩放比例
        if event.delta > 0:
            self.scale += scale_step
        elif event.delta < 0 and self.scale > scale_step:
            self.scale -= scale_step

        # 缩放图像并更新画布
        self.display_image()

    def display_image(self):
        if self.image is None:
            return
        # 根据缩放比例调整图像大小
        scaled_image = self.image.resize(
            (int(self.image.width * self.scale), int(self.image.height * self.scale)),
            Image.Resampling.LANCZOS
        )
        self.tk_image = ImageTk.PhotoImage(scaled_image)
        self.canvas.config(width=self.tk_image.width(), height=self.tk_image.height())
        self.canvas.delete("all")
        self.image_id = self.canvas.create_image(0, 0, anchor=tk.NW, image=self.tk_image)

        # 根据缩放比例调整并重新绘制多边形
        self.redraw()

    def remove_white_background(self, img):
        # 去除白色背景
        white = Image.new("RGBA", img.size, (255, 255, 255, 255))
        diff = ImageChops.difference(img, white)
        diff = ImageChops.add(diff, diff, 2.0, -100)
        mask = diff.convert("L").point(lambda p: 255 if p > 0 else 0)
        img.putalpha(mask)
        return img

    def on_drop(self, event):
        # 当文件拖拽到窗口时触发
        filepath = event.data
        if os.path.isfile(filepath) and filepath.lower().endswith((".png", ".jpg", ".jpeg")):
            self.add_image(filepath)

    def on_click(self, event):
        # 将点击位置转换为原始尺寸坐标
        x, y = event.x / self.scale, event.y / self.scale
        clicked_polygon_index, clicked_point_index = self.find_clicked_polygon(x, y)
        
        if clicked_polygon_index is not None and clicked_point_index is not None:
            self.selected_polygon_index = clicked_polygon_index
            self.dragging_point_index = clicked_point_index
        else:
            current_point_index = self.find_closest_point_in_current(x, y)
            if current_point_index is not None:
                self.dragging_point_index = current_point_index
            else:
                self.current_points.append((x, y))  # 添加为原始尺寸坐标
        self.redraw()

    def on_drag(self, event):
        if self.dragging_point_index is not None:
            x, y = event.x / self.scale, event.y / self.scale  # 转换为原始尺寸坐标
            if self.selected_polygon_index is not None:
                self.polygons[self.selected_polygon_index]['points'][self.dragging_point_index] = (x, y)
            elif self.current_points:
                self.current_points[self.dragging_point_index] = (x, y)
            self.redraw()

    def on_release(self, event):
        self.dragging_point_index = None
        self.selected_polygon_index = None

    def undo(self):
        if self.current_points:
            self.current_points.pop()
            self.redraw()

    def finish_polygon(self):
        if len(self.current_points) < 3:
            messagebox.showwarning("警告", "多边形至少需要三个点！")
            return
        label = simpledialog.askstring("输入标签", "为这个轮廓输入标签：")
        if label:
            self.polygons.append({'points': self.current_points[:], 'label': label})
            self.current_points.clear()
            self.redraw()
        else:
            messagebox.showwarning("警告", "标签不能为空")

    def redraw(self):
        self.canvas.delete("polygon")
        if self.image:
            self.canvas.create_image(0, 0, anchor=tk.NW, image=self.tk_image)

        # 绘制中心辅助线
        print(f"{self.image.width }=")
        center_x = (self.image.width * self.scale) / 2
        center_y = (self.image.height * self.scale) / 2
        self.canvas.create_line(center_x, 0, center_x, self.image.height * self.scale, dash=(5, 1))
        self.canvas.create_line(0, center_y, self.image.width * self.scale, center_y, dash=(5, 1))

        # 绘制多边形
        for i, polygon in enumerate(self.polygons):
            scaled_points = [(x * self.scale, y * self.scale) for x, y in polygon['points']]
            label = str(i)
            self.draw_polygon(scaled_points)
            if scaled_points:
                self.canvas.create_text(scaled_points[0][0] + 10, scaled_points[0][1] - 10, text=label, fill='black')

        # 绘制当前点
        if self.current_points:
            scaled_current_points = [(x * self.scale, y * self.scale) for x, y in self.current_points]
            self.draw_polygon(scaled_current_points)


    def draw_polygon(self, points):
        for i, (x, y) in enumerate(points):
            self.canvas.create_oval(x-3, y-3, x+3, y+3, fill='orange', tags="polygon")
            if i > 0:
                self.canvas.create_line(points[i-1], (x, y), fill='green', tags="polygon")
        if len(points) > 2:
            self.canvas.create_line(points[-1], points[0], fill='green', tags="polygon")

    def load_polygons(self, json_filepath):
        try:
            with open(json_filepath, 'r') as f:
                data = json.load(f)
                
            self.polygons.clear()
            
            if "region_hull" in data:
                region_hull_data = data["region_hull"]
                
                # 将 region_hull 数据转化为多边形点格式
                for hulls in region_hull_data:
                    for hull in hulls:
                        print(f'{hull=}')
                        points = [(hull[i], hull[i+1]) for i in range(0, len(hull), 2)]
                        self.polygons.append({"points": points, "label": "region_hull"}) 
                
                print(f"加载 region_hull 成功: {json_filepath}")
            else:
                print(f"文件中不包含 region_hull: {json_filepath}")
                
            # 重新绘制加载的多边形
            self.redraw()

        except Exception as e:
            print(f"无法加载 JSON 文件: {e}")

    def save_polygons(self):
        if not self.polygons or not self.image_path:
            return

        date_str = datetime.now().strftime("%Y%m%d_%H%M%S")
        raw_jsonfilepath = os.path.splitext(self.image_path)[0] + ".json"

        if self.isadded:
            json_filepath = os.path.splitext(self.image_path)[0] + date_str + "_added.json"
            img_save_path = os.path.splitext(self.image_path)[0] + date_str + "_added.png"
            tmp_image = self.image.copy()
            tmp_image = tmp_image.convert("RGB")
            tmp_image.save(img_save_path)
            messagebox.showinfo("保存叠加图像", f"数据已保存到: {img_save_path}")
        else:
            json_filepath = raw_jsonfilepath

        try:
            with open(raw_jsonfilepath, 'r') as f:
                data = json.load(f)
        except FileNotFoundError:
            data = {}

        # 使用原始尺寸坐标保存多边形数据
        formatted_region_hull = [[
            [float(round(coord, 2)) for point in polygon['points'] for coord in point]
            for polygon in self.polygons
        ]]

        data["region_hull"] = formatted_region_hull

        with open(json_filepath, 'w') as f:
            json.dump(data, f, indent=4)
            
        messagebox.showinfo("保存成功", f"数据已保存到: {json_filepath}")


    def find_clicked_polygon(self, x, y):
        for poly_index, polygon in enumerate(self.polygons):
            for point_index, (px, py) in enumerate(polygon['points']):
                if abs(px - x) < 5 and abs(py - y) < 5:
                    return poly_index, point_index
        return None, None

    def find_closest_point_in_current(self, x, y):
        for i, (px, py) in enumerate(self.current_points):
            if abs(px - x) < 5 and abs(py - y) < 5:
                return i
        return None

root = TkinterDnD.Tk()
root.title("GetYourRegion")
app = PolygonDrawer(root)
root.mainloop()
