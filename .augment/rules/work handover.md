---
type: "agent_requested"
description: "当用户需要暂时放下当前工作以及方便下次快速了解未完成的工作时需要按照此文档进行进度记录"
---
由于对话上下文过长，需要创建一个完整的工作进度总结报告，以便后续接手的AI助手能够快速理解项目状态并无缝继续未完成的工作。

请提供以下内容：

1. **项目概览**：
   - 项目名称和核心目标
   - 当前所处的开发阶段
   - 技术栈和架构选择

2. **已完成工作总结**：
   - 按时间顺序列出已完成的主要任务
   - 每个任务的具体成果和文件位置
   - 重要的技术决策和解决方案

3. **当前状态**：
   - 正在进行的任务详情
   - 已解决的技术问题和采用的方案
   - 当前代码库的运行状态

4. **待完成工作**：
   - 明确的下一步任务列表
   - 每个任务的优先级和预估工作量
   - 具体的实现要求和验收标准

5. **重要注意事项**：
   - 必须遵循的开发规范和原则
   - 关键的配置信息和依赖关系
   - 潜在的风险点和注意事项

6. **快速上手指南**：
   - 环境搭建和启动步骤
   - 关键文件和目录结构说明
   - 测试和验证方法

请确保信息完整、准确，便于新的AI助手快速接手并继续开发工作。