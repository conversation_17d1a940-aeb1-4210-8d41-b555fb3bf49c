---
type: "agent_requested"
description: "当需要使用git进行版本管理时需要严格遵循此份git规范"
---
# Git 提交规范

为保证项目代码的可追溯性和自动化，所有 Git 提交都应遵循本规范。本规范包含**提交信息格式 (Commit Message)** 和 **提交内容组织 (Commit Content)** 两部分。

---

## 第一部分：提交信息格式 (Conventional Commits)

核心理念：**类型和范围 (Type/Scope) 使用英文，主题和正文 (Subject/Body) 推荐使用中文，以便更清晰地描述意图。**

### 提交信息结构

```
<type>(<scope>): <subject>
<BLANK LINE>
<body>
<BLANK LINE>
<footer>

```

### 1. 页眉 (Header) - **必需**

页眉只有一行，包含**类型 (type)**、**范围 (scope)** 和 **主题 (subject)**。

### **类型 (Type)** - 英文

必须是以下关键字之一，描述了本次提交的性质：

- **feat**: 新功能 (feature)
- **fix**: 修复 Bug
- **docs**: 仅仅修改了文档 (documentation)
- **style**: 不影响代码含义的修改 (空格、格式化、缺少分号等)
- **refactor**: 代码重构，既不是修复 Bug 也不是添加新功能
- **perf**: 提升性能的代码修改
- **test**: 增加或修改测试
- **build**: 修改项目构建系统或外部依赖 (例如: `pyproject.toml`, `npm`)
- **ci**: 修改 CI 配置文件和脚本 (例如: GitHub Actions)
- **chore**: 其他不修改 `src` 或 `tests` 目录的提交 (例如: 更新 `.gitignore`)
- **revert**: 撤销一个先前的提交

### **范围 (Scope)** - 英文

用于说明本次提交影响的范围，通常是**模块名或功能名**。

- **示例**: `feat(api)`, `fix(strategy)`, `docs(readme)`

### **主题 (Subject)** - 中文

对本次修改的简洁描述，**强烈推荐使用中文**。

- **规则**:
    - 清晰地描述做了什么，避免模糊不清。
    - 结尾不加句号 (`.`)。
- **中英混搭示例**:
    - `feat(auth): 实现 JWT 用户身份验证`
    - `fix(parser): 正确处理空的 input 输入`
    - `refactor(utils): 重构` output_formatter `以支持多种格式`

### 2. 正文 (Body) - 中文

对本次修改的详细描述，解释修改的**原因**和**背景**。

### 3. 页脚 (Footer) - 可选

用于标记**重大更改 (Breaking Changes)** 或 **关联 Issue**。

---

## 第二部分：提交内容组织 (Commit Content)

除了写好提交信息，如何组织每次提交的内容同样重要。这决定了项目历史是否清晰、是否易于回溯。

### 1. 保持提交的原子性 (Atomic Commits)

**这是最重要的一条原则。**

- **一个提交只做一件事**。避免将多个不相关的改动混在一个提交里。例如，修复一个 Bug 和添加一个新功能应该是两次独立的提交。
- **优点**:
    - **可读性高**: 清晰地知道每次提交的目的。
    - **易于回溯**: 当发现 Bug 时，可以轻松地通过 `git bisect` 定位到引入问题的那个提交。
    - **便于 Code Review**: Reviewer 可以专注于一个独立的、完整的变更。
    - **易于撤销**: 如果某个功能不需要了，可以安全地 `git revert` 对应的提交，而不会影响其他功能。

### 2. 按逻辑顺序组织提交

当一个功能需要多次提交才能完成时，应确保提交的顺序符合逻辑。

- **示例**: 开发一个新功能时，推荐的提交顺序可以是：
    1. `refactor(core): 为新功能重构底层模块`
    2. `feat(core): 实现新功能的核心逻辑`
    3. `feat(api): 为新功能添加入站接口`
    4. `test(api): 为新功能的 API 编写集成测试`
    5. `docs(guide): 更新新功能的使用文档`

### 3. 提交前进行代码自审

在执行 `git commit` 前，请先使用 `git diff --staged` 检查本次将要提交的内容，确保：

- 没有包含意外的修改（如调试代码 `console.log`, `print()`）。
- 没有包含不必要的空行或格式错误。
- 所有修改都与本次提交的主题相关。

### 4. 确保提交是完整的

- 一个提交应该是一个**完整的功能单元**。例如，一次代码重构的提交，应该包含所有因重构而需要修改的地方，提交后项目应该能正常编译和运行。
- 避免提交一个“半成品”，这会导致项目历史中出现无法正常工作的节点。