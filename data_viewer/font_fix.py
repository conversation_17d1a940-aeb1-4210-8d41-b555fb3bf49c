#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字体修复模块
在导入时自动设置正确的中文字体
"""

def setup_chinese_fonts():
    """设置中文字体支持"""
    
    # 1. 设置matplotlib字体
    try:
        import matplotlib.pyplot as plt
        import matplotlib.font_manager as fm
        
        # 系统确认可用的字体
        available_chinese_fonts = ['AR PL UMing CN', 'AR PL UKai CN']
        
        # 验证字体是否真的可用
        available_fonts = [f.name for f in fm.fontManager.ttflist]
        working_font = None
        
        for font in available_chinese_fonts:
            if font in available_fonts:
                working_font = font
                break
        
        if working_font:
            # 设置matplotlib字体
            plt.rcParams['font.sans-serif'] = [working_font, 'DejaVu Sans', 'Arial', 'sans-serif']
            plt.rcParams['axes.unicode_minus'] = False
            print(f"✓ matplotlib字体设置: {working_font}")
        else:
            print("⚠ matplotlib未找到中文字体，使用默认字体")
            
    except ImportError:
        print("⚠ matplotlib未安装")
    
    # 2. 设置tkinter字体（在有显示环境时）
    try:
        import tkinter as tk
        import tkinter.font as tkfont
        import os
        
        # 检查是否有显示环境
        if 'DISPLAY' not in os.environ:
            print("⚠ 无显示环境，跳过tkinter字体测试")
            return 'AR PL UMing CN'  # 返回已知可用的字体
        
        # 创建临时root测试字体
        test_root = tk.Tk()
        test_root.withdraw()
        
        working_tk_font = None
        tk_fonts = ['AR PL UMing CN', 'AR PL UKai CN', 'Sans']
        
        for font_name in tk_fonts:
            try:
                test_font = tkfont.Font(family=font_name, size=9)
                working_tk_font = font_name
                break
            except:
                continue
        
        test_root.destroy()
        
        if working_tk_font:
            print(f"✓ tkinter字体准备: {working_tk_font}")
            return working_tk_font
        else:
            print("⚠ tkinter未找到合适字体")
            return None
            
    except (ImportError, Exception) as e:
        print(f"⚠ tkinter字体测试跳过: {e}")
        return 'AR PL UMing CN'  # 返回已知可用的字体

# 在模块导入时自动执行字体设置
if __name__ != "__main__":
    setup_chinese_fonts()

def apply_font_to_root(root, font_name=None):
    """为tkinter root应用字体设置"""
    if font_name is None:
        font_name = 'AR PL UMing CN'
    
    try:
        import tkinter.font as tkfont
        
        # 设置默认字体
        default_font = tkfont.nametofont("TkDefaultFont")
        default_font.configure(family=font_name, size=9)
        
        text_font = tkfont.nametofont("TkTextFont") 
        text_font.configure(family=font_name, size=9)
        
        menu_font = tkfont.nametofont("TkMenuFont")
        menu_font.configure(family=font_name, size=9)
        
        print(f"✓ 应用tkinter字体: {font_name}")
        
    except Exception as e:
        print(f"⚠ 字体应用失败: {e}")

if __name__ == "__main__":
    print("字体修复模块测试")
    font = setup_chinese_fonts()
    print(f"推荐字体: {font}") 