#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用现有数据测试可视化工具
"""

import pandas as pd
from pathlib import Path
import sys

def find_csv_file():
    """查找CSV数据文件"""
    # 可能的数据文件路径
    possible_paths = [
        "../keypoint_viewer/data/face_keypoints_temporal_stability_4.csv",
        "../../keypoint_viewer/data/face_keypoints_temporal_stability_4.csv", 
        "../data/face_keypoints_temporal_stability_4.csv"
    ]
    
    for path in possible_paths:
        if Path(path).exists():
            return path
    return None

def test_data_loading():
    """测试数据加载"""
    csv_path = find_csv_file()
    
    if csv_path:
        print(f"找到数据文件: {csv_path}")
        try:
            df = pd.read_csv(csv_path)
            print(f"✓ 数据加载成功")
            print(f"✓ 数据行数: {len(df)}")
            print(f"✓ 数据列: {list(df.columns)}")
            print(f"✓ 关键点范围: {df['keypoint_id'].min()} - {df['keypoint_id'].max()}")
            print(f"✓ 帧范围: {df['frame_id'].min()} - {df['frame_id'].max()}")
            
            # 检查是否有velocity列
            if 'velocity' in df.columns:
                print(f"✓ 速度数据可用，范围: {df['velocity'].min():.2f} - {df['velocity'].max():.2f}")
            else:
                print("! 注意: 没有velocity列")
                
            return csv_path
        except Exception as e:
            print(f"✗ 数据加载失败: {e}")
    else:
        print("未找到数据文件")
        print("请确保数据文件在以下位置之一：")
        for path in possible_paths:
            print(f"  - {path}")
    
    return None

def main():
    """主函数"""
    print("=== 数据文件测试 ===")
    
    csv_path = test_data_loading()
    
    if csv_path:
        print(f"\n数据文件可用: {csv_path}")
        print("您可以在可视化工具中加载此文件进行分析")
        
        print("\n启动可视化工具...")
        try:
            from keypoint_visualizer import main as start_visualizer
            start_visualizer()
        except Exception as e:
            print(f"启动错误: {e}")
            print("请直接运行: python run.py")
    else:
        print("\n没有找到合适的数据文件")
        print("您可以：")
        print("1. 运行 python quick_start.py 创建示例数据")
        print("2. 手动在GUI中加载您的CSV文件")

if __name__ == "__main__":
    main() 