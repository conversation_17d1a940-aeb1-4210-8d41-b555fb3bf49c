#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字体测试脚本
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import tkinter as tk
import tkinter.font as tkfont

def test_matplotlib_fonts():
    """测试matplotlib字体"""
    print("=== matplotlib字体测试 ===")
    
    # 获取系统可用字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    
    chinese_fonts = [
        'Noto Sans CJK SC',
        'Noto Sans CJK TC', 
        'AR PL UMing CN',
        'AR PL UKai CN',
        'WenQuanYi Micro Hei',
        'SimHei',
        'Microsoft YaHei'
    ]
    
    print("系统中可用的中文字体:")
    found_fonts = []
    for font in chinese_fonts:
        if font in available_fonts:
            found_fonts.append(font)
            print(f"✓ {font}")
        else:
            print(f"✗ {font}")
    
    if found_fonts:
        print(f"\n推荐使用: {found_fonts[0]}")
        # 设置字体
        plt.rcParams['font.sans-serif'] = [found_fonts[0]] + plt.rcParams['font.sans-serif']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 创建测试图
        fig, ax = plt.subplots(figsize=(8, 6))
        ax.plot([1, 2, 3, 4], [1, 4, 2, 3], 'o-', label='测试数据')
        ax.set_title('中文字体测试图表')
        ax.set_xlabel('X轴坐标')
        ax.set_ylabel('Y轴数值')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 保存测试图
        plt.savefig('font_test.png', dpi=150, bbox_inches='tight')
        plt.close()
        print("测试图表已保存为 font_test.png")
        
        return found_fonts[0]
    else:
        print("未找到合适的中文字体")
        return None

def test_tkinter_fonts():
    """测试tkinter字体"""
    print("\n=== tkinter字体测试 ===")
    
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    chinese_fonts = [
        'Noto Sans CJK SC',
        'AR PL UMing CN', 
        'AR PL UKai CN',
        'WenQuanYi Micro Hei',
        'SimHei',
        'Microsoft YaHei',
        'Sans'
    ]
    
    available_tk_fonts = []
    for font_name in chinese_fonts:
        try:
            test_font = tkfont.Font(family=font_name, size=10)
            available_tk_fonts.append(font_name)
            print(f"✓ {font_name} (tkinter可用)")
        except:
            print(f"✗ {font_name} (tkinter不可用)")
    
    root.destroy()
    
    if available_tk_fonts:
        print(f"tkinter推荐字体: {available_tk_fonts[0]}")
        return available_tk_fonts[0]
    else:
        print("tkinter未找到合适字体")
        return None

def main():
    """主函数"""
    print("字体兼容性测试\n")
    
    # 测试matplotlib字体
    mpl_font = test_matplotlib_fonts()
    
    # 测试tkinter字体
    tk_font = test_tkinter_fonts()
    
    print("\n=== 测试总结 ===")
    if mpl_font:
        print(f"matplotlib建议字体: {mpl_font}")
    if tk_font:
        print(f"tkinter建议字体: {tk_font}")
    
    print("\n建议:")
    if mpl_font and tk_font:
        print("✓ 字体支持良好，中文显示应该正常")
    elif mpl_font or tk_font:
        print("⚠ 部分字体支持，可能有显示问题")
    else:
        print("✗ 字体支持较差，建议安装中文字体")
        print("可以尝试安装: sudo apt-get install fonts-noto-cjk")

if __name__ == "__main__":
    main() 