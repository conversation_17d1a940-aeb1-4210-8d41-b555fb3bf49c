#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关键点可视化工具启动脚本
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    from keypoint_visualizer import main
    
    if __name__ == "__main__":
        print("=== 关键点时序数据可视化工具 ===")
        print("功能特性：")
        print("✓ 交互式选择X/Y轴变量")
        print("✓ 关键点选择和过滤")
        print("✓ 实时文件监控更新")
        print("✓ 多种显示模式（线图/散点图/组合）")
        print("✓ 图表导出（PNG/PDF/SVG）")
        print("✓ 支持大数据量（20000+行）")
        print("================================")
        print("\n启动工具...")
        
        main()
        
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保安装了所需的依赖包：")
    print("pip install pandas numpy matplotlib")
    sys.exit(1)
except Exception as e:
    print(f"启动错误: {e}")
    sys.exit(1) 