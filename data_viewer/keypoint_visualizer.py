#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关键点时序数据可视化工具
支持交互式选择轴变量、关键点过滤、实时更新等功能
"""

# 导入字体修复模块
import font_fix

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
from matplotlib.figure import Figure
import matplotlib.colors as mcolors
from pathlib import Path
import threading
import time
import os
from typing import Dict, List, Optional, Tuple

class KeypointVisualizer:
    def __init__(self, root):
        self.root = root
        self.root.title("关键点时序数据可视化工具")
        self.root.geometry("1400x800")
        
        # 应用字体修复
        font_fix.apply_font_to_root(self.root)
        
        # 数据相关
        self.df: Optional[pd.DataFrame] = None
        self.csv_file_path: Optional[str] = None
        self.last_modified_time: float = 0
        self.update_thread: Optional[threading.Thread] = None
        self.is_monitoring: bool = False
        
        # 图表相关
        self.figure: Optional[Figure] = None
        self.ax: Optional[plt.Axes] = None
        self.canvas: Optional[FigureCanvasTkAgg] = None
        
        # 关键点显示状态
        self.keypoint_visibility: Dict[int, bool] = {}
        self.keypoint_colors: Dict[int, str] = {}
        
        # 数值字段（可作为轴的字段）
        self.numeric_columns = ['frame_id', 'x', 'y', 'confidence', 'std_x', 'std_y', 
                               'velocity', 'acceleration', 'instability_score']
        
        # 初始化界面
        self.setup_ui()
        self.generate_keypoint_colors()
        

        
    def setup_ui(self):
        """初始化用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 左侧控制面板
        self.setup_control_panel(main_frame)
        
        # 右侧图表区域
        self.setup_chart_area(main_frame)
        
    def setup_control_panel(self, parent):
        """设置左侧控制面板"""
        control_frame = ttk.Frame(parent, width=300)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 5))
        control_frame.pack_propagate(False)
        
        # 文件加载区域
        file_frame = ttk.LabelFrame(control_frame, text="数据文件", padding=10)
        file_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(file_frame, text="选择CSV文件", 
                  command=self.load_csv_file).pack(fill=tk.X, pady=(0, 5))
        
        self.file_label = ttk.Label(file_frame, text="未选择文件", 
                                   foreground="gray")
        self.file_label.pack(fill=tk.X)
        
        # 实时更新控制
        self.auto_update_var = tk.BooleanVar()
        ttk.Checkbutton(file_frame, text="实时监控文件变化", 
                       variable=self.auto_update_var,
                       command=self.toggle_auto_update).pack(anchor=tk.W, pady=(5, 0))
        
        # 轴选择区域
        axis_frame = ttk.LabelFrame(control_frame, text="轴选择", padding=10)
        axis_frame.pack(fill=tk.X, pady=(0, 10))
        
        # X轴选择
        ttk.Label(axis_frame, text="X轴:").pack(anchor=tk.W)
        self.x_axis_var = tk.StringVar(value="frame_id")
        self.x_axis_combo = ttk.Combobox(axis_frame, textvariable=self.x_axis_var,
                                        values=self.numeric_columns, state="readonly")
        self.x_axis_combo.pack(fill=tk.X, pady=(0, 10))
        self.x_axis_combo.bind('<<ComboboxSelected>>', self.on_axis_change)
        
        # Y轴选择
        ttk.Label(axis_frame, text="Y轴:").pack(anchor=tk.W)
        self.y_axis_var = tk.StringVar(value="velocity")
        self.y_axis_combo = ttk.Combobox(axis_frame, textvariable=self.y_axis_var,
                                        values=self.numeric_columns, state="readonly")
        self.y_axis_combo.pack(fill=tk.X)
        self.y_axis_combo.bind('<<ComboboxSelected>>', self.on_axis_change)
        
        # 显示模式选择
        mode_frame = ttk.LabelFrame(control_frame, text="显示模式", padding=10)
        mode_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.plot_mode_var = tk.StringVar(value="line")
        ttk.Radiobutton(mode_frame, text="线图", variable=self.plot_mode_var, 
                       value="line", command=self.update_plot).pack(anchor=tk.W)
        ttk.Radiobutton(mode_frame, text="散点图", variable=self.plot_mode_var, 
                       value="scatter", command=self.update_plot).pack(anchor=tk.W)
        ttk.Radiobutton(mode_frame, text="线+点", variable=self.plot_mode_var, 
                       value="both", command=self.update_plot).pack(anchor=tk.W)
        
        # 关键点选择区域
        self.setup_keypoint_selection(control_frame)
        
        # 导出按钮
        export_frame = ttk.Frame(control_frame)
        export_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(export_frame, text="导出图表", 
                  command=self.export_chart).pack(fill=tk.X)
        
    def setup_keypoint_selection(self, parent):
        """设置关键点选择区域"""
        keypoint_frame = ttk.LabelFrame(parent, text="关键点选择", padding=10)
        keypoint_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 全选控制
        control_frame = ttk.Frame(keypoint_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.select_all_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(control_frame, text="全选", 
                       variable=self.select_all_var,
                       command=self.toggle_all_keypoints).pack(side=tk.LEFT)
        
        ttk.Button(control_frame, text="刷新", 
                  command=self.update_plot).pack(side=tk.RIGHT)
        
        # 分组选择
        group_frame = ttk.LabelFrame(keypoint_frame, text="分组选择")
        group_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.group_vars = {}
        groups = [
            ("0-9", range(10)),
            ("10-19", range(10, 20)),
            ("20-29", range(20, 30)),
            ("30-37", range(30, 38))
        ]
        
        for i, (label, keypoint_range) in enumerate(groups):
            var = tk.BooleanVar(value=True)
            self.group_vars[label] = (var, keypoint_range)
            ttk.Checkbutton(group_frame, text=label, variable=var,
                           command=lambda l=label: self.toggle_group(l)).grid(
                               row=i//2, column=i%2, sticky=tk.W, padx=5)
        
        # 单个关键点选择列表
        list_frame = ttk.Frame(keypoint_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 列表框
        self.keypoint_listbox = tk.Listbox(list_frame, yscrollcommand=scrollbar.set,
                                          selectmode=tk.MULTIPLE, height=8)
        self.keypoint_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=self.keypoint_listbox.yview)
        
        # 填充关键点列表
        for i in range(38):
            self.keypoint_listbox.insert(tk.END, f"关键点 {i}")
            self.keypoint_listbox.selection_set(i)  # 默认全选
        
        self.keypoint_listbox.bind('<<ListboxSelect>>', self.on_keypoint_selection_change)
        
    def setup_chart_area(self, parent):
        """设置右侧图表区域"""
        chart_frame = ttk.Frame(parent)
        chart_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 创建matplotlib图表
        self.figure = Figure(figsize=(10, 6), dpi=100)
        self.ax = self.figure.add_subplot(111)
        
        # 创建canvas
        self.canvas = FigureCanvasTkAgg(self.figure, chart_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 添加工具栏
        toolbar = NavigationToolbar2Tk(self.canvas, chart_frame)
        toolbar.update()
        
        # 初始化空图表
        self.ax.set_title("请选择CSV文件开始分析")
        self.ax.set_xlabel("X轴")
        self.ax.set_ylabel("Y轴")
        self.canvas.draw()
        
    def generate_keypoint_colors(self):
        """生成关键点颜色映射"""
        # 使用matplotlib的颜色循环生成38种不同的颜色
        colors = plt.cm.tab10(np.linspace(0, 1, 10))  # 基础10色
        additional_colors = plt.cm.Set3(np.linspace(0, 1, 12))  # 额外12色
        more_colors = plt.cm.Pastel1(np.linspace(0, 1, 9))  # 再9色
        extra_colors = plt.cm.Dark2(np.linspace(0, 1, 8))  # 最后8色
        
        # 确保有足够的颜色（需要38种颜色）
        all_colors = np.vstack([colors, additional_colors, more_colors, extra_colors])
        
        for i in range(38):
            color_index = i % len(all_colors)  # 使用模运算避免索引越界
            self.keypoint_colors[i] = mcolors.to_hex(all_colors[color_index])
            self.keypoint_visibility[i] = True
            
    def load_csv_file(self):
        """加载CSV文件"""
        file_path = filedialog.askopenfilename(
            title="选择CSV文件",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        
        if file_path:
            try:
                self.df = pd.read_csv(file_path)
                self.csv_file_path = file_path
                self.last_modified_time = os.path.getmtime(file_path)
                
                # 更新文件标签
                filename = Path(file_path).name
                self.file_label.config(text=f"已加载: {filename}", foreground="green")
                
                # 验证数据格式
                required_columns = ['frame_id', 'keypoint_id']
                if not all(col in self.df.columns for col in required_columns):
                    raise ValueError(f"CSV文件必须包含列: {required_columns}")
                
                # 更新轴选择下拉框
                available_columns = [col for col in self.numeric_columns if col in self.df.columns]
                self.x_axis_combo.config(values=available_columns)
                self.y_axis_combo.config(values=available_columns)
                
                # 更新图表
                self.update_plot()
                
                messagebox.showinfo("成功", f"成功加载CSV文件\n数据行数: {len(self.df)}")
                
            except Exception as e:
                messagebox.showerror("错误", f"加载CSV文件失败:\n{str(e)}")
                self.file_label.config(text="加载失败", foreground="red")
                
    def toggle_auto_update(self):
        """切换自动更新状态"""
        if self.auto_update_var.get():
            self.start_monitoring()
        else:
            self.stop_monitoring()
            
    def start_monitoring(self):
        """开始监控文件变化"""
        if not self.csv_file_path:
            messagebox.showwarning("警告", "请先选择CSV文件")
            self.auto_update_var.set(False)
            return
            
        self.is_monitoring = True
        self.update_thread = threading.Thread(target=self.monitor_file, daemon=True)
        self.update_thread.start()
        
    def stop_monitoring(self):
        """停止监控文件变化"""
        self.is_monitoring = False
        
    def monitor_file(self):
        """监控文件变化线程"""
        while self.is_monitoring and self.csv_file_path:
            try:
                current_time = os.path.getmtime(self.csv_file_path)
                if current_time > self.last_modified_time:
                    self.last_modified_time = current_time
                    # 在主线程中更新数据
                    self.root.after(0, self.reload_data)
                time.sleep(1)  # 每秒检查一次
            except Exception as e:
                print(f"监控文件错误: {e}")
                break
                
    def reload_data(self):
        """重新加载数据"""
        if self.csv_file_path:
            try:
                self.df = pd.read_csv(self.csv_file_path)
                self.update_plot()
            except Exception as e:
                print(f"重新加载数据错误: {e}")
                
    def get_selected_keypoints(self) -> List[int]:
        """获取选中的关键点列表"""
        selected_indices = self.keypoint_listbox.curselection()
        return [i for i in selected_indices if i < 38]
        
    def toggle_all_keypoints(self):
        """全选/全不选关键点"""
        if self.select_all_var.get():
            self.keypoint_listbox.selection_set(0, tk.END)
        else:
            self.keypoint_listbox.selection_clear(0, tk.END)
        self.update_plot()
        
    def toggle_group(self, group_label):
        """切换分组选择"""
        var, keypoint_range = self.group_vars[group_label]
        if var.get():
            # 选择该组的关键点
            for i in keypoint_range:
                if i < 38:
                    self.keypoint_listbox.selection_set(i)
        else:
            # 取消选择该组的关键点
            for i in keypoint_range:
                if i < 38:
                    self.keypoint_listbox.selection_clear(i)
        self.update_plot()
        
    def on_keypoint_selection_change(self, event):
        """关键点选择变化事件"""
        self.update_plot()
        
    def on_axis_change(self, event):
        """轴选择变化事件"""
        self.update_plot()
        
    def update_plot(self):
        """更新图表显示"""
        if self.df is None:
            return
            
        try:
            self.ax.clear()
            
            x_col = self.x_axis_var.get()
            y_col = self.y_axis_var.get()
            
            if x_col not in self.df.columns or y_col not in self.df.columns:
                self.ax.set_title("选择的列不存在于数据中")
                self.canvas.draw()
                return
                
            selected_keypoints = self.get_selected_keypoints()
            
            if not selected_keypoints:
                self.ax.set_title("请选择至少一个关键点")
                self.canvas.draw()
                return
                
            # 为每个选中的关键点绘制数据
            plot_mode = self.plot_mode_var.get()
            
            for keypoint_id in selected_keypoints:
                keypoint_data = self.df[self.df['keypoint_id'] == keypoint_id]
                
                if keypoint_data.empty:
                    continue
                    
                x_data = keypoint_data[x_col]
                y_data = keypoint_data[y_col]
                color = self.keypoint_colors.get(keypoint_id, 'blue')
                label = f'关键点 {keypoint_id}'
                
                if plot_mode == "line":
                    self.ax.plot(x_data, y_data, color=color, label=label, linewidth=1)
                elif plot_mode == "scatter":
                    self.ax.scatter(x_data, y_data, color=color, label=label, s=20, alpha=0.7)
                elif plot_mode == "both":
                    self.ax.plot(x_data, y_data, color=color, label=label, linewidth=1, marker='o', markersize=3)
                    
            self.ax.set_xlabel(x_col)
            self.ax.set_ylabel(y_col)
            self.ax.set_title(f"{y_col} vs {x_col} (选中 {len(selected_keypoints)} 个关键点)")
            
            # 只有当选中的关键点数量合理时才显示图例
            if len(selected_keypoints) <= 20:
                self.ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
            
            self.ax.grid(True, alpha=0.3)
            self.figure.tight_layout()
            self.canvas.draw()
            
        except Exception as e:
            self.ax.set_title(f"绘图错误: {str(e)}")
            self.canvas.draw()
            
    def export_chart(self):
        """导出图表"""
        if self.figure is None:
            messagebox.showwarning("警告", "没有可导出的图表")
            return
            
        file_path = filedialog.asksaveasfilename(
            title="保存图表",
            defaultextension=".png",
            filetypes=[
                ("PNG files", "*.png"),
                ("PDF files", "*.pdf"),
                ("SVG files", "*.svg"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            try:
                self.figure.savefig(file_path, dpi=300, bbox_inches='tight')
                messagebox.showinfo("成功", f"图表已保存到:\n{file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"保存图表失败:\n{str(e)}")


def main():
    """主函数"""
    root = tk.Tk()
    app = KeypointVisualizer(root)
    root.mainloop()


if __name__ == "__main__":
    main() 