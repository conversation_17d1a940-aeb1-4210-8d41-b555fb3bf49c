#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字体修复版启动脚本
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def setup_fonts():
    """预设置字体"""
    import matplotlib.pyplot as plt
    
    # 强制设置中文字体
    plt.rcParams['font.sans-serif'] = ['AR PL UMing CN', 'AR PL UKai CN', 'DejaVu Sans', 'sans-serif']
    plt.rcParams['axes.unicode_minus'] = False
    
    print("字体设置:")
    print(f"matplotlib字体: {plt.rcParams['font.sans-serif'][0]}")

try:
    # 预设置字体
    setup_fonts()
    
    from keypoint_visualizer import main
    
    if __name__ == "__main__":
        print("=== 关键点时序数据可视化工具 (字体修复版) ===")
        print("功能特性：")
        print("✓ 修复中文字体显示问题")
        print("✓ 交互式选择X/Y轴变量")
        print("✓ 关键点选择和过滤")
        print("✓ 实时文件监控更新")
        print("✓ 多种显示模式（线图/散点图/组合）")
        print("✓ 图表导出（PNG/PDF/SVG）")
        print("✓ 支持大数据量（20000+行）")
        print("===============================================")
        print("\n启动工具...")
        
        main()
        
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保安装了所需的依赖包：")
    print("pip install pandas numpy matplotlib")
    sys.exit(1)
except Exception as e:
    print(f"启动错误: {e}")
    sys.exit(1) 