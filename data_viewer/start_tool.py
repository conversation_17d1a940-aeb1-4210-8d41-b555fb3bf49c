#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关键点可视化工具启动器（字体修复版）
"""

import os
import sys
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def main():
    print("=== 关键点时序数据可视化工具 (字体修复版) ===")
    
    # 预设置字体环境
    try:
        import matplotlib.pyplot as plt
        
        # 强制使用已知可用的字体
        plt.rcParams['font.sans-serif'] = ['AR PL UMing CN', 'AR PL UKai CN', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        print("✓ matplotlib字体设置完成")
        
    except Exception as e:
        print(f"⚠ matplotlib字体设置失败: {e}")
    
    # 启动主程序
    try:
        from keypoint_visualizer import KeypointVisualizer
        import tkinter as tk
        
        # 创建主窗口
        root = tk.Tk()
        
        # 设置窗口字体
        try:
            import tkinter.font as tkfont
            
            # 设置默认字体
            default_font = tkfont.nametofont("TkDefaultFont")
            default_font.configure(family='AR PL UMing CN', size=9)
            
            text_font = tkfont.nametofont("TkTextFont")
            text_font.configure(family='AR PL UMing CN', size=9)
            
            print("✓ tkinter字体设置完成")
            
        except Exception as e:
            print(f"⚠ tkinter字体设置失败: {e}")
        
        # 创建应用
        app = KeypointVisualizer(root)
        
        print("✓ 工具启动成功")
        print("\n使用说明:")
        print("1. 点击'选择CSV文件'加载数据")
        print("2. 设置X轴=frame_id, Y轴=velocity")
        print("3. 选择要显示的关键点")
        print("4. 开始分析数据")
        
        # 运行主循环
        root.mainloop()
        
    except Exception as e:
        print(f"✗ 启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 