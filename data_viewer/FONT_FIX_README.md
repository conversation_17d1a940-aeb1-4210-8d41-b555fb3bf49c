# 字体显示问题修复说明

## 🔧 问题描述

在Linux系统中，可视化工具可能出现中文字体显示问题，表现为：

- matplotlib图表中的中文显示为方框或乱码
- tkinter界面控件的中文文字显示异常

## ✅ 修复方案

我们已经实现了多层次的字体修复方案：

### 1. 系统字体检测

- 自动检测系统中可用的中文字体
- 已确认您的系统支持：`AR PL UMing CN` 和 `AR PL UKai CN`

### 2. 字体修复模块

创建了 `font_fix.py` 模块，自动配置字体：

- matplotlib字体：`AR PL UMing CN`
- tkinter字体：`AR PL UMing CN`

### 3. 多种启动方式

#### 推荐方式1：使用字体修复版启动器

```bash
python start_tool.py
```

#### 方式2：使用修复版运行脚本

```bash
python run_fixed.py
```

#### 方式3：原始启动方式（已集成字体修复）

```bash
python run.py
```

## 🎯 使用建议

1. **首选启动方式**：

   ```bash
   cd /home/<USER>/tool_kit/data_viewer
   python start_tool.py
   ```
2. **验证字体效果**：

   - 启动后观察控制面板的中文是否正常显示
   - 加载数据后检查图表标题和轴标签的中文显示
3. **如果仍有问题**：

   - 字体问题通常不影响工具功能使用
   - 图表的核心功能（数据显示、交互、导出）都正常工作
   - 可以选择使用英文界面或导出图表后编辑

## 🔍 技术细节

### 修复内容

1. **matplotlib配置**：

   ```python
   plt.rcParams['font.sans-serif'] = ['AR PL UMing CN', 'AR PL UKai CN', 'DejaVu Sans']
   plt.rcParams['axes.unicode_minus'] = False
   ```
2. **tkinter字体设置**：

   ```python
   default_font.configure(family='AR PL UMing CN', size=9)
   ```

### 系统字体状态

- ✅ `AR PL UMing CN` - 可用
- ✅ `AR PL UKai CN` - 可用
- ✅ matplotlib字体测试通过
- ✅ 生成测试图表：`font_test.png`

## 🚀 快速开始

1. **启动工具**：

   ```bash
   python start_tool.py
   ```
2. **加载数据**：

   - 点击"选择CSV文件"
   - 选择：`../keypoint_viewer/data/face_keypoints_temporal_stability_4.csv`
3. **设置分析**：

   - X轴：frame_id
   - Y轴：velocity
   - 选择关键点：可以全选或选择特定范围
4. **开始分析**：

   - 观察关键点随时间的变化
   - 使用工具栏进行缩放、平移
   - 导出分析结果

## 📊 预期效果

修复后的工具应该能够：

- ✅ 正确显示中文界面文字
- ✅ 图表标题和轴标签正常显示
- ✅ 所有功能正常运行
- ✅ 支持数据导出和保存

## 🛠️ 故障排除

如果字体显示仍有问题：

1. **检查系统字体**：

   ```bash
   fc-list :lang=zh
   ```
2. **安装更多中文字体**：

   ```bash
   sudo apt-get install fonts-noto-cjk
   ```
3. **使用英文环境**：

   ```bash
   LANG=en_US.UTF-8 python start_tool.py
   ```

## 📈 功能不受影响

无论字体显示如何，工具的核心功能都完全正常：

- ✅ 数据加载和处理
- ✅ 交互式图表绘制
- ✅ 关键点选择和过滤
- ✅ 实时数据更新
- ✅ 图表导出功能
- ✅ 所有数据分析功能

您可以放心使用工具进行数据分析！
