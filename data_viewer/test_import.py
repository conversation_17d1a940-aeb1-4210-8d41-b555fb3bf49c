#!/usr/bin/env python3
"""测试所有依赖导入"""

print("测试导入...")

try:
    import tkinter as tk
    print("✓ tkinter 导入成功")
except ImportError as e:
    print(f"✗ tkinter 导入失败: {e}")

try:
    from tkinter import ttk, filedialog, messagebox
    print("✓ tkinter 子模块导入成功")
except ImportError as e:
    print(f"✗ tkinter 子模块导入失败: {e}")

try:
    import pandas as pd
    print("✓ pandas 导入成功")
except ImportError as e:
    print(f"✗ pandas 导入失败: {e}")

try:
    import numpy as np
    print("✓ numpy 导入成功")
except ImportError as e:
    print(f"✗ numpy 导入失败: {e}")

try:
    import matplotlib.pyplot as plt
    print("✓ matplotlib.pyplot 导入成功")
except ImportError as e:
    print(f"✗ matplotlib.pyplot 导入失败: {e}")

try:
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
    print("✓ matplotlib backend 导入成功")
except ImportError as e:
    print(f"✗ matplotlib backend 导入失败: {e}")

try:
    from matplotlib.figure import Figure
    print("✓ matplotlib Figure 导入成功")
except ImportError as e:
    print(f"✗ matplotlib Figure 导入失败: {e}")

try:
    import matplotlib.colors as mcolors
    print("✓ matplotlib colors 导入成功")
except ImportError as e:
    print(f"✗ matplotlib colors 导入失败: {e}")

print("\n所有导入测试完成!")
print("如果有导入失败，请运行: pip install pandas numpy matplotlib") 