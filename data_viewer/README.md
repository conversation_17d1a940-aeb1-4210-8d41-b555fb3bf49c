# 关键点时序数据可视化工具

一个功能强大的交互式可视化工具，专门用于分析和显示关键点时序数据。

## 功能特性

### 🎯 核心功能
- **交互式轴选择**：可自由选择X轴和Y轴变量进行分析
- **关键点过滤**：支持选择显示任意关键点组合（0-37）
- **实时数据更新**：监控CSV文件变化，自动刷新图表
- **多种显示模式**：线图、散点图、线+点组合模式
- **高性能**：支持处理20000+行大数据量

### 📊 可视化功能
- **时序分析**：专门针对frame_id和velocity关系分析
- **多关键点对比**：同时显示多个关键点的变化趋势
- **智能颜色管理**：自动为38个关键点分配不同颜色
- **交互式图例**：点击图例隐藏/显示对应关键点

### 💾 数据处理
- **CSV格式支持**：直接加载CSV时序数据
- **图表导出**：支持PNG、PDF、SVG格式导出
- **数据验证**：自动验证数据格式和完整性

## 数据格式要求

CSV文件必须包含以下字段：
```
frame_id,keypoint_id,x,y,confidence,std_x,std_y,velocity,acceleration,instability_score,is_unstable
```

- `frame_id`: 帧ID（时间序列）
- `keypoint_id`: 关键点ID（0-37）
- `x, y`: 坐标位置
- `confidence`: 置信度
- `std_x, std_y`: 坐标标准差
- `velocity`: 速度
- `acceleration`: 加速度
- `instability_score`: 不稳定性分数
- `is_unstable`: 是否不稳定

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 启动工具
```bash
python run.py
```

### 界面操作指南

#### 1. 数据加载
- 点击"选择CSV文件"加载数据文件
- 勾选"实时监控文件变化"开启自动更新

#### 2. 轴选择
- **X轴**：选择要显示在X轴的变量（如frame_id用于时序分析）
- **Y轴**：选择要显示在Y轴的变量（如velocity用于速度分析）

#### 3. 显示模式
- **线图**：显示连续的线条，适合时序分析
- **散点图**：显示离散点，适合相关性分析
- **线+点**：结合线条和点，提供最详细的信息

#### 4. 关键点选择
- **全选**：一键选择/取消所有关键点
- **分组选择**：按区域选择关键点（0-9, 10-19, 20-29, 30-37）
- **单点选择**：在列表中精确选择特定关键点

#### 5. 图表导出
- 点击"导出图表"保存当前显示的图表
- 支持PNG、PDF、SVG格式

## 使用场景

### 1. 时序稳定性分析
```
X轴: frame_id
Y轴: velocity 或 instability_score
用途: 观察关键点随时间的稳定性变化
```

### 2. 坐标变化趋势
```
X轴: frame_id
Y轴: x 或 y
用途: 分析关键点位置的时序变化
```

### 3. 置信度与稳定性关系
```
X轴: confidence
Y轴: instability_score
用途: 探索置信度与稳定性的相关性
```

### 4. 多关键点对比分析
- 选择多个关键点，观察它们的相对变化
- 识别异常或特殊行为的关键点
- 分析不同区域关键点的差异

## 性能优化

- **大数据处理**：针对20000+行数据进行了性能优化
- **实时更新**：高效的文件监控机制，不影响界面响应
- **图表缓存**：智能缓存机制，提高绘图效率
- **内存管理**：优化内存使用，支持长时间运行

## 技术特点

- **跨平台**：基于Python + Tkinter，支持Windows、Linux、macOS
- **响应式界面**：现代化的GUI设计，操作直观
- **专业图表**：基于matplotlib，提供出版级别的图表质量
- **模块化设计**：代码结构清晰，易于扩展和维护

## 快速开始示例

1. 启动工具：`python run.py`
2. 加载您的CSV数据文件
3. 设置X轴为"frame_id"，Y轴为"velocity"
4. 选择要分析的关键点（比如0-10）
5. 选择"线图"模式观察时序变化
6. 导出图表用于报告或分析

## 故障排除

### 常见问题
1. **导入错误**：确保安装了所有依赖包
2. **数据格式错误**：检查CSV文件格式是否符合要求
3. **文件权限**：确保有读取CSV文件的权限
4. **内存不足**：对于超大数据集，考虑分批处理

### 联系支持
如有问题，请检查：
- Python版本（推荐3.7+）
- 依赖包版本
- 数据文件格式 