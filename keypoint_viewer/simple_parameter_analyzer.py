#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关键点参数分析器 - 简化版本
使用matplotlib创建交互式可视化界面，分析关键点参数随时间的变化
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.widgets import CheckButtons, Slider, Button
import matplotlib.patches as patches
import sys
import os

class SimpleKeypointParameterAnalyzer:
    def __init__(self, csv_file_path):
        """初始化分析器"""
        self.csv_file = csv_file_path
        self.df = None
        self.fig = None
        self.axes = []
        self.widgets = {}
        self.current_keypoints = [0, 1, 2, 3, 4]  # 默认选择的关键点
        self.current_parameters = ['confidence', 'instability_score']  # 默认参数
        self.frame_range = [0, 100]  # 默认帧范围
        
        self.load_data()
        self.setup_gui()
    
    def load_data(self):
        """加载CSV数据"""
        try:
            self.df = pd.read_csv(self.csv_file)
            print(f"数据加载成功: {len(self.df)} 行, {len(self.df.columns)} 列")
            print(f"帧范围: {self.df['frame_id'].min()} - {self.df['frame_id'].max()}")
            print(f"关键点数量: {len(self.df['keypoint_id'].unique())}")
            
            # 设置默认帧范围
            max_frame = self.df['frame_id'].max()
            self.frame_range = [0, min(100, max_frame)]
            
        except Exception as e:
            print(f"数据加载失败: {e}")
            raise
    
    def setup_gui(self):
        """设置GUI界面"""
        # 创建主窗口
        self.fig = plt.figure(figsize=(16, 10))
        self.fig.suptitle('关键点参数时间序列分析器', fontsize=16, fontweight='bold')
        
        # 设置中文字体支持 - 使用系统检测到的字体
        import matplotlib.font_manager as fm
        available_fonts = []
        
        # 优先使用Noto Sans CJK系列字体
        font_candidates = [
            'Noto Sans CJK SC',
            'Noto Sans CJK TC', 
            'Noto Sans CJK JP',
            'Noto Sans CJK KR',
            'AR PL UMing CN',
            'AR PL UKai CN',
            'SimHei',
            'Microsoft YaHei',
            'WenQuanYi Micro Hei'
        ]
        
        # 检查哪些字体实际可用
        system_fonts = [f.name for f in fm.fontManager.ttflist]
        for font in font_candidates:
            if font in system_fonts:
                available_fonts.append(font)
        
        # 设置字体
        if available_fonts:
            plt.rcParams['font.sans-serif'] = available_fonts + ['DejaVu Sans', 'sans-serif']
        else:
            # 回退方案
            plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'sans-serif']
        
        plt.rcParams['axes.unicode_minus'] = False
        print(f"使用字体: {available_fonts[:3] if available_fonts else ['DejaVu Sans']}")
        
        # 创建网格布局
        gs = self.fig.add_gridspec(4, 4, hspace=0.4, wspace=0.3)
        
        # 主图表区域 (占据大部分空间)
        self.main_ax = self.fig.add_subplot(gs[0:3, 0:3])
        
        # 控制面板区域
        self.control_ax = self.fig.add_subplot(gs[0:2, 3])
        self.stats_ax = self.fig.add_subplot(gs[2:3, 3])
        
        # 滑块区域
        self.slider_ax = self.fig.add_subplot(gs[3, 0:3])
        
        # 设置控制面板
        self.setup_controls()
        
        # 设置滑块
        self.setup_sliders()
        
        # 初始化图表
        self.update_plot()
        
        # 显示统计信息
        self.update_stats()
    
    def setup_controls(self):
        """设置控制面板"""
        self.control_ax.axis('off')
        
        # 关键点选择区域
        keypoint_ids = sorted(self.df['keypoint_id'].unique())
        
        # 创建关键点选择按钮组
        y_positions = np.linspace(0.9, 0.1, len(keypoint_ids))
        self.keypoint_buttons = {}
        
        for i, kp_id in enumerate(keypoint_ids):
            if i >= 15:  # 限制显示的关键点数量
                break
            y_pos = y_positions[i]
            
            # 创建复选框样式的文本
            color = 'green' if kp_id in self.current_keypoints else 'gray'
            text = f"□ 关键点 {kp_id}" if kp_id not in self.current_keypoints else f"☑ 关键点 {kp_id}"
            
            button_text = self.control_ax.text(0.05, y_pos, text, 
                                             transform=self.control_ax.transAxes,
                                             fontsize=10, color=color,
                                             picker=True, gid=f'kp_{kp_id}')
            self.keypoint_buttons[kp_id] = button_text
        
        # 添加标题
        self.control_ax.text(0.05, 0.95, '选择关键点:', 
                           transform=self.control_ax.transAxes,
                           fontsize=12, fontweight='bold')
    
    def setup_sliders(self):
        """设置滑块"""
        self.slider_ax.axis('off')
        
        # 帧范围滑块
        max_frame = self.df['frame_id'].max()
        
        # 创建滑块区域
        slider_height = 0.15
        slider_y = 0.4
        
        # 开始帧滑块
        start_slider_ax = plt.axes([0.15, slider_y + 0.3, 0.5, slider_height])
        self.start_slider = Slider(start_slider_ax, '开始帧', 0, max_frame, 
                                 valinit=self.frame_range[0], valfmt='%d')
        
        # 结束帧滑块
        end_slider_ax = plt.axes([0.15, slider_y, 0.5, slider_height])
        self.end_slider = Slider(end_slider_ax, '结束帧', 0, max_frame, 
                               valinit=self.frame_range[1], valfmt='%d')
        
        # 快速选择按钮
        btn_ax1 = plt.axes([0.7, slider_y + 0.3, 0.1, slider_height])
        self.btn_first_100 = Button(btn_ax1, '前100帧')
        
        btn_ax2 = plt.axes([0.7, slider_y, 0.1, slider_height])
        self.btn_all_frames = Button(btn_ax2, '全部帧')
        
        # 绑定事件
        self.start_slider.on_changed(self.on_slider_change)
        self.end_slider.on_changed(self.on_slider_change)
        self.btn_first_100.on_clicked(self.on_first_100_click)
        self.btn_all_frames.on_clicked(self.on_all_frames_click)
        
        # 绑定关键点选择事件
        self.fig.canvas.mpl_connect('pick_event', self.on_keypoint_click)
    
    def on_keypoint_click(self, event):
        """处理关键点选择点击事件"""
        if hasattr(event.artist, 'get_gid') and event.artist.get_gid():
            gid = event.artist.get_gid()
            if gid.startswith('kp_'):
                kp_id = int(gid.split('_')[1])
                
                # 切换关键点选择状态
                if kp_id in self.current_keypoints:
                    self.current_keypoints.remove(kp_id)
                    event.artist.set_text(f"□ 关键点 {kp_id}")
                    event.artist.set_color('gray')
                else:
                    self.current_keypoints.append(kp_id)
                    event.artist.set_text(f"☑ 关键点 {kp_id}")
                    event.artist.set_color('green')
                
                # 更新图表
                self.update_plot()
                self.update_stats()
                self.fig.canvas.draw()
    
    def on_slider_change(self, val):
        """处理滑块变化事件"""
        start_val = int(self.start_slider.val)
        end_val = int(self.end_slider.val)
        
        # 确保开始帧小于结束帧
        if start_val >= end_val:
            if val == start_val:
                end_val = min(start_val + 1, self.df['frame_id'].max())
                self.end_slider.set_val(end_val)
            else:
                start_val = max(end_val - 1, 0)
                self.start_slider.set_val(start_val)
        
        self.frame_range = [start_val, end_val]
        self.update_plot()
        self.update_stats()
    
    def on_first_100_click(self, event):
        """前100帧按钮点击事件"""
        max_frame = self.df['frame_id'].max()
        self.start_slider.set_val(0)
        self.end_slider.set_val(min(99, max_frame))
        self.frame_range = [0, min(99, max_frame)]
        self.update_plot()
        self.update_stats()
    
    def on_all_frames_click(self, event):
        """全部帧按钮点击事件"""
        max_frame = self.df['frame_id'].max()
        self.start_slider.set_val(0)
        self.end_slider.set_val(max_frame)
        self.frame_range = [0, max_frame]
        self.update_plot()
        self.update_stats()
    
    def update_plot(self):
        """更新主图表"""
        self.main_ax.clear()
        
        if not self.current_keypoints:
            self.main_ax.text(0.5, 0.5, '请选择关键点', 
                            transform=self.main_ax.transAxes, 
                            ha='center', va='center', fontsize=14)
            return
        
        # 过滤数据
        filtered_df = self.df[
            (self.df['keypoint_id'].isin(self.current_keypoints)) &
            (self.df['frame_id'] >= self.frame_range[0]) &
            (self.df['frame_id'] <= self.frame_range[1])
        ]
        
        if len(filtered_df) == 0:
            self.main_ax.text(0.5, 0.5, '没有数据可显示', 
                            transform=self.main_ax.transAxes, 
                            ha='center', va='center', fontsize=14)
            return
        
        # 颜色映射
        colors = plt.cm.Set1(np.linspace(0, 1, len(self.current_keypoints)))
        
        # 创建子图 - 每个参数一个子图
        n_params = len(self.current_parameters)
        if n_params == 1:
            axes = [self.main_ax]
        else:
            # 清除主轴并创建子图
            self.main_ax.remove()
            gs_main = self.fig.add_gridspec(4, 4, hspace=0.4, wspace=0.3)[0:3, 0:3]
            gs_sub = gs_main.subgridspec(n_params, 1, hspace=0.3)
            axes = [self.fig.add_subplot(gs_sub[i]) for i in range(n_params)]
            self.main_ax = axes[0]  # 保持引用
        
        # 为每个参数绘制图表
        for i, param in enumerate(self.current_parameters):
            ax = axes[i] if len(axes) > 1 else axes[0]
            
            for j, kp_id in enumerate(self.current_keypoints):
                kp_data = filtered_df[filtered_df['keypoint_id'] == kp_id].sort_values('frame_id')
                
                if len(kp_data) > 0:
                    # 主线条
                    ax.plot(kp_data['frame_id'], kp_data[param], 
                           color=colors[j], linewidth=2, marker='o', markersize=3,
                           label=f'关键点 {kp_id}', alpha=0.8)
                    
                    # 标记异常点
                    unstable_points = kp_data[kp_data['is_unstable'] == 1]
                    if len(unstable_points) > 0:
                        ax.scatter(unstable_points['frame_id'], unstable_points[param],
                                 color='red', marker='x', s=50, linewidths=2,
                                 alpha=0.8, zorder=5)
            
            ax.set_xlabel('帧数')
            ax.set_ylabel(f'{param}')
            ax.set_title(f'{param} 随时间变化')
            ax.grid(True, alpha=0.3)
            
            if i == 0:  # 只在第一个子图显示图例
                ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        
        self.fig.canvas.draw()
    
    def update_stats(self):
        """更新统计信息"""
        self.stats_ax.clear()
        self.stats_ax.axis('off')
        
        if not self.current_keypoints:
            return
        
        # 过滤数据
        filtered_df = self.df[
            (self.df['keypoint_id'].isin(self.current_keypoints)) &
            (self.df['frame_id'] >= self.frame_range[0]) &
            (self.df['frame_id'] <= self.frame_range[1])
        ]
        
        stats_text = f"统计信息\n帧范围: {self.frame_range[0]}-{self.frame_range[1]}\n\n"
        
        for kp_id in self.current_keypoints:
            kp_data = filtered_df[filtered_df['keypoint_id'] == kp_id]
            if len(kp_data) == 0:
                continue
            
            unstable_ratio = (kp_data['is_unstable'] == 1).mean()
            avg_confidence = kp_data['confidence'].mean()
            avg_instability = kp_data['instability_score'].mean()
            
            stats_text += f"关键点 {kp_id}:\n"
            stats_text += f"  置信度: {avg_confidence:.3f}\n"
            stats_text += f"  不稳定率: {unstable_ratio:.2%}\n"
            stats_text += f"  不稳定性: {avg_instability:.3f}\n\n"
        
        self.stats_ax.text(0.05, 0.95, stats_text, 
                         transform=self.stats_ax.transAxes,
                         fontsize=10, verticalalignment='top',
                         fontfamily='monospace')
        
        self.fig.canvas.draw()
    
    def show(self):
        """显示界面"""
        plt.show()

def main():
    """主函数"""
    # 默认CSV文件路径
    default_csv = "data/face_keypoints_temporal_stability_4.csv"
    
    if len(sys.argv) > 1:
        csv_file = sys.argv[1]
    else:
        csv_file = default_csv
    
    if not os.path.exists(csv_file):
        print(f"错误: 找不到CSV文件 {csv_file}")
        print("用法: python simple_parameter_analyzer.py [csv_file_path]")
        sys.exit(1)
    
    try:
        analyzer = SimpleKeypointParameterAnalyzer(csv_file)
        print("\n=== 关键点参数分析器已启动 ===")
        print("操作说明:")
        print("- 点击左侧关键点名称来选择/取消选择关键点")
        print("- 使用底部滑块调整时间范围")
        print("- 红色X标记表示不稳定的帧")
        print("- 右侧显示统计信息")
        analyzer.show()
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 