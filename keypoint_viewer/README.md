# 关键点可视化工具

这是一个用于可视化人脸关键点数据的工具，在12800*800的画幅比例下显示0~21关键点，并在右侧显示特征排序。

## 功能特点

- **关键点可视化**: 在主画面显示0~21关键点，颜色编码和大小表示置信度
- **交互式控制**: 底部滑动条可以切换不同帧
- **特征排序**: 右侧面板显示当前帧的各种特征统计和排序
- **连接线**: 自动绘制关键点之间的连接线，显示面部结构
- **实时更新**: 切换帧时实时更新所有显示内容

## 数据格式

输入CSV文件应包含以下列：
- `frame_id`: 帧ID
- `keypoint_id`: 关键点ID (需要包含0~21)
- `x`, `y`: 关键点坐标
- `confidence`: 置信度
- `std_x`, `std_y`: 坐标标准差
- `velocity`: 速度
- `acceleration`: 加速度  
- `instability_score`: 不稳定性评分
- `is_unstable`: 是否不稳定

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 方法1: 直接运行demo
```bash
python run_demo.py
```

### 方法2: 使用类库
```python
from keypoint_visualizer import KeypointVisualizer

# 创建可视化器
visualizer = KeypointVisualizer("data/face_keypoints_temporal_stability.csv")

# 显示界面
visualizer.show()
```

## 界面说明

- **主画面 (左侧70%)**: 显示关键点可视化
  - 每个关键点用不同颜色的圆点表示
  - 圆点大小和透明度反映置信度高低
  - 数字标签显示关键点ID
  - 蓝色连接线显示面部结构

- **特征面板 (右侧30%)**: 显示当前帧特征排序
  - 平均置信度、最低/最高置信度
  - 置信度标准差
  - 不稳定性相关指标
  - 运动相关指标

- **控制区域 (底部)**: 帧控制滑动条
  - 拖动滑动条可以切换到不同帧
  - 显示当前帧号

## 控制方式

- **滑动条**: 拖动切换任意帧
- **键盘快捷键**: 
  - `←` / `A`: 上一帧
  - `→` / `D`: 下一帧  
  - `↑` / `W`: 快进10帧
  - `↓` / `S`: 快退10帧
  - `Home`: 跳转到第一帧
  - `End`: 跳转到最后一帧

## 数据统计

基于提供的数据文件：
- 总帧数: 799帧 (0~798)
- 关键点范围: 0~21 (共22个点)
- 坐标范围: X(361~862), Y(183~690)
- 总数据点: 17,578个

## 注意事项

- 确保数据文件路径正确
- 需要GUI环境支持matplotlib显示
- 大数据文件加载可能需要一些时间
- 建议在较大屏幕上使用以获得最佳体验 