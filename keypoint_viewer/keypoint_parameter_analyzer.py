#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关键点参数分析器 - Web可视化应用
用于分析关键点的confidence, std_x, std_y, velocity, acceleration, instability_score等参数随时间的变化
"""

import dash
from dash import dcc, html, Input, Output, State, callback_context
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from datetime import datetime
import base64
import io

class KeypointParameterAnalyzer:
    def __init__(self, csv_file_path):
        """初始化分析器"""
        self.csv_file = csv_file_path
        self.df = None
        self.app = None
        self.load_data()
        self.setup_app()
    
    def load_data(self):
        """加载CSV数据"""
        try:
            self.df = pd.read_csv(self.csv_file)
            print(f"数据加载成功: {len(self.df)} 行, {len(self.df.columns)} 列")
            print(f"帧范围: {self.df['frame_id'].min()} - {self.df['frame_id'].max()}")
            print(f"关键点数量: {len(self.df['keypoint_id'].unique())}")
        except Exception as e:
            print(f"数据加载失败: {e}")
            raise
    
    def get_keypoint_stats(self):
        """获取关键点统计信息"""
        stats = {}
        for kp_id in sorted(self.df['keypoint_id'].unique()):
            kp_data = self.df[self.df['keypoint_id'] == kp_id]
            stats[kp_id] = {
                'avg_confidence': kp_data['confidence'].mean(),
                'avg_instability': kp_data['instability_score'].mean(),
                'unstable_ratio': (kp_data['is_unstable'] == 1).mean()
            }
        return stats
    
    def setup_app(self):
        """设置Dash应用"""
        self.app = dash.Dash(__name__)
        
        # 获取可选的参数列表
        parameter_columns = ['confidence', 'std_x', 'std_y', 'velocity', 'acceleration', 'instability_score']
        keypoint_ids = sorted(self.df['keypoint_id'].unique())
        keypoint_stats = self.get_keypoint_stats()
        
        # 应用布局
        self.app.layout = html.Div([
            html.Div([
                html.H1("关键点参数时间序列分析器", 
                       style={'textAlign': 'center', 'color': '#2c3e50', 'marginBottom': '30px'}),
                
                # 控制面板
                html.Div([
                    # 关键点选择
                    html.Div([
                        html.Label("选择关键点:", style={'fontWeight': 'bold', 'marginBottom': '5px'}),
                        dcc.Dropdown(
                            id='keypoint-dropdown',
                            options=[
                                {
                                    'label': f'关键点 {kp_id} (置信度: {keypoint_stats[kp_id]["avg_confidence"]:.3f})',
                                    'value': kp_id
                                } for kp_id in keypoint_ids
                            ],
                            value=[0, 1, 2, 3, 4],  # 默认选择前5个关键点
                            multi=True,
                            placeholder="选择要分析的关键点...",
                            style={'marginBottom': '15px'}
                        )
                    ], style={'width': '48%', 'display': 'inline-block'}),
                    
                    # 参数选择
                    html.Div([
                        html.Label("选择参数:", style={'fontWeight': 'bold', 'marginBottom': '5px'}),
                        dcc.Dropdown(
                            id='parameter-dropdown',
                            options=[
                                {'label': '置信度 (confidence)', 'value': 'confidence'},
                                {'label': 'X轴标准差 (std_x)', 'value': 'std_x'},
                                {'label': 'Y轴标准差 (std_y)', 'value': 'std_y'},
                                {'label': '速度 (velocity)', 'value': 'velocity'},
                                {'label': '加速度 (acceleration)', 'value': 'acceleration'},
                                {'label': '不稳定性得分 (instability_score)', 'value': 'instability_score'}
                            ],
                            value=['confidence', 'instability_score'],  # 默认选择置信度和不稳定性
                            multi=True,
                            placeholder="选择要显示的参数...",
                            style={'marginBottom': '15px'}
                        )
                    ], style={'width': '48%', 'float': 'right', 'display': 'inline-block'})
                ], style={'marginBottom': '20px'}),
                
                # 时间范围控制
                html.Div([
                    html.Label("时间范围:", style={'fontWeight': 'bold', 'marginBottom': '5px'}),
                    dcc.RangeSlider(
                        id='frame-range-slider',
                        min=self.df['frame_id'].min(),
                        max=self.df['frame_id'].max(),
                        step=1,
                        value=[self.df['frame_id'].min(), min(self.df['frame_id'].max(), 100)],
                        marks={
                            i: str(i) for i in range(
                                self.df['frame_id'].min(), 
                                self.df['frame_id'].max() + 1, 
                                max(1, (self.df['frame_id'].max() - self.df['frame_id'].min()) // 10)
                            )
                        },
                        tooltip={"placement": "bottom", "always_visible": True}
                    )
                ], style={'marginBottom': '30px'}),
                
                # 快速选择按钮
                html.Div([
                    html.Button('前100帧', id='btn-first-100', n_clicks=0, 
                              style={'marginRight': '10px', 'backgroundColor': '#3498db', 'color': 'white', 'border': 'none', 'padding': '8px 16px', 'borderRadius': '4px'}),
                    html.Button('全部帧', id='btn-all-frames', n_clicks=0,
                              style={'backgroundColor': '#27ae60', 'color': 'white', 'border': 'none', 'padding': '8px 16px', 'borderRadius': '4px'})
                ], style={'textAlign': 'center', 'marginBottom': '20px'}),
                
                # 图表区域
                dcc.Graph(id='parameter-graph', style={'height': '800px'}),
                
                # 统计信息面板
                html.Div(id='stats-panel', style={'marginTop': '20px', 'padding': '15px', 'backgroundColor': '#f8f9fa', 'borderRadius': '5px'})
                
            ], style={'padding': '20px', 'maxWidth': '1200px', 'margin': '0 auto'})
        ])
        
        # 设置回调函数
        self.setup_callbacks()
    
    def setup_callbacks(self):
        """设置回调函数"""
        
        @self.app.callback(
            [Output('frame-range-slider', 'value')],
            [Input('btn-first-100', 'n_clicks'),
             Input('btn-all-frames', 'n_clicks')],
            [State('frame-range-slider', 'min'),
             State('frame-range-slider', 'max')]
        )
        def update_frame_range(btn1_clicks, btn2_clicks, min_frame, max_frame):
            ctx = callback_context
            if not ctx.triggered:
                return [[min_frame, min(max_frame, 100)]]
            
            button_id = ctx.triggered[0]['prop_id'].split('.')[0]
            
            if button_id == 'btn-first-100':
                return [[min_frame, min(max_frame, min_frame + 99)]]
            elif button_id == 'btn-all-frames':
                return [[min_frame, max_frame]]
            
            return [[min_frame, min(max_frame, 100)]]
        
        @self.app.callback(
            [Output('parameter-graph', 'figure'),
             Output('stats-panel', 'children')],
            [Input('keypoint-dropdown', 'value'),
             Input('parameter-dropdown', 'value'),
             Input('frame-range-slider', 'value')]
        )
        def update_graph(selected_keypoints, selected_parameters, frame_range):
            if not selected_keypoints or not selected_parameters:
                return {}, "请选择关键点和参数"
            
            # 过滤数据
            filtered_df = self.df[
                (self.df['keypoint_id'].isin(selected_keypoints)) &
                (self.df['frame_id'] >= frame_range[0]) &
                (self.df['frame_id'] <= frame_range[1])
            ]
            
            # 创建子图
            n_params = len(selected_parameters)
            fig = make_subplots(
                rows=n_params, cols=1,
                subplot_titles=[f'{param}' for param in selected_parameters],
                vertical_spacing=0.08
            )
            
            # 颜色映射
            colors = px.colors.qualitative.Set1[:len(selected_keypoints)]
            
            # 为每个参数和关键点绘制线条
            for i, param in enumerate(selected_parameters):
                for j, kp_id in enumerate(selected_keypoints):
                    kp_data = filtered_df[filtered_df['keypoint_id'] == kp_id].sort_values('frame_id')
                    
                    if len(kp_data) > 0:
                        # 主线条
                        fig.add_trace(
                            go.Scatter(
                                x=kp_data['frame_id'],
                                y=kp_data[param],
                                mode='lines+markers',
                                name=f'关键点 {kp_id}',
                                line=dict(color=colors[j % len(colors)], width=2),
                                marker=dict(size=4),
                                showlegend=(i == 0),
                                hovertemplate=f'<b>关键点 {kp_id}</b><br>' +
                                            f'帧: %{{x}}<br>' +
                                            f'{param}: %{{y:.4f}}<br>' +
                                            '<extra></extra>'
                            ),
                            row=i+1, col=1
                        )
                        
                        # 标记异常点
                        unstable_points = kp_data[kp_data['is_unstable'] == 1]
                        if len(unstable_points) > 0:
                            fig.add_trace(
                                go.Scatter(
                                    x=unstable_points['frame_id'],
                                    y=unstable_points[param],
                                    mode='markers',
                                    name=f'异常点 {kp_id}',
                                    marker=dict(
                                        color='red',
                                        size=8,
                                        symbol='x',
                                        line=dict(width=2)
                                    ),
                                    showlegend=False,
                                    hovertemplate=f'<b>异常点 - 关键点 {kp_id}</b><br>' +
                                                f'帧: %{{x}}<br>' +
                                                f'{param}: %{{y:.4f}}<br>' +
                                                '<extra></extra>'
                                ),
                                row=i+1, col=1
                            )
            
            # 更新布局
            fig.update_layout(
                height=200 * n_params + 100,
                title=f"关键点参数时间序列分析 (帧 {frame_range[0]} - {frame_range[1]})",
                template='plotly_white',
                hovermode='x unified'
            )
            
            # 更新X轴标签
            for i in range(n_params):
                fig.update_xaxes(title_text="帧数", row=i+1, col=1)
            
            # 生成统计信息
            stats_content = self.generate_stats_panel(filtered_df, selected_keypoints, selected_parameters, frame_range)
            
            return fig, stats_content
    
    def generate_stats_panel(self, filtered_df, selected_keypoints, selected_parameters, frame_range):
        """生成统计信息面板"""
        if len(filtered_df) == 0:
            return html.Div("没有数据可显示")
        
        stats_cards = []
        
        for kp_id in selected_keypoints:
            kp_data = filtered_df[filtered_df['keypoint_id'] == kp_id]
            if len(kp_data) == 0:
                continue
                
            # 计算统计信息
            unstable_count = (kp_data['is_unstable'] == 1).sum()
            total_count = len(kp_data)
            unstable_ratio = unstable_count / total_count if total_count > 0 else 0
            
            param_stats = []
            for param in selected_parameters:
                mean_val = kp_data[param].mean()
                std_val = kp_data[param].std()
                min_val = kp_data[param].min()
                max_val = kp_data[param].max()
                param_stats.append(
                    html.Div([
                        html.Strong(f"{param}: "),
                        f"均值={mean_val:.4f}, 标准差={std_val:.4f}, 范围=[{min_val:.4f}, {max_val:.4f}]"
                    ])
                )
            
            card = html.Div([
                html.H4(f"关键点 {kp_id}", style={'color': '#2c3e50', 'marginBottom': '10px'}),
                html.Div([
                    html.Strong("不稳定性: "),
                    f"{unstable_count}/{total_count} 帧 ({unstable_ratio:.2%})"
                ], style={'marginBottom': '10px'}),
                html.Div(param_stats)
            ], style={
                'border': '1px solid #ddd',
                'borderRadius': '5px',
                'padding': '15px',
                'margin': '10px',
                'backgroundColor': 'white',
                'width': '300px',
                'display': 'inline-block',
                'verticalAlign': 'top'
            })
            
            stats_cards.append(card)
        
        return html.Div([
            html.H3(f"统计信息 (帧 {frame_range[0]} - {frame_range[1]})", 
                   style={'color': '#2c3e50', 'marginBottom': '15px'}),
            html.Div(stats_cards, style={'textAlign': 'center'})
        ])
    
    def run(self, debug=True, port=8050):
        """启动应用"""
        print(f"启动关键点参数分析器...")
        print(f"访问地址: http://localhost:{port}")
        self.app.run_server(debug=debug, port=port, host='0.0.0.0')

def main():
    """主函数"""
    import sys
    import os
    
    # 默认CSV文件路径
    default_csv = "data/face_keypoints_temporal_stability_4.csv"
    
    if len(sys.argv) > 1:
        csv_file = sys.argv[1]
    else:
        csv_file = default_csv
    
    if not os.path.exists(csv_file):
        print(f"错误: 找不到CSV文件 {csv_file}")
        print("用法: python keypoint_parameter_analyzer.py [csv_file_path]")
        sys.exit(1)
    
    try:
        analyzer = KeypointParameterAnalyzer(csv_file)
        analyzer.run()
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 