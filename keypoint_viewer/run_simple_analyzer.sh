#!/bin/bash
# 关键点参数分析器启动脚本 - 简化版

echo "=== 关键点参数分析器启动脚本 (简化版) ==="

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3"
    exit 1
fi

# 检查CSV文件是否存在
CSV_FILE="data/face_keypoints_temporal_stability_4.csv"
if [ ! -f "$CSV_FILE" ]; then
    echo "错误: 未找到CSV文件 $CSV_FILE"
    echo "请确保CSV文件存在并且路径正确"
    exit 1
fi

# 检查基础依赖
echo "检查依赖..."
python3 -c "import pandas, numpy, matplotlib.pyplot; print('所有依赖已准备就绪')" || {
    echo "错误: 缺少必要的依赖包"
    echo "请安装: pip3 install pandas numpy matplotlib"
    exit 1
}

# 启动应用
echo "启动关键点参数分析器..."
echo ""
echo "操作说明:"
echo "- 点击左侧关键点名称来选择/取消选择关键点"
echo "- 使用底部滑块调整时间范围"
echo "- 红色X标记表示不稳定的帧"
echo "- 右侧显示统计信息"
echo ""

python3 simple_parameter_analyzer.py "$CSV_FILE" 