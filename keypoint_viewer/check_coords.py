#!/usr/bin/env python3
import pandas as pd

def check_coordinates():
    """检查关键点坐标"""
    data = pd.read_csv('data/face_keypoints_temporal_stability.csv', nrows=100)
    filtered = data[(data['keypoint_id'] >= 0) & (data['keypoint_id'] <= 21)]
    
    print('前几个关键点坐标:')
    print(filtered[['frame_id', 'keypoint_id', 'x', 'y']].head(10))
    
    print('\n第一帧的关键点分布:')
    frame0 = filtered[filtered['frame_id'] == 0]
    print(frame0[['keypoint_id', 'x', 'y']].sort_values('keypoint_id'))
    
    print(f'\n坐标范围:')
    print(f'X: {filtered["x"].min():.1f} ~ {filtered["x"].max():.1f}')
    print(f'Y: {filtered["y"].min():.1f} ~ {filtered["y"].max():.1f}')

if __name__ == "__main__":
    check_coordinates() 