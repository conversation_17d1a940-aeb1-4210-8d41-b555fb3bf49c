#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关键点可视化Demo启动脚本
"""

import os
import sys

def main():
    print("=== 关键点可视化Demo ===")
    
    # 检查数据文件是否存在
    data_file = "data/face_keypoints_temporal_stability.csv"
    if not os.path.exists(data_file):
        print(f"错误: 找不到数据文件 {data_file}")
        print("请确保数据文件存在!")
        return
    
    # 导入可视化器
    try:
        from keypoint_visualizer import KeypointVisualizer
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请先安装依赖: pip install -r requirements.txt")
        return
    
    try:
        # 创建并启动可视化器
        visualizer = KeypointVisualizer(data_file)
        visualizer.show()
        
    except Exception as e:
        print(f"运行错误: {e}")
        print("请检查数据文件格式!")

if __name__ == "__main__":
    main() 