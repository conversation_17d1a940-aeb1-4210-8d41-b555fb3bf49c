#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Keypoint Parameter Analyzer - English Version
Interactive visualization tool for analyzing keypoint parameters over time
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.widgets import CheckButtons, Slider, Button
import matplotlib.patches as patches
import sys
import os

class SimpleKeypointParameterAnalyzer:
    def __init__(self, csv_file_path):
        """Initialize analyzer"""
        self.csv_file = csv_file_path
        self.df = None
        self.fig = None
        self.axes = []
        self.widgets = {}
        self.current_keypoints = [0, 1, 2, 3, 4]  # Default selected keypoints
        self.current_parameters = ['confidence', 'instability_score']  # Default parameters
        self.frame_range = [0, 100]  # Default frame range
        
        self.load_data()
        self.setup_gui()
    
    def load_data(self):
        """Load CSV data"""
        try:
            self.df = pd.read_csv(self.csv_file)
            print(f"Data loaded successfully: {len(self.df)} rows, {len(self.df.columns)} columns")
            print(f"Frame range: {self.df['frame_id'].min()} - {self.df['frame_id'].max()}")
            print(f"Number of keypoints: {len(self.df['keypoint_id'].unique())}")
            
            # Set default frame range
            max_frame = self.df['frame_id'].max()
            self.frame_range = [0, min(100, max_frame)]
            
        except Exception as e:
            print(f"Data loading failed: {e}")
            raise
    
    def setup_gui(self):
        """Setup GUI interface"""
        # Create main window
        self.fig = plt.figure(figsize=(16, 10))
        self.fig.suptitle('Keypoint Parameter Time Series Analyzer', fontsize=16, fontweight='bold')
        
        # Create grid layout
        gs = self.fig.add_gridspec(4, 4, hspace=0.4, wspace=0.3)
        
        # Main chart area (占据大部分空间)
        self.main_ax = self.fig.add_subplot(gs[0:3, 0:3])
        
        # Control panel area
        self.control_ax = self.fig.add_subplot(gs[0:2, 3])
        self.stats_ax = self.fig.add_subplot(gs[2:3, 3])
        
        # Slider area
        self.slider_ax = self.fig.add_subplot(gs[3, 0:3])
        
        # Setup control panel
        self.setup_controls()
        
        # Setup sliders
        self.setup_sliders()
        
        # Initialize chart
        self.update_plot()
        
        # Show statistics
        self.update_stats()
    
    def setup_controls(self):
        """Setup control panel"""
        self.control_ax.axis('off')
        
        # Keypoint selection area
        keypoint_ids = sorted(self.df['keypoint_id'].unique())
        
        # Create keypoint selection button group
        y_positions = np.linspace(0.9, 0.1, len(keypoint_ids))
        self.keypoint_buttons = {}
        
        for i, kp_id in enumerate(keypoint_ids):
            if i >= 15:  # Limit displayed keypoints
                break
            y_pos = y_positions[i]
            
            # Create checkbox style text
            color = 'green' if kp_id in self.current_keypoints else 'gray'
            text = f"□ Keypoint {kp_id}" if kp_id not in self.current_keypoints else f"☑ Keypoint {kp_id}"
            
            button_text = self.control_ax.text(0.05, y_pos, text, 
                                             transform=self.control_ax.transAxes,
                                             fontsize=10, color=color,
                                             picker=True, gid=f'kp_{kp_id}')
            self.keypoint_buttons[kp_id] = button_text
        
        # Add title
        self.control_ax.text(0.05, 0.95, 'Select Keypoints:', 
                           transform=self.control_ax.transAxes,
                           fontsize=12, fontweight='bold')
    
    def setup_sliders(self):
        """Setup sliders"""
        self.slider_ax.axis('off')
        
        # Frame range sliders
        max_frame = self.df['frame_id'].max()
        
        # Create slider area
        slider_height = 0.15
        slider_y = 0.4
        
        # Start frame slider
        start_slider_ax = plt.axes([0.15, slider_y + 0.3, 0.5, slider_height])
        self.start_slider = Slider(start_slider_ax, 'Start Frame', 0, max_frame, 
                                 valinit=self.frame_range[0], valfmt='%d')
        
        # End frame slider
        end_slider_ax = plt.axes([0.15, slider_y, 0.5, slider_height])
        self.end_slider = Slider(end_slider_ax, 'End Frame', 0, max_frame, 
                               valinit=self.frame_range[1], valfmt='%d')
        
        # Quick selection buttons
        btn_ax1 = plt.axes([0.7, slider_y + 0.3, 0.1, slider_height])
        self.btn_first_100 = Button(btn_ax1, 'First 100')
        
        btn_ax2 = plt.axes([0.7, slider_y, 0.1, slider_height])
        self.btn_all_frames = Button(btn_ax2, 'All Frames')
        
        # Bind events
        self.start_slider.on_changed(self.on_slider_change)
        self.end_slider.on_changed(self.on_slider_change)
        self.btn_first_100.on_clicked(self.on_first_100_click)
        self.btn_all_frames.on_clicked(self.on_all_frames_click)
        
        # Bind keypoint selection events
        self.fig.canvas.mpl_connect('pick_event', self.on_keypoint_click)
    
    def on_keypoint_click(self, event):
        """Handle keypoint selection click events"""
        if hasattr(event.artist, 'get_gid') and event.artist.get_gid():
            gid = event.artist.get_gid()
            if gid.startswith('kp_'):
                kp_id = int(gid.split('_')[1])
                
                # Toggle keypoint selection state
                if kp_id in self.current_keypoints:
                    self.current_keypoints.remove(kp_id)
                    event.artist.set_text(f"□ Keypoint {kp_id}")
                    event.artist.set_color('gray')
                else:
                    self.current_keypoints.append(kp_id)
                    event.artist.set_text(f"☑ Keypoint {kp_id}")
                    event.artist.set_color('green')
                
                # Update chart
                self.update_plot()
                self.update_stats()
                self.fig.canvas.draw()
    
    def on_slider_change(self, val):
        """Handle slider change events"""
        start_val = int(self.start_slider.val)
        end_val = int(self.end_slider.val)
        
        # Ensure start frame is less than end frame
        if start_val >= end_val:
            if val == start_val:
                end_val = min(start_val + 1, self.df['frame_id'].max())
                self.end_slider.set_val(end_val)
            else:
                start_val = max(end_val - 1, 0)
                self.start_slider.set_val(start_val)
        
        self.frame_range = [start_val, end_val]
        self.update_plot()
        self.update_stats()
    
    def on_first_100_click(self, event):
        """First 100 frames button click event"""
        max_frame = self.df['frame_id'].max()
        self.start_slider.set_val(0)
        self.end_slider.set_val(min(99, max_frame))
        self.frame_range = [0, min(99, max_frame)]
        self.update_plot()
        self.update_stats()
    
    def on_all_frames_click(self, event):
        """All frames button click event"""
        max_frame = self.df['frame_id'].max()
        self.start_slider.set_val(0)
        self.end_slider.set_val(max_frame)
        self.frame_range = [0, max_frame]
        self.update_plot()
        self.update_stats()
    
    def update_plot(self):
        """Update main chart"""
        self.main_ax.clear()
        
        if not self.current_keypoints:
            self.main_ax.text(0.5, 0.5, 'Please select keypoints', 
                            transform=self.main_ax.transAxes, 
                            ha='center', va='center', fontsize=14)
            return
        
        # Filter data
        filtered_df = self.df[
            (self.df['keypoint_id'].isin(self.current_keypoints)) &
            (self.df['frame_id'] >= self.frame_range[0]) &
            (self.df['frame_id'] <= self.frame_range[1])
        ]
        
        if len(filtered_df) == 0:
            self.main_ax.text(0.5, 0.5, 'No data to display', 
                            transform=self.main_ax.transAxes, 
                            ha='center', va='center', fontsize=14)
            return
        
        # Color mapping
        colors = plt.cm.Set1(np.linspace(0, 1, len(self.current_keypoints)))
        
        # Create subplots - one subplot per parameter
        n_params = len(self.current_parameters)
        if n_params == 1:
            axes = [self.main_ax]
        else:
            # Clear main axis and create subplots
            self.main_ax.remove()
            gs_main = self.fig.add_gridspec(4, 4, hspace=0.4, wspace=0.3)[0:3, 0:3]
            gs_sub = gs_main.subgridspec(n_params, 1, hspace=0.3)
            axes = [self.fig.add_subplot(gs_sub[i]) for i in range(n_params)]
            self.main_ax = axes[0]  # Keep reference
        
        # Draw chart for each parameter
        for i, param in enumerate(self.current_parameters):
            ax = axes[i] if len(axes) > 1 else axes[0]
            
            for j, kp_id in enumerate(self.current_keypoints):
                kp_data = filtered_df[filtered_df['keypoint_id'] == kp_id].sort_values('frame_id')
                
                if len(kp_data) > 0:
                    # Main line
                    ax.plot(kp_data['frame_id'], kp_data[param], 
                           color=colors[j], linewidth=2, marker='o', markersize=3,
                           label=f'Keypoint {kp_id}', alpha=0.8)
                    
                    # Mark unstable points
                    unstable_points = kp_data[kp_data['is_unstable'] == 1]
                    if len(unstable_points) > 0:
                        ax.scatter(unstable_points['frame_id'], unstable_points[param],
                                 color='red', marker='x', s=50, linewidths=2,
                                 alpha=0.8, zorder=5)
            
            ax.set_xlabel('Frame Number')
            ax.set_ylabel(f'{param}')
            ax.set_title(f'{param.title()} Over Time')
            ax.grid(True, alpha=0.3)
            
            if i == 0:  # Only show legend in first subplot
                ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        
        self.fig.canvas.draw()
    
    def update_stats(self):
        """Update statistics information"""
        self.stats_ax.clear()
        self.stats_ax.axis('off')
        
        if not self.current_keypoints:
            return
        
        # Filter data
        filtered_df = self.df[
            (self.df['keypoint_id'].isin(self.current_keypoints)) &
            (self.df['frame_id'] >= self.frame_range[0]) &
            (self.df['frame_id'] <= self.frame_range[1])
        ]
        
        stats_text = f"Statistics\nFrame Range: {self.frame_range[0]}-{self.frame_range[1]}\n\n"
        
        for kp_id in self.current_keypoints:
            kp_data = filtered_df[filtered_df['keypoint_id'] == kp_id]
            if len(kp_data) == 0:
                continue
            
            unstable_ratio = (kp_data['is_unstable'] == 1).mean()
            avg_confidence = kp_data['confidence'].mean()
            avg_instability = kp_data['instability_score'].mean()
            
            stats_text += f"Keypoint {kp_id}:\n"
            stats_text += f"  Confidence: {avg_confidence:.3f}\n"
            stats_text += f"  Unstable Rate: {unstable_ratio:.2%}\n"
            stats_text += f"  Instability: {avg_instability:.3f}\n\n"
        
        self.stats_ax.text(0.05, 0.95, stats_text, 
                         transform=self.stats_ax.transAxes,
                         fontsize=10, verticalalignment='top',
                         fontfamily='monospace')
        
        self.fig.canvas.draw()
    
    def show(self):
        """Show interface"""
        plt.show()

def main():
    """Main function"""
    # Default CSV file path
    default_csv = "data/face_keypoints_temporal_stability_4.csv"
    
    if len(sys.argv) > 1:
        csv_file = sys.argv[1]
    else:
        csv_file = default_csv
    
    if not os.path.exists(csv_file):
        print(f"Error: Cannot find CSV file {csv_file}")
        print("Usage: python simple_parameter_analyzer_en.py [csv_file_path]")
        sys.exit(1)
    
    try:
        analyzer = SimpleKeypointParameterAnalyzer(csv_file)
        print("\n=== Keypoint Parameter Analyzer Started ===")
        print("Instructions:")
        print("- Click keypoint names on the right to select/deselect keypoints")
        print("- Use bottom sliders to adjust time range")
        print("- Red X marks indicate unstable frames")
        print("- Statistics are shown on the right panel")
        analyzer.show()
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 