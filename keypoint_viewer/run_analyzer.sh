#!/bin/bash
# 关键点参数分析器启动脚本

echo "=== 关键点参数分析器启动脚本 ==="

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3"
    exit 1
fi

# 安装依赖
echo "安装依赖包..."
pip3 install -r requirements.txt

# 检查CSV文件是否存在
CSV_FILE="data/face_keypoints_temporal_stability_4.csv"
if [ ! -f "$CSV_FILE" ]; then
    echo "错误: 未找到CSV文件 $CSV_FILE"
    echo "请确保CSV文件存在并且路径正确"
    exit 1
fi

# 启动应用
echo "启动关键点参数分析器..."
echo "请在浏览器中访问: http://localhost:8050"
python3 keypoint_parameter_analyzer.py "$CSV_FILE" 