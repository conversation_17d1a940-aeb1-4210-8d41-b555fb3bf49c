#!/usr/bin/env python3
import pandas as pd
import numpy as np

def analyze_keypoint_range():
    """分析关键点坐标范围"""
    print('正在分析坐标范围...')
    
    chunk_size = 10000
    chunks = []

    for chunk in pd.read_csv('data/face_keypoints_temporal_stability.csv', chunksize=chunk_size):
        # 只保留0~21关键点
        chunk_filtered = chunk[(chunk['keypoint_id'] >= 0) & (chunk['keypoint_id'] <= 21)]
        chunks.append(chunk_filtered)

    data = pd.concat(chunks, ignore_index=True)

    print(f'X坐标范围: {data["x"].min():.1f} ~ {data["x"].max():.1f}')
    print(f'Y坐标范围: {data["y"].min():.1f} ~ {data["y"].max():.1f}')
    print(f'数据行数: {len(data)}')
    print(f'关键点ID范围: {data["keypoint_id"].min()} ~ {data["keypoint_id"].max()}')
    print(f'帧数范围: {data["frame_id"].min()} ~ {data["frame_id"].max()}')

if __name__ == "__main__":
    analyze_keypoint_range() 