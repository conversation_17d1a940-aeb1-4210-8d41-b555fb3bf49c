# 关键点参数时间序列分析器

一个用于分析关键点参数（confidence, std_x, std_y, velocity, acceleration, instability_score）随时间变化的可视化工具。

## 🎯 功能特性

### 📊 **多参数分析**
- **置信度 (confidence)**: 关键点检测的置信程度
- **标准差 (std_x/std_y)**: 位置稳定性指标
- **速度 (velocity)**: 关键点移动速度
- **加速度 (acceleration)**: 运动加速度变化
- **不稳定性得分 (instability_score)**: 综合不稳定性评估

### 🎮 **交互式界面**
- **关键点选择**: 点击选择/取消选择关键点进行对比分析
- **时间范围控制**: 滑块调整分析的帧数范围
- **异常点标记**: 红色X标记显示不稳定的帧
- **实时统计**: 右侧面板显示统计信息
- **快速操作**: 一键选择前100帧或全部帧

## 🚀 快速开始

### 方法1: 使用启动脚本 (推荐)

```bash
# 进入项目目录
cd keypoint_viewer

# 运行简化版分析器 (基于matplotlib)
./run_simple_analyzer.sh
```

### 方法2: 手动运行

```bash
# 直接运行简化版
python3 simple_parameter_analyzer.py data/face_keypoints_temporal_stability_4.csv

# 如果要运行Web版本 (需要先安装dash)
pip3 install dash plotly
python3 keypoint_parameter_analyzer.py data/face_keypoints_temporal_stability_4.csv
```

## 📋 版本说明

### 简化版 (simple_parameter_analyzer.py)
- **基于**: matplotlib + tkinter widget
- **优点**: 无需额外依赖，启动快速
- **界面**: 桌面应用程序
- **推荐**: 适合快速分析和本地使用

### Web版 (keypoint_parameter_analyzer.py)  
- **基于**: Plotly Dash
- **优点**: 界面更美观，功能更丰富
- **界面**: Web应用 (http://localhost:8050)
- **推荐**: 适合展示和远程访问

## 📊 数据要求

CSV文件需要包含以下列：
```
frame_id,keypoint_id,x,y,confidence,std_x,std_y,velocity,acceleration,instability_score,is_unstable
```

## 🎮 使用指南

### 简化版操作
1. **选择关键点**: 点击右侧面板的关键点名称来选择/取消选择
2. **调整时间范围**: 使用底部的开始帧和结束帧滑块
3. **快速选择**: 点击"前100帧"或"全部帧"按钮
4. **查看统计**: 右上角显示选中关键点的统计信息
5. **异常识别**: 红色X标记表示不稳定的帧

### Web版操作
1. **选择关键点**: 使用左上角的下拉菜单（可多选）
2. **选择参数**: 使用右上角的下拉菜单（可多选）
3. **调整范围**: 使用时间范围滑块
4. **交互查看**: 鼠标悬停查看具体数值，点击图例隐藏/显示线条

## 📈 界面布局

### 简化版布局
```
┌─────────────────────────────┬─────────────┐
│                             │ 关键点选择   │
│        主图表区域            │             │
│                             ├─────────────┤
│                             │ 统计信息     │
└─────────────────────────────┴─────────────┘
├─────────── 时间范围滑块 ──────────────────┤
```

### Web版布局
```
┌─────────────┬─────────────┐
│ 关键点选择   │ 参数选择     │
├─────────────────────────────┤
│       时间范围滑块           │
├─────────────────────────────┤
│                             │
│        主图表区域            │
│                             │
├─────────────────────────────┤
│        统计信息面板          │
└─────────────────────────────┘
```

## 🔧 系统要求

### 基础依赖 (简化版)
- Python 3.6+
- pandas
- numpy  
- matplotlib

### 扩展依赖 (Web版)
- dash>=2.14.0
- plotly>=5.15.0

## 📊 分析功能

### 时间序列分析
- 查看参数随时间的变化趋势
- 识别异常波动和稳定期
- 对比不同关键点的稳定性

### 统计分析
- 平均值、标准差、最小值、最大值
- 不稳定帧的数量和比例
- 关键点稳定性排名

### 异常检测
- 自动标记is_unstable=1的帧
- 红色X标记显示异常时刻
- 统计异常频率

## 🎯 应用场景

1. **质量评估**: 评估关键点检测算法的稳定性
2. **参数优化**: 找出需要调优的时间段和关键点
3. **异常分析**: 识别检测失败或不稳定的帧
4. **算法对比**: 比较不同算法的检测效果
5. **数据清洗**: 识别需要重新标注的数据

## 🔍 故障排除

### 常见问题

1. **脚本启动失败**
   ```bash
   # 检查Python版本
   python3 --version
   
   # 检查依赖
   python3 -c "import pandas, numpy, matplotlib; print('OK')"
   ```

2. **CSV文件不存在**
   ```bash
   # 检查文件路径
   ls -la data/face_keypoints_temporal_stability_4.csv
   
   # 使用绝对路径
   python3 simple_parameter_analyzer.py /full/path/to/your/data.csv
   ```

3. **图形界面不显示**
   ```bash
   # 如果是SSH连接，启用X11转发
   ssh -X username@hostname
   
   # 或者使用Web版本
   python3 keypoint_parameter_analyzer.py
   ```

4. **中文显示问题**
   - 系统已自动配置中文字体支持
   - 如仍有问题，请安装中文字体包

## 🚀 扩展开发

### 添加新参数
1. 在CSV中添加新列
2. 修改`current_parameters`默认值
3. 更新参数选择列表

### 自定义分析
```python
from simple_parameter_analyzer import SimpleKeypointParameterAnalyzer

# 创建分析器
analyzer = SimpleKeypointParameterAnalyzer("your_data.csv")

# 自定义默认选择
analyzer.current_keypoints = [0, 1, 2]  # 关键点
analyzer.current_parameters = ['confidence', 'velocity']  # 参数

# 显示界面
analyzer.show()
```

## 📝 更新日志

- **v1.0.0**: 初始版本，支持基础时间序列分析
- **v1.1.0**: 添加Web版本支持
- **v1.2.0**: 优化交互体验，添加快速选择功能

---

**开发者**: AI助手  
**最后更新**: 2024年6月 