#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关键点可视化工具
在12800*800的画幅上显示人脸关键点（0~21），右侧显示特征排序
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.widgets import Slider, Button
import seaborn as sns
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体支持 - 基于系统检测结果
available_fonts = ['Noto Sans CJK JP', 'AR PL UMing CN', 'AR PL UKai CN']  # 已确认可用
plt.rcParams['font.sans-serif'] = available_fonts + ['DejaVu Sans', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

class KeypointVisualizer:
    def __init__(self, csv_path: str):
        """
        初始化关键点可视化器
        
        Args:
            csv_path: CSV数据文件路径
        """
        self.csv_path = csv_path
        self.data = None
        self.current_frame = 0
        self.keypoint_range = (0, 21)  # 处理0~21关键点
        
        # 界面设置 - 增大画布以容纳更多信息
        self.fig_width = 20.0    # 增大宽度
        self.fig_height = 12.0   # 增大高度
        self.dpi = 100
        
        # 颜色设置
        self.keypoint_colors = plt.cm.Set3(np.linspace(0, 1, 22))  # 0~21共22个点
        
        self.load_data()
        self.setup_ui()
    
    def load_data(self):
        """加载和预处理数据"""
        print("正在加载数据...")
        
        # 分块读取大文件
        chunk_size = 10000
        chunks = []
        
        for chunk in pd.read_csv(self.csv_path, chunksize=chunk_size):
            # 只保留0~21关键点
            chunk_filtered = chunk[
                (chunk['keypoint_id'] >= self.keypoint_range[0]) & 
                (chunk['keypoint_id'] <= self.keypoint_range[1])
            ]
            chunks.append(chunk_filtered)
        
        self.data = pd.concat(chunks, ignore_index=True)
        
        # 获取帧范围
        self.frame_range = (self.data['frame_id'].min(), self.data['frame_id'].max())
        self.total_frames = len(self.data['frame_id'].unique())
        
        print(f"数据加载完成!")
        print(f"- 总帧数: {self.total_frames}")
        print(f"- 帧范围: {self.frame_range[0]} ~ {self.frame_range[1]}")
        print(f"- 关键点范围: {self.keypoint_range[0]} ~ {self.keypoint_range[1]}")
        print(f"- 数据点数: {len(self.data)}")
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建图形和子图
        self.fig = plt.figure(figsize=(self.fig_width, self.fig_height), dpi=self.dpi)
        
        # 主绘图区域 (左侧，占50%宽度)
        self.ax_main = plt.subplot2grid((12, 12), (1, 0), rowspan=9, colspan=6)
        # 1280*800画布尺寸设置
        # 完整画布范围，确保所有点都能显示
        self.ax_main.set_xlim(0, 1280)   # 1280宽度
        self.ax_main.set_ylim(800, 0)    # 800高度，Y轴向下
        self.ax_main.set_aspect('equal')
        self.ax_main.set_title('关键点可视化 (0~21)', fontsize=14, fontweight='bold')
        self.ax_main.grid(True, alpha=0.3)
        
        # 特征排序区域 (右侧，占50%宽度) - 分为三部分
        self.ax_features = plt.subplot2grid((12, 12), (1, 6), rowspan=3, colspan=6)
        self.ax_features.set_title('当前帧速度排序 (除16,17,18)', fontsize=10, fontweight='bold')
        
        # 方差距离区域 (右侧中部)
        self.ax_variance = plt.subplot2grid((12, 12), (4, 6), rowspan=3, colspan=6)
        self.ax_variance.set_title('当前帧方差距离排序', fontsize=10, fontweight='bold')
        
        # 历史趋势区域 (右侧下半部分)
        self.ax_history = plt.subplot2grid((12, 12), (7, 6), rowspan=3, colspan=6)
        self.ax_history.set_title('前20帧速度趋势', fontsize=10, fontweight='bold')
        
        # 控制区域 (底部)
        self.ax_slider = plt.subplot2grid((12, 12), (11, 0), colspan=12)
        
        # 创建滑动条
        self.slider = Slider(
            self.ax_slider, '帧', 
            self.frame_range[0], self.frame_range[1], 
            valinit=self.frame_range[0], 
            valfmt='%d'
        )
        self.slider.on_changed(self.update_frame)
        
        # 设置按键响应
        self.fig.canvas.mpl_connect('key_press_event', self.on_key_press)
        
        # 初始化绘图
        self.update_visualization()
        
        plt.tight_layout()
    
    def get_frame_data(self, frame_id: int) -> pd.DataFrame:
        """获取指定帧的数据"""
        return self.data[self.data['frame_id'] == frame_id]
    
    def update_frame(self, val):
        """更新当前帧"""
        self.current_frame = int(self.slider.val)
        self.update_visualization()
    
    def on_key_press(self, event):
        """按键响应处理"""
        if event.key == 'right' or event.key == 'd':
            # 右箭头或D键：下一帧
            if self.current_frame < self.frame_range[1]:
                self.current_frame += 1
                self.slider.set_val(self.current_frame)
        elif event.key == 'left' or event.key == 'a':
            # 左箭头或A键：上一帧  
            if self.current_frame > self.frame_range[0]:
                self.current_frame -= 1
                self.slider.set_val(self.current_frame)
        elif event.key == 'up' or event.key == 'w':
            # 上箭头或W键：快进10帧
            new_frame = min(self.current_frame + 10, self.frame_range[1])
            self.current_frame = new_frame
            self.slider.set_val(self.current_frame)
        elif event.key == 'down' or event.key == 's':
            # 下箭头或S键：快退10帧
            new_frame = max(self.current_frame - 10, self.frame_range[0])
            self.current_frame = new_frame
            self.slider.set_val(self.current_frame)
        elif event.key == 'home':
            # Home键：跳转到第一帧
            self.current_frame = self.frame_range[0]
            self.slider.set_val(self.current_frame)
        elif event.key == 'end':
            # End键：跳转到最后一帧
            self.current_frame = self.frame_range[1]
            self.slider.set_val(self.current_frame)
    
    def update_visualization(self):
        """更新可视化"""
        # 清空主绘图区域
        self.ax_main.clear()
        
        # 获取当前帧数据
        frame_data = self.get_frame_data(self.current_frame)
        
        if frame_data.empty:
            # 如果没有数据，使用默认范围
            self.ax_main.set_xlim(0, 1280)
            self.ax_main.set_ylim(800, 0)
            self.ax_main.set_aspect('equal')  
            self.ax_main.set_title(f'关键点可视化 (帧: {self.current_frame})', fontsize=14, fontweight='bold')
            self.ax_main.grid(True, alpha=0.3)
            self.ax_main.text(640, 400, f'帧 {self.current_frame} 无数据', 
                            ha='center', va='center', fontsize=16)
            return
        
        # 计算关键点的几何中心和范围
        x_coords = frame_data['x'].values
        y_coords = frame_data['y'].values
        
        # 几何中心
        center_x = np.mean(x_coords)
        center_y = np.mean(y_coords)
        
        # 当前范围
        x_range = x_coords.max() - x_coords.min()
        y_range = y_coords.max() - y_coords.min()
        
        # 扩大1.5倍，并确保最小显示范围
        expand_factor = 1.5
        min_range = 200  # 最小显示范围
        
        display_x_range = max(x_range * expand_factor, min_range)
        display_y_range = max(y_range * expand_factor, min_range)
        
        # 计算显示边界
        x_min = center_x - display_x_range / 2
        x_max = center_x + display_x_range / 2
        y_min = center_y - display_y_range / 2
        y_max = center_y + display_y_range / 2
        
        # 确保不超出1280*800画布边界
        x_min = max(0, x_min)
        x_max = min(1280, x_max)
        y_min = max(0, y_min)
        y_max = min(800, y_max)
        
        # 如果调整后范围太小，重新居中
        if x_max - x_min < display_x_range:
            center = (x_min + x_max) / 2
            half_range = display_x_range / 2
            x_min = max(0, center - half_range)
            x_max = min(1280, center + half_range)
            
        if y_max - y_min < display_y_range:
            center = (y_min + y_max) / 2
            half_range = display_y_range / 2
            y_min = max(0, center - half_range)
            y_max = min(800, center + half_range)
        
        # 设置动态显示范围
        self.ax_main.set_xlim(x_min, x_max)
        self.ax_main.set_ylim(y_max, y_min)  # Y轴翻转
        self.ax_main.set_aspect('equal')  
        self.ax_main.set_title(f'关键点可视化 (帧: {self.current_frame})', fontsize=14, fontweight='bold')
        self.ax_main.grid(True, alpha=0.3)
        
        # 绘制关键点
        for idx, row in frame_data.iterrows():
            keypoint_id = int(row['keypoint_id'])
            x, y = row['x'], row['y']
            confidence = row['confidence']
            
            # 根据置信度调整点的大小和透明度 - 增大显示尺寸
            size = max(80, confidence * 200)  # 增大基础尺寸
            alpha = max(0.6, confidence)       # 提高透明度
            
            # 绘制关键点
            color = self.keypoint_colors[keypoint_id]
            self.ax_main.scatter(x, y, c=[color], s=size, alpha=alpha, 
                               edgecolors='black', linewidth=0.5)
            
            # 添加关键点编号
            self.ax_main.annotate(str(keypoint_id), (x, y), 
                                xytext=(5, 5), textcoords='offset points',
                                fontsize=8, fontweight='bold')
        
        # 连接关键点（简单的面部轮廓连接）
        # self.draw_face_connections(frame_data)  # 已禁用连接线
        
        # 更新特征排序
        self.update_features_panel(frame_data)
        
        self.fig.canvas.draw()
    
    def draw_face_connections(self, frame_data: pd.DataFrame):
        """绘制面部关键点连接线"""
        if frame_data.empty:
            return
        
        # 创建关键点位置字典
        points = {}
        for _, row in frame_data.iterrows():
            points[int(row['keypoint_id'])] = (row['x'], row['y'])
        
        # 定义连接关系（简化的面部轮廓）
        connections = [
            # 面部轮廓 (假设0-16是面部轮廓点)
            [(i, i+1) for i in range(min(16, max(points.keys())))],
            # 其他可能的连接...
        ]
        
        # 绘制连接线
        for connection_group in connections:
            for start, end in connection_group:
                if start in points and end in points:
                    x1, y1 = points[start]
                    x2, y2 = points[end]
                    self.ax_main.plot([x1, x2], [y1, y2], 'b-', alpha=0.3, linewidth=1)
    
    def update_features_panel(self, frame_data: pd.DataFrame):
        """更新右侧特征面板 - 分别显示当前速度、方差距离和历史趋势"""
        # 清空三个面板
        self.ax_features.clear()
        self.ax_variance.clear()
        self.ax_history.clear()
        
        self.ax_features.set_title('当前帧速度排序 (除16,17,18)', fontsize=10, fontweight='bold')
        self.ax_variance.set_title('当前帧方差距离排序', fontsize=10, fontweight='bold')
        self.ax_history.set_title('前20帧速度趋势', fontsize=10, fontweight='bold')
        
        if frame_data.empty or 'velocity' not in frame_data.columns:
            self.ax_features.text(0.5, 0.5, '无速度数据', ha='center', va='center', 
                                transform=self.ax_features.transAxes)
            self.ax_variance.text(0.5, 0.5, '无方差数据', ha='center', va='center', 
                                transform=self.ax_variance.transAxes)
            self.ax_history.text(0.5, 0.5, '无速度数据', ha='center', va='center', 
                               transform=self.ax_history.transAxes)
            return
        
        # === 上半部分：当前帧速度排序 ===
        current_velocities = []
        current_stds = []
        for _, row in frame_data.iterrows():
            keypoint_id = int(row['keypoint_id'])
            # 过滤掉16、17、18点
            if keypoint_id in [16, 17, 18]:
                continue
                
            velocity = row['velocity']
            # 计算方差距离（std_x和std_y的欧氏距离）
            std_distance = np.sqrt(row['std_x']**2 + row['std_y']**2)
            current_velocities.append((keypoint_id, velocity))
            current_stds.append((keypoint_id, std_distance))
        
        # 按速度值排序（从高到低）
        current_velocities.sort(key=lambda x: x[1], reverse=True)
        current_stds.sort(key=lambda x: x[1], reverse=True)
        
        # 显示过滤后的关键点
        keypoint_ids = [item[0] for item in current_velocities]
        velocities = [item[1] for item in current_velocities]
        colors = [self.keypoint_colors[kp_id] for kp_id in keypoint_ids]
        
        # 创建水平条形图
        y_positions = np.arange(len(keypoint_ids))
        bars = self.ax_features.barh(y_positions, velocities, color=colors, alpha=0.8)
        
        # 设置Y轴标签
        self.ax_features.set_yticks(y_positions)
        kp_labels = [f'KP{kp_id}' for kp_id in keypoint_ids]
        self.ax_features.set_yticklabels(kp_labels, fontsize=7)
        
        # 添加数值标签
        if velocities:
            max_vel = max(velocities)
            for i, (bar, velocity) in enumerate(zip(bars, velocities)):
                                 self.ax_features.text(bar.get_width() + max_vel * 0.02, 
                                     bar.get_y() + bar.get_height()/2, 
                                     f'{velocity:.2f}', 
                                     va='center', ha='left', fontsize=6)
        
        self.ax_features.set_xlabel('速度值', fontsize=8)
        self.ax_features.grid(True, alpha=0.3, axis='x')
        self.ax_features.tick_params(axis='both', which='major', labelsize=6)
        self.ax_features.invert_yaxis()
        
        # === 中部：当前帧方差距离排序 ===
        # 获取方差距离数据并绘制
        std_keypoint_ids = [item[0] for item in current_stds]
        std_distances = [item[1] for item in current_stds]
        std_colors = [self.keypoint_colors[kp_id] for kp_id in std_keypoint_ids]
        
        # 创建水平条形图
        std_y_positions = np.arange(len(std_keypoint_ids))
        std_bars = self.ax_variance.barh(std_y_positions, std_distances, color=std_colors, alpha=0.8)
        
        # 设置Y轴标签
        self.ax_variance.set_yticks(std_y_positions)
        std_kp_labels = [f'KP{kp_id}' for kp_id in std_keypoint_ids]
        self.ax_variance.set_yticklabels(std_kp_labels, fontsize=6)
        
        # 添加数值标签
        if std_distances:
            max_std = max(std_distances)
            for i, (bar, std_dist) in enumerate(zip(std_bars, std_distances)):
                self.ax_variance.text(bar.get_width() + max_std * 0.02, 
                                    bar.get_y() + bar.get_height()/2, 
                                    f'{std_dist:.2f}', 
                                    va='center', ha='left', fontsize=6)
        
        self.ax_variance.set_xlabel('方差距离', fontsize=8)
        self.ax_variance.grid(True, alpha=0.3, axis='x')
        self.ax_variance.tick_params(axis='both', which='major', labelsize=6)
        self.ax_variance.invert_yaxis()
        
        # === 下半部分：历史趋势 ===
        # 获取前20帧的历史数据
        end_frame = self.current_frame
        start_frame = max(0, end_frame - 19)
        
        history_data = self.data[
            (self.data['frame_id'] >= start_frame) & 
            (self.data['frame_id'] <= end_frame) &
            (self.data['keypoint_id'] >= 0) & 
            (self.data['keypoint_id'] <= 21)
        ]
        
        if not history_data.empty:
            # 选择速度变化最大的几个关键点进行展示
            selected_kps = keypoint_ids[:10]  # 取当前速度最快的前10个关键点
            
            for kp_id in selected_kps:
                kp_history = history_data[history_data['keypoint_id'] == kp_id]
                if len(kp_history) > 1:
                    frames = kp_history['frame_id'].values
                    hist_velocities = kp_history['velocity'].values
                    
                    color = self.keypoint_colors[kp_id]
                    self.ax_history.plot(frames, hist_velocities, 
                                       color=color, alpha=0.8, linewidth=2,
                                       marker='o', markersize=3, label=f'KP{kp_id}')
            
            # 高亮当前帧
            self.ax_history.axvline(x=self.current_frame, color='red', linestyle='--', 
                                  alpha=0.8, linewidth=2, label='当前帧')
            
            self.ax_history.set_xlabel('帧号', fontsize=9)
            self.ax_history.set_ylabel('速度', fontsize=9)
            self.ax_history.grid(True, alpha=0.3)
            self.ax_history.tick_params(axis='both', which='major', labelsize=7)
            
            # 添加图例
            self.ax_history.legend(fontsize=6, loc='upper right', ncol=2)
            
            # 设置X轴范围
            self.ax_history.set_xlim(start_frame, end_frame)
        
        else:
            self.ax_history.text(0.5, 0.5, '历史数据不足', ha='center', va='center', 
                               transform=self.ax_history.transAxes, fontsize=8)
    
    def show(self):
        """显示可视化界面"""
        plt.show()

def main():
    """主函数"""
    # 数据文件路径
    csv_path = "data/face_keypoints_temporal_stability_4.csv"
    
    print("=== 关键点可视化工具 ===")
    print("功能:")
    print("- 在12800*800画幅上显示0~21关键点")
    print("- 右侧显示特征排序")
    print("- 支持帧间切换")
    print("========================")
    
    try:
        # 创建可视化器
        visualizer = KeypointVisualizer(csv_path)
        
        print("\n可视化界面已启动!")
        print("控制方式:")
        print("- 滑动条：拖动切换帧")
        print("- 方向键/WASD：←→(A/D)单帧切换，↑↓(W/S)快进快退10帧")
        print("- Home/End：跳转到第一帧/最后一帧")
        print("- 关键点颜色和大小表示置信度")
        print("- 右侧上部：当前帧速度排序(除16,17,18点)")
        print("- 右侧中部：当前帧方差距离排序(sqrt(std_x²+std_y²))")
        print("- 右侧下部：前20帧速度趋势图(颜色对应左侧关键点)")
        
        # 显示界面
        visualizer.show()
        
    except Exception as e:
        print(f"错误: {e}")
        print("请确保数据文件路径正确!")

if __name__ == "__main__":
    main() 