# DMS自动化流水线开发项目计划

## 项目概述

**项目名称**: DMS自动化流水线开发项目  
**项目目标**: 基于现有dms_postmortem_optimised.py和cut_video_optimized.py，开发端到端自动化DMS分析流水线，解决手动流程痛点  
**预期效果**: 处理时间减少80%+，错误率降低至5%以下，支持批量处理和断点续传  

## 核心痛点解决

1. **流程断裂** → 统一任务编排器
2. **环境准备繁琐** → 自动化C++文件部署+配置确认
3. **远程验证复杂** → 自动SSH连接+服务管理+模型同步
4. **重复劳动** → 一键式执行+智能去重

## 技术架构

**项目根目录**: `/home/<USER>/tool_kit/dms_postmortem_end2end`

```
/home/<USER>/tool_kit/dms_postmortem_end2end/
├── dms_postmortem_optimised.py           # 现有文件（保持不变）
├── cut_video_fromwhatyouwant_optimized.py # 现有文件（保持不变）
├── BYD_HKH_R_2.01.07.2025.07.08.4_x86/  # 现有目录（保持不变）
├── dms_automation_pipeline/              # 新增自动化流水线
│   ├── dms_automation_main.py            # 统一入口
│   ├── core/
│   │   ├── task_orchestrator.py         # 核心编排器
│   │   ├── video_preprocessor.py        # 视频预处理+去重
│   │   ├── environment_manager.py       # 环境管理+配置确认
│   │   ├── remote_validator.py          # 远程验证+服务管理
│   │   └── rendering_engine.py          # 渲染引擎
│   ├── config/
│   │   ├── pipeline_config_template.json
│   │   └── validation_schemas.json
│   ├── utils/
│   │   ├── file_utils.py
│   │   ├── validation_utils.py
│   │   └── logger_config.py
│   └── tests/
│       └── [测试文件]
└── dms_automation_pipeline_plan.md      # 项目计划文档
```

## 阶段一：基础框架搭建

### 1.1 创建项目目录结构
**任务描述**: 在 `/home/<USER>/tool_kit/dms_postmortem_end2end` 下创建 dms_automation_pipeline/ 目录及子目录

**验收要求**:
- [ ] 目录结构完整创建
- [ ] 所有子目录包含__init__.py文件
- [ ] README.md文件包含项目说明

**验收方法**:
```bash
# 切换到项目根目录
cd /home/<USER>/tool_kit/dms_postmortem_end2end

# 检查目录结构
ls -la dms_automation_pipeline/
ls -la dms_automation_pipeline/core/
ls -la dms_automation_pipeline/config/
ls -la dms_automation_pipeline/utils/
ls -la dms_automation_pipeline/tests/

# 验证Python包结构
cd dms_automation_pipeline
python -c "import core; print('Core module imported successfully')"
```

### 1.2 实现核心TaskOrchestrator
**任务描述**: 实现状态机管理工作流，支持断点续传和进度跟踪

**验收要求**:
- [ ] 状态机支持5个状态：INIT, VIDEO_PROCESSING, ENV_SETUP, REMOTE_VALIDATION, RENDERING
- [ ] 状态持久化到.state文件
- [ ] 支持从任意状态恢复执行
- [ ] Rich库进度显示

**验收方法**:
```python
# 测试状态机基本功能
orchestrator = TaskOrchestrator(config)
assert orchestrator.current_state == "INIT"

# 测试状态持久化
orchestrator.set_state("VIDEO_PROCESSING")
orchestrator.save_state()
new_orchestrator = TaskOrchestrator.load_from_state()
assert new_orchestrator.current_state == "VIDEO_PROCESSING"
```

### 1.3 配置管理系统
**任务描述**: 设计pipeline_config.json模板和JSON Schema验证规则

**验收要求**:
- [ ] 完整的配置文件模板
- [ ] JSON Schema验证规则
- [ ] 配置文件加载和验证功能
- [ ] 错误信息友好提示

**验收方法**:
```python
# 测试配置加载
config = ConfigManager.load_config("test_config.json")
assert config is not None

# 测试配置验证
invalid_config = {"invalid": "config"}
try:
    ConfigManager.validate_config(invalid_config)
    assert False, "Should raise validation error"
except ValidationError:
    pass  # Expected
```

### 1.4 基础工具类
**任务描述**: 实现file_utils.py、validation_utils.py、logger_config.py

**验收要求**:
- [ ] file_utils: 文件拷贝、权限设置、MD5计算
- [ ] validation_utils: 路径验证、时间格式验证、配置验证
- [ ] logger_config: 统一日志配置、多级别日志

**验收方法**:
```python
# 测试文件工具
from utils.file_utils import copy_with_permissions, calculate_md5
copy_with_permissions("source.txt", "dest.txt")
assert os.path.exists("dest.txt")
assert calculate_md5("test.txt") == "expected_hash"

# 测试验证工具
from utils.validation_utils import validate_time_format
assert validate_time_format("00:04:57") == True
assert validate_time_format("25:00:00") == False
```

### 1.5 主入口程序
**任务描述**: 实现dms_automation_main.py，支持命令行参数和配置文件

**验收要求**:
- [ ] 命令行参数解析
- [ ] 配置文件加载
- [ ] 基本的工作流调用
- [ ] 错误处理和用户友好提示

**验收方法**:
```bash
# 切换到自动化流水线目录
cd /home/<USER>/tool_kit/dms_postmortem_end2end/dms_automation_pipeline

# 测试命令行接口
python dms_automation_main.py --help
python dms_automation_main.py --config test_config.json --dry-run
python dms_automation_main.py --config test_config.json --validate-only
```

**阶段一验收标准**: 
- 所有子任务验收通过
- 基础框架可以加载配置并显示工作流状态
- 代码覆盖率 > 80%

## 阶段二：视频预处理模块

### 2.1 包装cut_video功能
**任务描述**: 基于cut_video_fromwhatyouwant_optimized.py创建包装器类

**验收要求**:
- [ ] VideoPreprocessor类封装cut_video功能
- [ ] 保持原有的FFmpeg处理能力
- [ ] 支持批量视频处理
- [ ] 错误处理和进度回调

**验收方法**:
```python
# 测试视频处理
processor = VideoPreprocessor(config)
result = processor.process_video(
    "/path/to/video.mkv", 
    [("00:04:57", "00:05:17")], 
    roi="1920:1080:0:0"
)
assert result.success == True
assert os.path.exists(result.output_files[0])
```

### 2.2 实现去重机制
**任务描述**: 基于标准化文件命名的重复检测功能

**验收要求**:
- [ ] 标准化文件命名规则：{base_name}_{time_range}_roi_{roi}.mp4
- [ ] 文件存在性检查实现去重
- [ ] 支持强制重新处理选项
- [ ] 去重统计和日志记录

**验收方法**:
```python
# 测试去重功能
dedup = DeduplicationManager("./output/")
path1, needs1 = dedup.check_and_get_output_path("video.mkv", "00:04:57", "00:05:17")
# 第一次应该需要处理
assert needs1 == True

# 创建文件后再次检查
open(path1, 'w').close()
path2, needs2 = dedup.check_and_get_output_path("video.mkv", "00:04:57", "00:05:17")
# 第二次应该跳过
assert needs2 == False
assert path1 == path2
```

### 2.3 批量处理支持
**任务描述**: 支持多个视频文件的并行处理和统一输出管理

**验收要求**:
- [ ] 多视频文件并行处理
- [ ] 统一的输出目录管理
- [ ] 进度跟踪和状态报告
- [ ] 失败任务重试机制

**验收方法**:
```python
# 测试批量处理
batch_config = [
    {"path": "video1.mkv", "time_ranges": ["00:04:57-00:05:17"]},
    {"path": "video2.mkv", "time_ranges": ["00:15:10-00:15:30"]}
]
results = processor.process_batch(batch_config)
assert len(results) == 2
assert all(r.success for r in results)
```

### 2.4 集成到TaskOrchestrator
**任务描述**: 将VideoPreprocessor集成到主工作流中

**验收要求**:
- [ ] TaskOrchestrator可以调用VideoPreprocessor
- [ ] 状态正确转换：INIT → VIDEO_PROCESSING → ENV_SETUP
- [ ] 错误时状态回滚
- [ ] 进度信息正确传递

**验收方法**:
```python
# 测试集成
orchestrator = TaskOrchestrator(config)
orchestrator.execute_stage("VIDEO_PROCESSING")
assert orchestrator.current_state == "ENV_SETUP"
assert orchestrator.get_stage_result("VIDEO_PROCESSING").success == True
```

**阶段二验收标准**:
- 视频预处理功能完整可用
- 去重机制有效工作
- 批量处理性能满足要求
- 与TaskOrchestrator集成无误

## 阶段三：环境管理模块

### 3.1 C++文件自动部署
**任务描述**: 实现自动拷贝和权限设置：test_dms_internal_postmortem、libtx_dms.so等

**验收要求**:
- [ ] 自动拷贝所有必要的C++文件
- [ ] 正确设置可执行权限
- [ ] 验证文件完整性
- [ ] 支持不同版本的C++程序切换

**验收方法**:
```python
# 测试文件部署
env_manager = EnvironmentManager(config)
result = env_manager.deploy_cpp_files()
assert result.success == True

# 验证文件存在和权限
assert os.path.exists("./runtime_env/test_dms_internal_postmortem")
assert os.access("./runtime_env/test_dms_internal_postmortem", os.X_OK)
assert os.path.exists("./runtime_env/libtx_dms.so")
```

### 3.2 配置文件交互确认
**任务描述**: 实现ip_port.json和calidata.json的终端显示和5秒超时确认机制

**验收要求**:
- [ ] 美观的配置文件内容显示（Rich表格）
- [ ] 5秒超时自动确认机制
- [ ] 用户可选择编辑或确认
- [ ] 超时默认行为可配置

**验收方法**:
```python
# 测试配置确认（模拟用户输入）
import io
import sys
from unittest.mock import patch

# 模拟超时情况
with patch('select.select', return_value=([], [], [])):
    result = env_manager.confirm_config_file("ip_port.json")
    assert result == "timeout"

# 模拟用户确认
with patch('select.select', return_value=([sys.stdin], [], [])):
    with patch('sys.stdin.readline', return_value='\n'):
        result = env_manager.confirm_config_file("ip_port.json")
        assert result == "confirm"
```

### 3.3 配置文件编辑功能
**任务描述**: 实现终端交互式配置编辑，支持逐项修改和类型保持

**验收要求**:
- [ ] 逐项配置编辑界面
- [ ] 自动类型检测和转换
- [ ] 输入验证和错误提示
- [ ] 修改后的配置保存

**验收方法**:
```python
# 测试配置编辑
original_config = {"ip": "***********", "port": 1180}
with patch('builtins.input', side_effect=['***********', '1181']):
    new_config = env_manager.edit_config_interactive(original_config)
    assert new_config["ip"] == "***********"
    assert new_config["port"] == 1181
    assert isinstance(new_config["port"], int)
```

### 3.4 环境验证机制
**任务描述**: 验证所有必要文件存在性、权限和配置有效性

**验收要求**:
- [ ] 文件存在性检查
- [ ] 可执行权限验证
- [ ] 配置文件格式验证
- [ ] 依赖库检查

**验收方法**:
```python
# 测试环境验证
validation_result = env_manager.validate_environment()
assert validation_result.all_files_exist == True
assert validation_result.permissions_correct == True
assert validation_result.configs_valid == True
assert len(validation_result.missing_files) == 0
```

**阶段三验收标准**:
- C++文件自动部署成功
- 配置文件交互确认机制工作正常
- 环境验证通过所有检查
- 用户体验友好，错误提示清晰

## 阶段四：远程验证模块

### 4.1 SSH连接管理
**任务描述**: 实现基于paramiko的SSH连接、认证和错误处理

**验收要求**:
- [ ] SSH连接建立和认证
- [ ] 支持密钥和密码认证
- [ ] 连接超时和重试机制
- [ ] 连接池管理

**验收方法**:
```python
# 测试SSH连接
ssh_manager = SSHManager(config)
connection = ssh_manager.connect("***********", "user", key_file="~/.ssh/id_rsa")
assert connection.is_connected() == True

# 测试命令执行
result = connection.execute_command("ls -la")
assert result.return_code == 0
```

### 4.2 远程服务管理
**任务描述**: 检测/userfs/tx_dms_oax_test_tool_update服务状态，支持启动/重启

**验收要求**:
- [ ] 服务状态检测
- [ ] 服务启动功能
- [ ] 服务重启功能
- [ ] 启动等待和验证

**验收方法**:
```python
# 测试服务管理
service_manager = RemoteServiceManager(ssh_connection)
status = service_manager.check_service_status("/userfs/tx_dms_oax_test_tool_update")
assert status in ["running", "stopped", "not_found"]

if status == "stopped":
    result = service_manager.start_service()
    assert result.success == True
```

### 4.3 模型文件同步
**任务描述**: 实现模型文件MD5校验和自动同步（FaceDetection.ovm等）

**验收要求**:
- [ ] 本地和远程文件MD5对比
- [ ] 文件自动上传同步
- [ ] 同步进度显示
- [ ] 同步失败重试

**验收方法**:
```python
# 测试模型同步
sync_manager = ModelSyncManager(ssh_connection)
sync_result = sync_manager.sync_models([
    "FaceDetection.ovm", "FaceKeypoints.ovm", "eye.ovm"
])
assert sync_result.success == True
assert sync_result.synced_files == 3
```

### 4.4 服务连通性验证
**任务描述**: 验证远程服务端口可访问性，确保服务正常运行

**验收要求**:
- [ ] TCP端口连通性测试
- [ ] 服务响应验证
- [ ] 超时和重试机制
- [ ] 连通性报告

**验收方法**:
```python
# 测试服务连通性
connectivity = RemoteValidator.test_service_connectivity("***********", 1180)
assert connectivity.reachable == True
assert connectivity.response_time < 1000  # ms
```

**阶段四验收标准**:
- SSH连接稳定可靠
- 远程服务管理功能完整
- 模型文件同步准确
- 服务连通性验证有效

## 阶段五：渲染引擎模块

### 5.1 包装dms_postmortem功能
**任务描述**: 基于dms_postmortem_optimised.py创建包装器，保持原有功能

**验收要求**:
- [ ] 完整包装原有渲染功能
- [ ] 保持多线程处理能力
- [ ] 支持配置参数传递
- [ ] 错误处理和状态回调

**验收方法**:
```python
# 测试渲染引擎
rendering_engine = RenderingEngine(config)
result = rendering_engine.render_video(
    video_frames_path="./processed_frames/",
    json_results_path="./json_results/",
    output_video_path="./output.mp4"
)
assert result.success == True
assert os.path.exists("./output.mp4")
```

### 5.2 结果文件管理
**任务描述**: 实现自动的输出目录组织、文件命名和版本控制

**验收要求**:
- [ ] 时间戳目录自动创建
- [ ] 结果文件分类存储
- [ ] 版本控制和备份
- [ ] 清理策略配置

**验收方法**:
```python
# 测试结果管理
result_manager = ResultManager(config)
output_dir = result_manager.create_output_directory("BYD_HKH_R_analysis")
assert os.path.exists(output_dir)
assert "20250714" in output_dir  # 包含时间戳

# 测试文件组织
result_manager.organize_results(output_dir, {
    "videos": ["output.mp4"],
    "logs": ["process.log"],
    "configs": ["used_config.json"]
})
```

### 5.3 临时文件清理
**任务描述**: 实现可配置的临时文件清理机制，节省磁盘空间

**验收要求**:
- [ ] 临时文件自动识别
- [ ] 可配置清理策略
- [ ] 安全清理确认
- [ ] 清理日志记录

**验收方法**:
```python
# 测试临时文件清理
cleanup_manager = TempFileCleanup(config)
temp_files = cleanup_manager.identify_temp_files("./temp_processing/")
assert len(temp_files) > 0

cleanup_result = cleanup_manager.cleanup_files(temp_files, confirm=False)
assert cleanup_result.cleaned_count > 0
assert cleanup_result.space_freed > 0
```

### 5.4 错误处理增强
**任务描述**: 增强C++程序调用的错误处理和重试机制

**验收要求**:
- [ ] C++程序调用错误捕获
- [ ] 自动重试机制
- [ ] 错误分类和处理
- [ ] 详细错误日志

**验收方法**:
```python
# 测试错误处理
with patch('subprocess.Popen') as mock_popen:
    mock_popen.return_value.wait.return_value = 1  # 模拟失败
    mock_popen.return_value.stderr.read.return_value = "Error message"

    result = rendering_engine.call_cpp_program("test_input.txt")
    assert result.success == False
    assert "Error message" in result.error_message
    assert result.retry_count > 0
```

**阶段五验收标准**:
- 渲染功能完整保持
- 结果文件管理有序
- 临时文件清理有效
- 错误处理健壮

## 阶段六：集成测试和优化

### 6.1 单元测试
**任务描述**: 为所有核心组件编写单元测试用例

**验收要求**:
- [ ] 代码覆盖率 > 90%
- [ ] 所有公共方法有测试
- [ ] 边界条件测试
- [ ] 异常情况测试

**验收方法**:
```bash
# 切换到项目根目录
cd /home/<USER>/tool_kit/dms_postmortem_end2end

# 运行单元测试
python -m pytest dms_automation_pipeline/tests/ -v --cov=dms_automation_pipeline --cov-report=html
# 检查覆盖率报告
open htmlcov/index.html
```

### 6.2 端到端集成测试
**任务描述**: 使用真实数据进行完整流水线测试

**验收要求**:
- [ ] 完整流程测试通过
- [ ] 真实数据处理验证
- [ ] 性能基准测试
- [ ] 并发处理测试

**验收方法**:
```python
# 端到端测试
config = load_test_config("integration_test.json")
orchestrator = TaskOrchestrator(config)
result = orchestrator.execute_full_pipeline()

assert result.success == True
assert result.processing_time < expected_time_limit
assert os.path.exists(result.output_video)
```

### 6.3 性能优化
**任务描述**: 分析和优化处理性能，确保达到目标效率

**验收要求**:
- [ ] 处理时间相比手动减少80%+
- [ ] 内存使用优化
- [ ] 并发处理优化
- [ ] 性能监控和报告

**验收方法**:
```python
# 性能测试
import time
start_time = time.time()
result = orchestrator.execute_full_pipeline()
end_time = time.time()

processing_time = end_time - start_time
manual_time = estimate_manual_processing_time(config)
efficiency_gain = (manual_time - processing_time) / manual_time

assert efficiency_gain > 0.8  # 80%+ improvement
```

### 6.4 用户文档
**任务描述**: 编写使用文档、配置说明和故障排除指南

**验收要求**:
- [ ] 完整的用户使用手册
- [ ] 配置文件详细说明
- [ ] 常见问题解答
- [ ] 故障排除指南

**验收方法**:
```bash
# 检查文档完整性
ls docs/
# 应包含：
# - user_manual.md
# - configuration_guide.md
# - troubleshooting.md
# - api_reference.md
```

### 6.5 部署验证
**任务描述**: 在目标环境中进行部署验证和最终验收

**验收要求**:
- [ ] 目标环境部署成功
- [ ] 功能验证通过
- [ ] 性能达标
- [ ] 用户培训完成

**验收方法**:
```bash
# 切换到自动化流水线目录
cd /home/<USER>/tool_kit/dms_postmortem_end2end/dms_automation_pipeline

# 部署验证
python dms_automation_main.py --config production_config.json --validate-env
python dms_automation_main.py --config production_config.json --dry-run
python dms_automation_main.py --config production_config.json
```

**阶段六验收标准**:
- 所有测试通过
- 性能达到预期目标
- 文档完整可用
- 生产环境部署成功

## 项目交接信息

### 当前进度跟踪
- **当前阶段**: 计划制定完成
- **下一步**: 开始阶段一实施
- **关键里程碑**: 每个阶段完成后更新此文档

### 技术栈和依赖
```python
# 主要依赖
dependencies = [
    "rich>=12.0.0",      # 终端美化显示
    "paramiko>=2.8.0",   # SSH连接
    "jsonschema>=4.0.0", # 配置验证
    "opencv-python",     # 视频处理
    "psutil",           # 系统监控
    "pytest",           # 测试框架
    "pytest-cov"        # 覆盖率测试
]
```

### 关键配置文件
- `dms_automation_pipeline/config/pipeline_config.json`: 主配置文件
- `BYD_HKH_R_2.01.07.2025.07.08.4_x86/ip_port.json`: 远程服务配置（从现有项目读取）
- `BYD_HKH_R_2.01.07.2025.07.08.4_x86/calidata.json`: 标定数据配置（从现有项目读取）

### 重要约定
1. **项目根目录**: `/home/<USER>/tool_kit/dms_postmortem_end2end`
2. **文件命名**: `{base_name}_{time_range}_roi_{roi}.mp4`
3. **状态文件**: `dms_automation_pipeline/.pipeline_state.json`
4. **日志格式**: 统一使用Python logging模块
5. **错误处理**: 三层错误处理机制
6. **现有代码复用**: 通过包装器模式复用dms_postmortem_optimised.py和cut_video_optimized.py

### 验收检查清单
每个阶段完成后，必须通过以下检查：
- [ ] 所有验收要求满足
- [ ] 验收方法测试通过
- [ ] 代码审查完成
- [ ] 文档更新完成
- [ ] 下一阶段准备就绪

---

**文档版本**: v1.0
**创建时间**: 2025-07-14
**最后更新**: 2025-07-14
**负责人**: AI Assistant
**审核状态**: 待My Lord确认
