#!/usr/bin/env python3
import sys
import os
sys.path.insert(0, '.')

from core.task_orchestrator import TaskOrchestrator

# 创建简单配置
config = {
    "task_info": {"name": "test"},
    "video_config": {"source_videos": [], "output_dir": "./test/"},
    "environment_config": {"cpp_source_path": "./", "target_dir": "./", "required_files": []}
}

# 测试TaskOrchestrator
print("创建TaskOrchestrator...")
orchestrator = TaskOrchestrator(config)
print(f"当前状态: {orchestrator.get_current_state()}")

print("显示状态...")
orchestrator.display_status()

print("测试完成！")
