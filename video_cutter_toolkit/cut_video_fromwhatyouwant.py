import os
import subprocess
import argparse
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
from tqdm import tqdm
import time
import threading
from threading import Lock

class ProgressManager:
    def __init__(self, total_segments):
        self.segments = {}
        self.total_segments = total_segments
        self.lock = Lock()
        self.running = True
        self.display_thread = None
        
    def start_display(self):
        self.display_thread = threading.Thread(target=self._display_loop, daemon=True)
        self.display_thread.start()
        
    def stop_display(self):
        self.running = False
        if self.display_thread:
            self.display_thread.join()
        
    def update_segment(self, index, status, progress=0, message=""):
        with self.lock:
            self.segments[index] = {
                'status': status,  # 'waiting', 'processing', 'completed', 'failed'
                'progress': progress,
                'message': message
            }
    
    def _display_loop(self):
        while self.running:
            self._print_status()
            time.sleep(2)  # 每2秒更新一次显示
    
    def _print_status(self):
        with self.lock:
            # 清屏并移动到顶部
            print("\033[2J\033[H", end="")
            print("=" * 80)
            print(f"视频片段处理进度 (共 {self.total_segments} 个片段)")
            print("=" * 80)
            
            for i in range(self.total_segments):
                if i in self.segments:
                    seg = self.segments[i]
                    status_symbol = {
                        'waiting': '⏳',
                        'processing': '🔄',
                        'completed': '✅',
                        'failed': '❌'
                    }.get(seg['status'], '❓')
                    
                    if seg['status'] == 'processing' and seg['progress'] > 0:
                        progress_bar = "█" * int(seg['progress'] / 5) + "░" * (20 - int(seg['progress'] / 5))
                        print(f"{status_symbol} Segment {i+1:2d}: [{progress_bar}] {seg['progress']:5.1f}% {seg['message']}")
                    else:
                        print(f"{status_symbol} Segment {i+1:2d}: {seg['message']}")
                else:
                    print(f"⏳ Segment {i+1:2d}: 等待开始...")
            print("=" * 80)

class VideoProcessor:
    def __init__(self, input_file, output_dir, roi=None, progress_manager=None):
        self.input_file = input_file
        self.output_dir = output_dir
        self.roi = roi
        self.progress_manager = progress_manager
        self.video_info = self._get_video_info()

    def _get_video_info(self):
        cmd = ['ffprobe', '-v', 'error', '-select_streams', 'v:0',
               '-show_entries', 'stream=width,height', '-of', 'csv=p=0', 
               self.input_file]
        result = subprocess.run(cmd, capture_output=True, text=True)
        width, height = map(int, result.stdout.strip().split(','))
        return {'width': width, 'height': height}

    def _clamp_roi(self, w, h, x, y):
        vw, vh = self.video_info['width'], self.video_info['height']
        w = min(w, vw - x) if x + w > vw else w
        h = min(h, vh - y) if y + h > vh else h
        x = max(0, min(x, vw - w))
        y = max(0, min(y, vh - h))
        return w, h, x, y

    def process_segment(self, start, end, index):
        base_name = os.path.splitext(os.path.basename(self.input_file))[0]
        time_range = f"{start.replace(':', '')}_{end.replace(':', '')}"
        roi_part = f"_{self.roi.replace(':', '_')}" if self.roi else ""
        output_file = os.path.join(self.output_dir, f"{base_name}_{time_range}{roi_part}.mp4")
        
        # 更新状态：开始处理
        if self.progress_manager:
            self.progress_manager.update_segment(index, 'processing', 0, f"{start} - {end}")
        
        ffmpeg_cmd = ['ffmpeg', '-y', '-i', self.input_file,
                     '-ss', start, '-to', end]
        
        filters = []
        if self.roi:
            w, h, x, y = map(int, self.roi.split(':'))
            w, h, x, y = self._clamp_roi(w, h, x, y)
            filters.append(f'crop={w}:{h}:{x}:{y}')
        
        if filters:
            ffmpeg_cmd.extend(['-vf', ','.join(filters)])
        
        ffmpeg_cmd.extend(['-c:v', 'libx264', output_file])
        ffmpeg_cmd.extend(['-progress', 'pipe:1'])
        
        try:
            process = subprocess.Popen(ffmpeg_cmd, stdout=subprocess.PIPE,
                                     stderr=subprocess.PIPE, text=True)
            
            # 处理进度信息
            progress = {}
            total_time = (self._time_to_seconds(end) - self._time_to_seconds(start))
            
            for line in process.stdout:
                if '=' in line:
                    key, value = line.strip().split('=')
                    progress[key] = value
                
                if 'out_time_ms' in progress and self.progress_manager:
                    current_time = int(progress['out_time_ms']) / 1000000
                    progress_percent = (current_time / total_time) * 100 if total_time > 0 else 0
                    
                    speed_str = progress.get('speed', '1x')
                    if speed_str.endswith('x'):
                        speed_str = speed_str[:-1]
                    try:
                        speed = float(speed_str)
                    except ValueError:
                        speed = 1.0
                    
                    message = f"{start} - {end} (速度: {speed:.1f}x)"
                    self.progress_manager.update_segment(index, 'processing', progress_percent, message)
            
            process.wait()
            
            if process.returncode == 0:
                if self.progress_manager:
                    self.progress_manager.update_segment(index, 'completed', 100, f"{start} - {end} 完成")
                return True
            else:
                stderr_output = process.stderr.read()
                if self.progress_manager:
                    self.progress_manager.update_segment(index, 'failed', 0, f"失败: {stderr_output[:50]}...")
                return False
        except Exception as e:
            if self.progress_manager:
                self.progress_manager.update_segment(index, 'failed', 0, f"异常: {str(e)[:50]}...")
            return False

    def _time_to_seconds(self, time_str):
        h, m, s = map(float, time_str.split(':'))
        return h * 3600 + m * 60 + s

    def _seconds_to_time(self, seconds):
        m, s = divmod(seconds, 60)
        h, m = divmod(m, 60)
        return f"{int(h):02d}:{int(m):02d}:{int(s):02d}"

def parse_time_ranges(time_ranges):
    return [tuple(tr.split('-')) for tr in time_ranges.split(';')]

def main():
    parser = argparse.ArgumentParser(description='Video clipping tool')
    parser.add_argument('-i', '--input', required=True, help='Input video file')
    parser.add_argument('-o', '--output', required=True, help='Output directory')
    parser.add_argument('-t', '--time', required=True, 
                       help='Time ranges in format start1-end1;start2-end2')
    parser.add_argument('--roi', help='Region of interest in format width:height:x:y')
    parser.add_argument('--threads', type=int, default=os.cpu_count(),
                       help='Number of threads to use')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.output):
        os.makedirs(args.output)
    
    time_ranges = parse_time_ranges(args.time)
    progress_manager = ProgressManager(len(time_ranges))
    processor = VideoProcessor(args.input, args.output, args.roi, progress_manager)
    
    # 启动进度显示
    progress_manager.start_display()
    
    try:
        with ThreadPoolExecutor(max_workers=args.threads) as executor:
            futures = [executor.submit(processor.process_segment, start, end, i)
                      for i, (start, end) in enumerate(time_ranges)]
            
            # 等待所有任务完成
            results = []
            for future in as_completed(futures):
                results.append(future.result())
        
        # 停止进度显示
        progress_manager.stop_display()
        
        # 最终结果显示
        print("\033[2J\033[H", end="")  # 清屏
        successful = sum(results)
        total = len(results)
        print("=" * 80)
        print(f"处理完成！成功: {successful}/{total}")
        print("=" * 80)
        
        if successful == total:
            print("✅ 所有视频片段处理成功！")
        else:
            print(f"⚠️  {total - successful} 个片段处理失败")
        print("")
        
    except KeyboardInterrupt:
        progress_manager.stop_display()
        print("\n\n用户中断处理...")
    except Exception as e:
        progress_manager.stop_display()
        print(f"\n\n处理过程中发生错误: {e}")

if __name__ == '__main__':
    main() 
# python3 cut_video_fromwhatyouwant.py -i input.mp4 -o output_dir -t "00:01:00-00:01:30;00:02:00-00:02:30" --roi 1280:800:0:0 --threads 12
# python3 cut_video_fromwhatyouwant.py -i /home/<USER>/share/Data_need_classified/DMS/BYD_dms/海鸥-EQ_L/20250424_EQ跟测视频记录/1-1.mkv -o /home/<USER>/data/dms/byd/eq/250424/ -t "00:30:48-00:31:28" --roi 1920:1080:0:0 --threads 12
# python3 cut_video_fromwhatyouwant.py -i /home/<USER>/share/Data_need_classified/DMS/BYD_dms/EK_L/20250430_EK25六部跟测视频记录/1-1.mkv -o /home/<USER>/data/dms/byd/ek_l/250513/ -t "00:01:00-00:06:00" --roi 1920:1080:0:0 --threads 12
# python3 cut_video_fromwhatyouwant.py -i /home/<USER>/data/dms/byd/hkh_r/250703/2025-07-03\ 14-03-03.mkv -o /home/<USER>/data/dms/byd/hkh_r/250703/ -t "00:08:48-00:09:08;" --roi 1920:1080:0:0 --threads 12