# 视频切割工具包 (Video Cutter Toolkit)

这是一个专业的视频切割工具包，包含原版和优化版的视频切割脚本，主要用于DMS（驾驶员监控系统）相关的视频处理。

## 📁 文件结构

```
video_cutter_toolkit/
├── README.md                           # 本说明文档
├── cut_video_fromwhatyouwant.py        # 原版视频切割脚本
├── cut_video_fromwhatyouwant_optimized.py  # 优化版视频切割脚本 ⭐推荐
├── video_config.json                   # 配置文件示例
├── test_optimized.py                   # 测试脚本
└── examples/                           # 使用示例（如需要）
```

## 🚀 快速开始

### 环境要求

```bash
# 安装Python依赖
pip install rich psutil

# 确保系统已安装FFmpeg
ffmpeg -version
```

### 基本使用

```bash
# 使用优化版（推荐）
python3 cut_video_fromwhatyouwant_optimized.py \
  -i input.mp4 \
  -o output_directory \
  -t "00:01:00-00:01:30;00:02:00-00:02:30"

# 使用原版
python3 cut_video_fromwhatyouwant.py \
  input.mp4 output_directory \
  "00:01:00-00:01:30;00:02:00-00:02:30"
```

## 📊 版本对比

| 特性 | 原版 | 优化版 |
|------|------|--------|
| 终端显示 | ❌ 清屏闪烁问题 | ✅ Rich库美观显示 |
| 内存管理 | ❌ 可能内存泄漏 | ✅ 流式处理优化 |
| 并发控制 | ⚠️ 固定线程数 | ✅ 智能线程池 |
| 错误处理 | ⚠️ 基础处理 | ✅ 完善的异常处理 |
| 输入验证 | ❌ 缺少验证 | ✅ 严格参数校验 |
| 配置管理 | ❌ 硬编码参数 | ✅ 支持配置文件 |
| 信号处理 | ❌ 无优雅退出 | ✅ Ctrl+C安全退出 |
| 进度显示 | ⚠️ 简单文本 | ✅ 实时进度条 |
| 路径显示 | ❌ 无输出路径提示 | ✅ 显示生成文件完整路径 |

## 🛠️ 优化版详细功能

### 1. 命令行参数

```bash
python3 cut_video_fromwhatyouwant_optimized.py [选项]

必需参数:
  -i, --input INPUT     输入视频文件路径
  -o, --output OUTPUT   输出目录路径
  -t, --time TIME       时间范围，格式: "start1-end1;start2-end2"

可选参数:
  --roi ROI            感兴趣区域，格式: "width:height:x:y"
  --threads THREADS    线程数（默认自动计算）
  --config CONFIG      配置文件路径
  --dry-run           仅验证参数，不执行处理
  -h, --help          显示帮助信息
```

### 2. 配置文件

`video_config.json` 示例：

```json
{
    "ffmpeg_preset": "medium",
    "ffmpeg_crf": 23,
    "max_memory_usage": 80,
    "update_interval": 0.5,
    "supported_formats": [".mp4", ".mkv", ".avi", ".mov", ".wmv", ".flv"],
    "output_format": "mp4",
    "hardware_acceleration": "auto"
}
```

### 3. 使用示例

#### 基础切割
```bash
python3 cut_video_fromwhatyouwant_optimized.py \
  -i /path/to/video.mp4 \
  -o ./output \
  -t "00:01:00-00:01:30"
```

#### 多段切割
```bash
python3 cut_video_fromwhatyouwant_optimized.py \
  -i /path/to/video.mp4 \
  -o ./output \
  -t "00:01:00-00:01:30;00:02:00-00:02:30;00:03:00-00:03:30"
```

#### ROI裁剪
```bash
python3 cut_video_fromwhatyouwant_optimized.py \
  -i /path/to/video.mp4 \
  -o ./output \
  -t "00:01:00-00:01:30" \
  --roi "1280:800:0:0"
```

#### 使用配置文件
```bash
python3 cut_video_fromwhatyouwant_optimized.py \
  -i /path/to/video.mp4 \
  -o ./output \
  -t "00:01:00-00:01:30" \
  --config video_config.json
```

#### 参数验证模式
```bash
python3 cut_video_fromwhatyouwant_optimized.py \
  -i /path/to/video.mp4 \
  -o ./output \
  -t "00:01:00-00:01:30" \
  --dry-run
```

## 🧪 测试

运行测试脚本验证工具功能：

```bash
python3 test_optimized.py
```

预期输出：
```
开始测试优化版视频切割工具...
==================================================
测试配置管理...
✅ 配置管理测试通过
测试输入验证...
✅ 输入验证测试通过
测试时间范围解析...
✅ 时间范围解析测试通过
测试资源监控...
✅ 资源监控测试通过，推荐线程数: 16
==================================================
🎉 所有测试通过！优化版本工作正常
```

## 🔧 技术特性

### 优化版核心改进

1. **终端显示优化**
   - 使用Rich库替代原始ANSI转义序列
   - 避免清屏闪烁问题
   - 美观的进度条和状态显示

2. **内存管理优化**
   - 流式处理FFmpeg输出
   - 避免内存累积和泄漏
   - 资源使用监控

3. **并发处理优化**
   - 智能线程池大小计算
   - 考虑I/O限制的并发策略
   - 优雅的任务调度

4. **错误处理增强**
   - 完整的输入参数验证
   - 详细的错误信息提示
   - 异常恢复机制

5. **用户体验提升**
   - 信号处理和优雅退出
   - 实时进度显示
   - 配置文件支持

## 📝 注意事项

1. **时间格式**: 必须使用 `HH:MM:SS` 格式
2. **ROI格式**: `width:height:x:y`，所有值必须为正整数
3. **文件路径**: 支持相对路径和绝对路径
4. **输出目录**: 如果不存在会自动创建
5. **中断处理**: 使用 Ctrl+C 可以安全中断处理

## 🐛 故障排除

### 常见问题

1. **终端显示异常**
   - 使用优化版脚本
   - 确保终端支持UTF-8编码

2. **内存不足**
   - 调整配置文件中的 `max_memory_usage`
   - 减少并发线程数

3. **FFmpeg错误**
   - 检查FFmpeg是否正确安装
   - 验证输入文件格式是否支持

4. **权限问题**
   - 确保对输入文件有读权限
   - 确保对输出目录有写权限

## 📄 许可证

本工具包遵循项目原有许可证。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个工具包。

---

**推荐使用优化版 `cut_video_fromwhatyouwant_optimized.py` 以获得最佳体验！**
