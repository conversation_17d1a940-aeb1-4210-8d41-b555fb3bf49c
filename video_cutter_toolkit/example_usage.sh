#!/bin/bash
# 视频切割工具使用示例脚本

echo "=== 视频切割工具使用示例 ==="
echo

# 检查依赖
echo "1. 检查依赖..."
if ! command -v ffmpeg &> /dev/null; then
    echo "❌ FFmpeg 未安装，请先安装 FFmpeg"
    exit 1
fi

if ! python3 -c "import rich, psutil" &> /dev/null; then
    echo "❌ Python依赖缺失，正在安装..."
    pip install rich psutil
fi

echo "✅ 依赖检查完成"
echo

# 运行测试
echo "2. 运行功能测试..."
python3 test_optimized.py
echo

# 示例用法说明
echo "3. 使用示例："
echo

echo "基础切割："
echo "python3 cut_video_fromwhatyouwant_optimized.py \\"
echo "  -i input.mp4 \\"
echo "  -o output_dir \\"
echo "  -t \"00:01:00-00:01:30\""
echo

echo "多段切割："
echo "python3 cut_video_fromwhatyouwant_optimized.py \\"
echo "  -i input.mp4 \\"
echo "  -o output_dir \\"
echo "  -t \"00:01:00-00:01:30;00:02:00-00:02:30\""
echo

echo "ROI裁剪："
echo "python3 cut_video_fromwhatyouwant_optimized.py \\"
echo "  -i input.mp4 \\"
echo "  -o output_dir \\"
echo "  -t \"00:01:00-00:01:30\" \\"
echo "  --roi \"1280:800:0:0\""
echo

echo "使用配置文件："
echo "python3 cut_video_fromwhatyouwant_optimized.py \\"
echo "  -i input.mp4 \\"
echo "  -o output_dir \\"
echo "  -t \"00:01:00-00:01:30\" \\"
echo "  --config video_config.json"
echo

echo "参数验证模式："
echo "python3 cut_video_fromwhatyouwant_optimized.py \\"
echo "  -i input.mp4 \\"
echo "  -o output_dir \\"
echo "  -t \"00:01:00-00:01:30\" \\"
echo "  --dry-run"
echo

echo "=== 工具包设置完成！==="
echo "请查看 README.md 获取详细使用说明"
