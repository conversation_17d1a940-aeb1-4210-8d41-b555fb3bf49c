#!/usr/bin/env python3
"""
测试终端重置功能的脚本
"""

import sys
import time
from rich.console import Console
from rich.live import Live
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn
from rich.panel import Panel

def test_terminal_reset():
    """测试终端重置功能"""
    console = Console()
    
    print("=== 终端重置功能测试 ===")
    print("这个测试将模拟视频处理的终端显示，然后测试重置功能")
    print("请观察测试完成后终端是否正常工作")
    print()
    
    # 模拟Rich Live显示
    progress = Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
        console=console
    )
    
    # 添加一些任务
    task1 = progress.add_task("处理片段 1", total=100)
    task2 = progress.add_task("处理片段 2", total=100)
    task3 = progress.add_task("处理片段 3", total=100)
    
    # 创建Live显示
    live = Live(
        Panel(progress, title="模拟视频处理进度", border_style="blue"),
        console=console,
        refresh_per_second=4
    )
    
    try:
        print("开始模拟处理...")
        live.start()
        
        # 模拟进度更新
        for i in range(101):
            progress.update(task1, completed=i)
            if i > 20:
                progress.update(task2, completed=i-20)
            if i > 40:
                progress.update(task3, completed=i-40)
            time.sleep(0.05)
        
        print("模拟处理完成，测试终端重置...")
        
    finally:
        # 停止Live显示
        live.stop()
        
        # 应用我们的终端重置函数
        safe_terminal_reset()
        
        # 清理console状态
        console.clear()
        console.show_cursor(True)
    
    # 测试终端是否正常
    print("\n=== 终端重置测试完成 ===")
    print("请测试以下功能是否正常：")
    print("1. 输入命令（如 'ls'）")
    print("2. 按回车键")
    print("3. 光标显示")
    print("4. 文本显示")
    print()
    print("如果以上功能都正常，说明终端重置成功！")

def safe_terminal_reset():
    """安全的终端重置函数（复制自主程序）"""
    try:
        import os
        
        # 保存当前终端设置
        if hasattr(sys.stdout, 'isatty') and sys.stdout.isatty():
            # 发送完全重置序列
            reset_sequences = [
                '\033[!p',      # 软重置
                '\033[?25h',    # 显示光标
                '\033[0m',      # 重置所有属性
                '\033[?1049l',  # 退出备用屏幕缓冲区
                '\033[?1000l',  # 禁用鼠标跟踪
                '\033[?1002l',  # 禁用按钮事件跟踪
                '\033[?1003l',  # 禁用任何事件跟踪
                '\033[?1006l',  # 禁用SGR扩展鼠标模式
            ]
            
            for seq in reset_sequences:
                sys.stdout.write(seq)
            
            sys.stdout.flush()
            
            # 使用stty重置终端（如果可用）
            try:
                os.system('stty sane 2>/dev/null')
            except:
                pass
                
    except Exception:
        pass

if __name__ == '__main__':
    test_terminal_reset()
