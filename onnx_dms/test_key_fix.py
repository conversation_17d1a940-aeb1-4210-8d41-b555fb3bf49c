#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的按键功能
"""

import cv2
import numpy as np

def test_key_loop():
    """测试按键循环逻辑"""
    
    # 创建测试图片
    img = np.zeros((400, 600, 3), dtype=np.uint8)
    img[:, :] = [100, 150, 200]  # BGR
    
    cv2.putText(img, "Key Test - Press keys:", (50, 150), 
               cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    cv2.putText(img, "'f' = continue", (50, 200), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
    cv2.putText(img, "'s' = save", (50, 230), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
    cv2.putText(img, "'q' = quit", (50, 260), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
    
    cv2.imshow("Key Test", img)
    
    print("测试按键循环逻辑")
    print("=" * 40)
    
    # 模拟图片处理的按键循环
    while True:
        print("按键控制: 'f'=继续, 's'=保存, 'q'=退出, ESC=退出")
        key = cv2.waitKey(0) & 0xFF
        
        if key == ord('q') or key == 27:  # 'q' 或 ESC 键退出
            print("用户退出")
            cv2.destroyAllWindows()
            break
        elif key == ord('s'):  # 's' 键保存
            save_path = "test_saved_image.jpg"
            cv2.imwrite(save_path, img)
            print(f"结果已保存到: {save_path}")
        elif key == ord('f'):  # 'f' 键继续
            print("继续处理下一张...")
            break  # 退出按键等待循环
        else:
            # 未识别的按键，继续等待
            print(f"未识别的按键 (按键码: {key})，请使用: 'f'=继续, 's'=保存, 'q'=退出")
            continue
    
    cv2.destroyAllWindows()
    print("测试完成")

if __name__ == "__main__":
    test_key_loop()
