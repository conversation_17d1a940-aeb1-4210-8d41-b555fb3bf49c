#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多张图片处理的脚本
"""

import cv2
import numpy as np
import os

def create_test_images():
    """创建多张测试图片"""
    
    test_dir = "test_multi"
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    colors = [
        ([50, 50, 200], "Red Image"),      # 红色
        ([50, 200, 50], "Green Image"),    # 绿色  
        ([200, 50, 50], "Blue Image"),     # 蓝色
        ([100, 100, 100], "Gray Image"),   # 灰色
        ([200, 200, 50], "Cyan Image")     # 青色
    ]
    
    for i, (color, text) in enumerate(colors):
        img = np.zeros((400, 600, 3), dtype=np.uint8)
        img[:, :] = color
        
        # 添加文字
        cv2.putText(img, text, (150, 150), cv2.FONT_HERSHEY_SIMPLEX, 1.5, (255, 255, 255), 3)
        cv2.putText(img, f"Image {i+1} of {len(colors)}", (150, 200), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        cv2.putText(img, "Press 'f' to continue", (150, 250), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
        cv2.putText(img, "Press 'q' to quit", (150, 280), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
        
        filename = f"test_image_{i+1:02d}.png"
        filepath = os.path.join(test_dir, filename)
        cv2.imwrite(filepath, img)
        print(f"创建测试图片: {filepath}")
    
    print(f"\n创建了 {len(colors)} 张测试图片在 {test_dir}/ 目录")
    print(f"运行测试: python test_glass.py {test_dir}")
    
    return test_dir

def simulate_key_processing():
    """模拟按键处理逻辑"""
    
    files = ["image1.png", "image2.png", "image3.png"]
    
    print("模拟多文件处理流程:")
    print("=" * 40)
    
    for i, filename in enumerate(files):
        print(f"\n正在处理: {filename} ({i+1}/{len(files)})")
        
        # 模拟按键等待
        while True:
            print("按键控制: 'f'=继续, 's'=保存, 'q'=退出")
            
            # 模拟用户输入 (实际中这里是 cv2.waitKey)
            user_input = input("模拟按键 (f/s/q): ").strip().lower()
            
            if user_input == 'q':
                print("用户退出")
                return
            elif user_input == 's':
                print(f"保存 {filename}")
                # 继续等待下一个按键
            elif user_input == 'f':
                print("继续下一张...")
                break  # 退出按键循环，处理下一个文件
            else:
                print("未识别的按键，请重新输入")
                continue
    
    print("\n所有文件处理完成！")

if __name__ == "__main__":
    print("选择测试模式:")
    print("1. 创建测试图片")
    print("2. 模拟按键处理逻辑")
    
    choice = input("请选择 (1/2): ").strip()
    
    if choice == "1":
        create_test_images()
    elif choice == "2":
        simulate_key_processing()
    else:
        print("无效选择")
