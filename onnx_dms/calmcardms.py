import onnxruntime
import cv2
import numpy as np
import json
import os
import torch
import inference_result_pb2
import requests
class ONNXModel(object):
    def __init__(self, onnx_path):
        """
        :param onnx_path:
        """
        self.onnx_session = onnxruntime.InferenceSession(onnx_path)
        self.input_name = self.get_input_name(self.onnx_session)
        self.output_name = self.get_output_name(self.onnx_session)
        # print("input_name:{}".format(self.input_name))
        # print("output_name:{}".format(self.output_name))
 
    def get_output_name(self, onnx_session):
        """
        output_name = onnx_session.get_outputs()[0].name
        :param onnx_session:
        :return:
        """
        output_name = []
        for node in onnx_session.get_outputs():
            output_name.append(node.name)
        return output_name
 
    def get_input_name(self, onnx_session):
        """
        input_name = onnx_session.get_inputs()[0].name
        :param onnx_session:
        :return:
        """
        input_name = []
        for node in onnx_session.get_inputs():
            input_name.append(node.name)
        return input_name
 
    def get_input_feed(self, input_name, image_tensor):
        """
        input_feed={self.input_name: image_tensor}
        :param input_name:
        :param image_tensor:
        :return:
        """
        input_feed = {}
        for name in input_name:
            input_feed[name] = image_tensor
        return input_feed
 
    def forward(self, image_tensor):
        '''
        image_tensor = image.transpose(2, 0, 1)
        image_tensor = image_tensor[np.newaxis, :]
        onnx_session.run([output_name], {input_name: x})
        :param image_tensor:
        :return:
        '''
        # 输入数据的类型必须与模型一致,以下三种写法都是可以的
        # scores, boxes = self.onnx_session.run(None, {self.input_name: image_tensor})
        # scores, boxes = self.onnx_session.run(self.output_name, input_feed={self.input_name: image_tensor})
        input_feed = self.get_input_feed(self.input_name, image_tensor)
        output_tensors = self.onnx_session.run(self.output_name, input_feed=input_feed)
        # output=dict()
        # for j in range(0,len(self.output_name)):
        #     output[self.output_name[j]]=output_tensors[j]
        return output_tensors
class RestfulApi1Model(object):
    def __init__(self, url):
        self.url=url
    def forward(self, image_tensor):
        response_image = requests.post(self.url, data=image_tensor.tobytes(), headers={'Content-Type': 'image/jpeg'})
        if response_image.status_code == 200:
            # 假设成功获取到 Protobuf 响应数据
            protobuf_data = response_image.content  # 获取原始 Protobuf 字节数据

            # 解析 Protobuf 数据
            inference_result = inference_result_pb2.InferenceResult()
            inference_result.ParseFromString(protobuf_data)

            output=[]
            # 打印解析结果
            for tensor in inference_result.tensors:
                if(len(tensor.data)>0):
                    output.append(np.array(tensor.data).reshape(tensor.shape))
                    # print("Tensor shape:", tensor.shape)
                # print("Tensor data:", tensor.data)
            # print(output)
            return output
        else:
            print(f"Image Request failed with status code {response_image.status_code}")

def creat_backend(config):
    if(config["backend"]=="ONNX"):
        return ONNXModel(config["filename"])
    elif(config["backend"]=="RESFUL_API1"):
        return RestfulApi1Model(config["filename"])
        

class DetectObj(object):
    def __init__(self):
        self.xmin=0
        self.ymin=0
        self.xmax=0
        self.ymax=0
        self.label=0
        self.score=0
    def zoom(self,max_output_w,max_output_h,max_input_w,max_input_h):
        obj=DetectObj()
        obj.label=self.label
        obj.score=self.score
        obj.xmin=int(self.xmin*max_output_w/max_input_w)
        obj.ymin=int(self.ymin*max_output_h/max_input_h)
        obj.xmax=int(self.xmax*max_output_w/max_input_w)
        obj.ymax=int(self.ymax*max_output_h/max_input_h)

        # print(obj.xmin,obj.ymin,obj.xmax,obj.ymax)
        if(obj.xmin<0):
            obj.xmin=0
        if(obj.xmin>max_output_w):
            obj.xmin=max_output_w

        if(obj.xmax<0):
            obj.xmax=0
        if(obj.xmax>max_output_w):
            obj.xmax=max_output_w

        if(obj.ymin<0):
            obj.ymin=0
        if(obj.ymin>max_output_h):
            obj.ymin=max_output_h

        if(obj.ymax<0):
            obj.ymax=0
        if(obj.ymax>max_output_h):
            obj.ymax=max_output_h
        return obj

import math
def inverse_sigmoid(x):
    return math.log(x/(1-x))
def sigmoid(x):
    return (1 / (1 + math.exp(-x)))

def py_cpu_nms(dets, thresh):
 
    dets=np.array(dets)
    x1 = dets[:,0]
    y1 = dets[:,1]
    x2 = dets[:,2]
    y2 = dets[:,3]
    areas = (y2-y1+1) * (x2-x1+1)
    scores = dets[:,4]
    keep = []
    index = scores.argsort()[::-1]
    while index.size >0:
        i = index[0]       # every time the first is the biggst, and add it directly
        keep.append(i)
 
 
        x11 = np.maximum(x1[i], x1[index[1:]])    # calculate the points of overlap 
        y11 = np.maximum(y1[i], y1[index[1:]])
        x22 = np.minimum(x2[i], x2[index[1:]])
        y22 = np.minimum(y2[i], y2[index[1:]])
        
 
        w = np.maximum(0, x22-x11+1)    # the weights of overlap
        h = np.maximum(0, y22-y11+1)    # the height of overlap
       
        overlaps = w*h
        ious = overlaps / (areas[i]+areas[index[1:]] - overlaps)
 
        idx = np.where(ious<=thresh)[0]
        index = index[idx+1]   # because index start from 1
 
    return keep

class FaceDetectionDecoder(object):
    def __init__(self,stride=8,scale=20.0,size=[160,96],score_th=0.5,iou_th=0.1):
        self.size=size
        self.stride=stride
        self.scale=scale
        self.iou_th=iou_th
        self.score_th=inverse_sigmoid(score_th)
    def decode(self, x):
        
        batch=x.shape[0]
        f_w=int(self.size[0]/self.stride)
        f_h=int(self.size[1]/self.stride)
        loop_num=f_w*f_h
        objs=[]
        # print(x)
        for b in range(batch):
            obj=[]
            
            for i in range(loop_num):
                f_x=int(i%f_w)
                f_y=int(i/f_w)
                score=x[b][f_y][f_x][0].item()
                if(score>=self.score_th):
                    
                    x_min=round((f_x+0.5)*self.stride-(x[b][f_y][f_x][1].item()+6)*self.scale)
                    y_min=round((f_y+0.5)*self.stride-(x[b][f_y][f_x][2].item()+6)*self.scale)
                    x_max=round((f_x+0.5)*self.stride+(x[b][f_y][f_x][3].item()+6)*self.scale)
                    y_max=round((f_y+0.5)*self.stride+(x[b][f_y][f_x][4].item()+6)*self.scale)
                    obj.append([x_min,y_min,x_max,y_max,sigmoid(score)])
            if(len(obj)>1):
                keep=py_cpu_nms(obj,self.iou_th)
                nms_obj=[]
                for i in keep :
                    nms_obj.append(obj[i])
                objs.append(nms_obj)
            else:
                objs.append(obj)
        return objs

class FaceKeypointsDecoder(object):
    def __init__(self,stride=16,size=[192,192]):
        self.size=size
        self.stride=stride
        self.radius=math.sqrt(2*self.stride*self.stride)/2
        self.feat_w=int(self.size[0]/self.stride)
        self.feat_h=int(self.size[1]/self.stride)
    def decode(self, x):
        batch=x.shape[0]
        ch=int(x.shape[1]/3)
        tmp=x
        heatmaps=[]
        x_offsets=[]
        y_offsets=[]
        for p in range(ch):
            heatmaps.append(tmp[:,p:p+1])
            x_offsets.append(tmp[:,p*2+ch:p*2+ch+1])
            y_offsets.append(tmp[:,p*2+ch+1:p*2+ch+2])
        heatmap=np.concatenate(heatmaps,1) 
        x_offsets=np.concatenate(x_offsets,1)
        y_offsets=np.concatenate(y_offsets,1)
        tmp=heatmap.reshape(batch,1,ch,-1)
        # print(np.argmax(tmp,axis=3))
        # print(np.max(tmp,axis=3))
        index=np.argmax(tmp,axis=3)
        value=np.max(tmp,axis=3)
        y=index/(x.shape[3])
        x=index%(x.shape[3])
        value=value.reshape(-1,ch,1)
        x=x.reshape(-1,ch,1)
        y=y.reshape(-1,ch,1)          
        index=np.concatenate([value,x,y],2)
        out=np.zeros((batch,ch,3))
        for b in range(batch):
           for i in range(ch):
               score=index[b][i][0].item()
               x_start=int(index[b][i][1].item())
               y_start=int(index[b][i][2].item())
               out[b][i][0]=sigmoid(score)
               out[b][i][1]=(x_start+0.5+(x_offsets[b][i][y_start][x_start].item()/3))*self.stride
               out[b][i][2]=(y_start+0.5+(y_offsets[b][i][y_start][x_start].item()/3))*self.stride
        return out



class LabelsDetsPostprocessor(object):
    def __init__(self,config_dict):
        self.labels_key=config_dict["labels_key"]
        self.dets_key=config_dict["dets_key"]
        self.score=config_dict["score"]
        self.max_x=config_dict["max_output_size"][1]
        self.max_y=config_dict["max_output_size"][0]
        self.decoder=FaceDetectionDecoder()
        pass
    def call(self,inputs,max_output_size,pad=(0,0)):
        outputs=[]
        obj=self.decoder.decode(inputs[0])  

        for i in range(len(obj)):
            output=[]
            bboxs=obj[i]
            for j in range(len(bboxs)):
                bbox=bboxs[j]
                if(bbox[4]>self.score):
                    if(bbox[0]<0):
                        bbox[0]=0
                    if(bbox[0]>self.max_x):
                        bbox[0]=self.max_x              

                    if(bbox[1]<0):
                        bbox[1]=0
                    if(bbox[1]>self.max_y):
                        bbox[1]=self.max_y   

                    if(bbox[2]<0):
                        bbox[2]=0
                    if(bbox[2]>self.max_x):
                        bbox[2]=self.max_x 

                    if(bbox[3]<0):
                        bbox[3]=0
                    if(bbox[3]>self.max_y):
                        bbox[3]=self.max_y

                    obj=DetectObj()
                    obj.xmin=int(bbox[0]*max_output_size[1]/(self.max_x-pad[1]))
                    obj.ymin=int(bbox[1]*max_output_size[0]/(self.max_y-pad[0]))
                    obj.xmax=int(bbox[2]*max_output_size[1]/(self.max_x-pad[1]))
                    obj.ymax=int(bbox[3]*max_output_size[0]/(self.max_y-pad[0]))
                    obj.score=bbox[4]
                    obj.label=0
                    output.append(obj)
                    # print(output)
            outputs.append(output)
        return outputs


class AlignmentPostprocessor(object):
    def __init__(self,config_dict):
        self.headPose_key=config_dict["headPose_key"]
        self.aligment_key=config_dict["aligment_key"]
        self.aligment_num=config_dict["aligment_num"]
        self.max_x=config_dict["max_output_size"][1]
        self.max_y=config_dict["max_output_size"][0]
        pass
    def call(self,inputs,max_output_size,pad=(0,0)):
        # result.face_score,result.mask_score,result.glass_score,result.irblocked_score,result.face_angle,result.face_keypoint
        batch=len(inputs[self.aligment_key])
        outputs=[]
        for i in range(batch):
            output=[]
    
            # yaw=inputs[self.headPose_key][i][2]*(180.0/3.1415926)
            # pit=inputs[self.headPose_key][i][1]*(180.0/3.1415926)
            # roll=inputs[self.headPose_key][i][0]*(180.0/3.1415926)
            # face_angle=[yaw,pit,roll]
            # output.append(face_angle)
            face_angle=[0,0,0]
            output.append(face_angle)
            
            face_keypoint=[]

            for j in range(0,self.aligment_num):
                x_start=int(inputs[self.aligment_key][i][j][1])
                y_start=int(inputs[self.aligment_key][i][j][2])
                x_offset=inputs["x_offset"]
                y_offset=inputs["y_offset"]
                x=int((x_offset[i][j][y_start][x_start]+x_start)*32)
                y=int((y_offset[i][j][y_start][x_start]+y_start)*32)
  

                score=inputs[self.aligment_key][i][j][0]
                # print(inputs[self.aligment_key][i][0][0])
                if(x<0):
                    x=0
                if(x>self.max_x):
                    x=self.max_x
                if(y<0):
                    y=0
                if(y>self.max_y):
                    y=self.max_y
                x*=max_output_size[1]/(self.max_x-pad[1])
                y*=max_output_size[0]/(self.max_y-pad[0])
                face_keypoint.append((int(x),int(y),score))
                
            output.append(tuple(face_keypoint))
            outputs.append(output)

        return outputs

class ClassifyPostprocessor(object):
    def __init__(self,config_dict):
        self.output_key=config_dict["output_key"]
        self.class_num=config_dict["class_num"]
    def call(self,inputs):
        return inputs[0]


class SigPostprocessor(object):
    def __init__(self,config_dict):
        self.sig_key=config_dict["sig_key"]
        self.max_x=config_dict["max_output_size"][1]
        self.max_y=config_dict["max_output_size"][0]
        pass
    def call(self,inputs,max_output_size,pad=(0,0)):
        batch=len(inputs[self.sig_key])
        outputs=[]
        for i in range(batch):
            ch=len(inputs[self.sig_key][i])
            output=[]
            for c in range(ch):
                output.append([])
            for x in range(int(self.max_x)):
                for y in range(int(self.max_y)):
                    max_index=0
                    max_value=-99999
                    for c in range(ch):
                        if(inputs[self.sig_key][i][c][y][x]>=max_value):
                            max_value=inputs[self.sig_key][i][c][y][x]
                            max_index=c
                    if(max_index>0):
                        print(x)
                        output[max_index-1].append((int(x*(max_output_size[1]-pad[1])/self.max_x) ,int(y*(max_output_size[0]-pad[0])/self.max_y)))
            outputs.append(output)
        return outputs
class Preprocessor(object):
    def __init__(self,config_dict):
        self.color=config_dict["color"]
        if("mean" in config_dict.keys()):
            self.mean=config_dict["mean"]
        else:
            self.mean=None
        if("std" in config_dict.keys()):
            self.std=config_dict["std"]
        else:
            self.std=None
        self.output_size=config_dict["input_size"]
        self.pad=config_dict["pad"]
    def call(self,image):
        
        input_size=image.shape
        w=0
        h=0
        resize_pad_w=0
        resize_pad_h=0
        if(input_size[0]/self.output_size[0]>input_size[1]/self.output_size[1]):
            h=self.output_size[0]
            w=int((self.output_size[0]/input_size[0])*input_size[1])
            resize_pad_w=self.output_size[1]-w
        else:
            w=self.output_size[1]
            h=int((self.output_size[1]/input_size[1])*input_size[0])
            resize_pad_h=self.output_size[0]-h
        pad_w=resize_pad_w
        pad_h=resize_pad_h
        if(self.pad>0):
            if(0<(w+pad_w)%self.pad):
                pad_w+=self.pad-((w+pad_w)%self.pad)
            if(0<(h+pad_h)%self.pad):
                pad_h+=self.pad-((h+pad_h)%self.pad)
        # print(w,h,pad_w,pad_h)
        resize_image=cv2.resize(image,(w,h))
        
        if(pad_h>0 or pad_w>0):
            resize_image= cv2.copyMakeBorder(resize_image,0,pad_h,0,pad_w,cv2.BORDER_CONSTANT)
        # print(pad_h,pad_w)
        # cv2.imwrite("test.jpg",resize_image)
        # exit(0)
        if(self.color=="gray"):
            resize_image=cv2.cvtColor(resize_image,cv2.COLOR_BGR2GRAY)
            # resize_image=cv2.GaussianBlur(resize_image,(3,3),1)
            if(self.mean!=None or self.std!=None):
                resize_image =resize_image.astype(np.float32)

            if(self.mean!=None):
                resize_image-=self.mean[0]
            if(self.std!=None):
                resize_image/=self.std[0]
            resize_image = np.expand_dims(resize_image, axis=0)
            resize_image = np.expand_dims(resize_image, axis=0)
            pass
        elif(self.color=="gray_3h"):
            if(self.mean!=None or self.std!=None):
                resize_image =resize_image.astype(np.float32)
            resize_image=cv2.cvtColor(resize_image,cv2.COLOR_BGR2GRAY)
            resize_image=cv2.cvtColor(resize_image,cv2.COLOR_GRAY2BGR)
            # resize_image =resize_image.astype(np.float32)
            resize_image = np.transpose(resize_image, (2, 0, 1)).astype(np.float32)
            if(self.mean!=None):
                resize_image[0]-=self.mean[0]
                resize_image[1]-=self.mean[1]
                resize_image[2]-=self.mean[2]
            if(self.std!=None):
                resize_image[0]/=self.std[0]
                resize_image[1]/=self.std[1]
                resize_image[2]/=self.std[2]

            resize_image = np.expand_dims(resize_image, axis=0)
            pass
        elif(self.color=="gray_hist"):
            # if(self.mean!=None or self.std!=None):
            #     resize_image =resize_image.astype(np.float32)
            resize_image=cv2.cvtColor(resize_image,cv2.COLOR_BGR2GRAY)
            
            resize_image=cv2.equalizeHist(resize_image)
            # resize_image = cv2.bilateralFilter(resize_image, 3, 9, 9)
            # cv2.imshow("resize_image",resize_image)

            # resize_image =resize_image.astype(np.float32)
            if(self.mean!=None or self.std!=None):
                resize_image =resize_image.astype(np.float32)
            if(self.mean!=None):
                resize_image-=self.mean[0]
            if(self.std!=None):
                resize_image/=self.std[0]
            resize_image = np.expand_dims(resize_image, axis=0)
            resize_image = np.expand_dims(resize_image, axis=0)
            pass
        elif(self.color=="rgb"):
            if(self.mean!=None or self.std!=None):
                resize_image =resize_image.astype(np.float32)
            resize_image=cv2.cvtColor(resize_image,cv2.COLOR_BGR2RGB)
            # resize_image =resize_image.astype(np.float32)
            resize_image = np.transpose(resize_image, (2, 0, 1)).astype(np.float32)
            if(self.mean!=None):
                resize_image[0]-=self.mean[0]
                resize_image[1]-=self.mean[1]
                resize_image[2]-=self.mean[2]
            if(self.std!=None):
                resize_image[0]/=self.std[0]
                resize_image[1]/=self.std[1]
                resize_image[2]/=self.std[2]

            resize_image = np.expand_dims(resize_image, axis=0)
            pass
        # print(resize_image.dtype)
        return (pad_h,pad_w),resize_image
class CalmcarDmsResult(object):
    def __init__(self,faceBbox):
  
        self.faceBbox=faceBbox
        # self.face_score=faceBbox[4]
        
        self.alignment_roi=[]
        self.alignment_img=[]

        self.face_score=0
        self.mask_score=0
        self.glass_score=0
        self.irblocked_score=0
        self.face_angle=[]
        self.face_keypoint=[]
        self.face_attr=[]

        self.eyes_roi=[]
        self.eyes_img=[]
        self.eyes_close_score=[]
        self.eyes_landmarks=[]

        self.phone_cig_roi=[]
        self.phone_cig_img=[]
        self.phone_point=[]
        self.cig_point=[]
        
def getRoi(faceObj,max_size,expand):
    wx=faceObj.xmax-faceObj.xmin
    wy=faceObj.ymax-faceObj.ymin
    cx=(faceObj.xmax+faceObj.xmin)/2
    cy=(faceObj.ymax+faceObj.ymin)/2
    w=max(wx,wy)*(1+expand)/2
    xmin=cx-w
    ymin=cy-w
    xmax=cx+w
    ymax=cy+w

    if(xmin<0):
        xmin=0
    if(xmin>max_size[1]):
        xmin=max_size[1]
    if(xmax<0):
        xmax=0
    if(xmax>max_size[1]):
        xmax=max_size[1]
    if(ymin<0):
        ymin=0
    if(ymin>max_size[0]):
        ymin=max_size[0]
    if(ymax<0):
        ymax=0
    if(ymax>max_size[0]):
        ymax=max_size[0]
    # print(xmin,ymin,xmax,ymax)
    return [int(xmin),int(ymin),int(xmax),int(ymax)]
def getEyeRoi(p1,p2,max_size,expand):
    cx = (p1[0] + p2[0]) / 2
    cy = (p1[1] + p2[1]) / 2
    w=max(abs(p1[0] - p2[0]),abs(p1[1] - p2[1]))*(1+expand)/2
    w=max(2,w)
    xmin=cx-w
    ymin=cy-w
    xmax=cx+w
    ymax=cy+w

    if(xmin<0):
        xmin=0
    if(xmin>max_size[1]):
        xmin=max_size[1]
    if(xmax<0):
        xmax=0
    if(xmax>max_size[1]):
        xmax=max_size[1]
    if(ymin<0):
        ymin=0
    if(ymin>max_size[0]):
        ymin=max_size[0]
    if(ymax<0):
        ymax=0
    if(ymax>max_size[0]):
        ymax=max_size[0]
    # print(xmin,ymin,xmax,ymax)
    return [int(xmin),int(ymin),int(xmax),int(ymax)]



class CalmcarDms(object):
    def __init__(self,config_path):
        config_f=open(config_path,mode='r',encoding='utf-8')
        config_json=config_f.read()
        config_f.close()
        self.config_dict = json.loads(config_json)
        
        self.face_det_model=creat_backend(self.config_dict["face_model"])
        self.face_det_preprocessor=Preprocessor(self.config_dict["face_model"]["preprocessor"])
        if(self.config_dict["face_model"]["postprocessor"]["type"]=='LabelsDets'):
            self.face_postprocessor=LabelsDetsPostprocessor(self.config_dict["face_model"]["postprocessor"])
        
        self.alignment_model=creat_backend(self.config_dict["alignment_model"])
        self.alignment_preprocessor=Preprocessor(self.config_dict["alignment_model"]["preprocessor"])
        if(self.config_dict["alignment_model"]["postprocessor"]["type"]=='Alignment'):
            self.alignment_postprocessor=FaceKeypointsDecoder(16,(192,192))

        self.phone_cig_model=creat_backend(self.config_dict["phone_cig_model"])
        self.phone_cig_preprocessor=Preprocessor(self.config_dict["phone_cig_model"]["preprocessor"])
        if(self.config_dict["phone_cig_model"]["postprocessor"]["type"]=='Sig'):
            self.phone_cig_postprocessor=SigPostprocessor(self.config_dict["phone_cig_model"]["postprocessor"])   

        self.eye_model=creat_backend(self.config_dict["eye_close_model"])
        self.eye_preprocessor=Preprocessor(self.config_dict["eye_close_model"]["preprocessor"])
        if(self.config_dict["eye_close_model"]["postprocessor"]["type"]=='classify'):
            self.eye_postprocessor=ClassifyPostprocessor(self.config_dict["eye_close_model"]["postprocessor"])      
        pass
        self.eyelandmarks_postprocessor=FaceKeypointsDecoder(16,(96,96))
    def update_result(self,image,faceObj):
        # print(faceObj)
        result=CalmcarDmsResult(faceObj)
        result.alignment_roi=getRoi(faceObj,image.shape[:-1],0.25)
        result.alignment_img=image[result.alignment_roi[1]:result.alignment_roi[3],result.alignment_roi[0]:result.alignment_roi[2]]
        pad,alignment_input=self.alignment_preprocessor.call(result.alignment_img)
        alignment_model_res=self.alignment_model.forward(alignment_input)
        result.face_keypoint=self.alignment_postprocessor.decode(alignment_model_res[0])[0]
        alignment_roi_size=np.array(result.alignment_roi[2:])-np.array(result.alignment_roi[0:2])
        alignment_input_size=np.array(alignment_input.shape)[2:]-np.array([pad[1],pad[0]])
        alignment_scale=alignment_roi_size/alignment_input_size
        result.face_keypoint[:,1:]=result.face_keypoint[:,1:]*alignment_scale

        result.face_attr=alignment_model_res[1][0]
        # result.face_keypoint[:][1:]=result.face_keypoint[:][1:]*alignment_scale
        # alignment_res=self.alignment_postprocessor.call(alignment_model_res,result.alignment_img.shape[:-1],pad)
        # # result.face_score=alignment_res[0][0]
        # # result.mask_score=alignment_res[0][1]
        # # result.glass_score=alignment_res[0][2]
        # # result.irblocked_score=alignment_res[0][3]
        # result.face_angle=alignment_res[0][0]
        # result.face_keypoint=alignment_res[0][1]
        # # print(result.face_keypoint)

        result.eyes_roi.append(getEyeRoi( result.face_keypoint[:,1:][0], result.face_keypoint[:,1:][1],result.alignment_img.shape[:-1],0.75))
        result.eyes_roi.append(getEyeRoi( result.face_keypoint[:,1:][2], result.face_keypoint[:,1:][3],result.alignment_img.shape[:-1],0.75))
        result.eyes_img.append(result.alignment_img[result.eyes_roi[0][1]:result.eyes_roi[0][3],result.eyes_roi[0][0]:result.eyes_roi[0][2]])
        result.eyes_img.append(result.alignment_img[result.eyes_roi[1][1]:result.eyes_roi[1][3],result.eyes_roi[1][0]:result.eyes_roi[1][2]])

        pad,eye_input=self.eye_preprocessor.call(result.eyes_img[0])
        eye_model_res=self.eye_model.forward(eye_input)
        eye_0_res=self.eye_postprocessor.call(eye_model_res)
        result.eyes_close_score.append(eye_0_res[0][0])
        left_eye_landmarks=self.eyelandmarks_postprocessor.decode(eye_model_res[1])[0]
        left_eye_roi_size=np.array(result.eyes_roi[0][2:])-np.array(result.eyes_roi[0][0:2])
        left_eye_input_size=np.array([96,96])
        left_eye_scale=left_eye_roi_size/left_eye_input_size
        left_eye_landmarks[:,1:]=left_eye_landmarks[:,1:]*left_eye_scale
        result.eyes_landmarks.append(left_eye_landmarks)

        pad,eye_input=self.eye_preprocessor.call(result.eyes_img[1])
        eye_model_res=self.eye_model.forward(eye_input)
        eye_1_res=self.eye_postprocessor.call(eye_model_res)
        result.eyes_close_score.append(eye_1_res[0][0])
        right_eye_landmarks=self.eyelandmarks_postprocessor.decode(eye_model_res[1])[0]
        right_eye_roi_size=np.array(result.eyes_roi[1][2:])-np.array(result.eyes_roi[1][0:2])
        right_eye_input_size=np.array([96,96])
        right_eye_scale=right_eye_roi_size/right_eye_input_size
        right_eye_landmarks[:,1:]=right_eye_landmarks[:,1:]*right_eye_scale
        result.eyes_landmarks.append(right_eye_landmarks)


        # result.phone_cig_roi=getRoi(faceObj,image.shape[:-1],1.0)
        # result.phone_cig_img=image[result.phone_cig_roi[1]:result.phone_cig_roi[3],result.phone_cig_roi[0]:result.phone_cig_roi[2]]

        # pad,phone_cig_input=self.phone_cig_preprocessor.call(result.phone_cig_img)
        # phone_cig_model_res=self.phone_cig_model.forward(phone_cig_input)
        # phone_cig_res=self.phone_cig_postprocessor.call(phone_cig_model_res,result.phone_cig_img.shape[:-1],pad)
        # result.phone_point=phone_cig_res[0][0]
        # result.cig_point=phone_cig_res[0][1]

        return result
    def run(self,image):
        results=[]
        pad,face_det_img=self.face_det_preprocessor.call(image)
        face_det_res=self.face_det_model.forward(face_det_img)
        face_objs=self.face_postprocessor.call(face_det_res,image.shape[:-1],pad)
        for obj in face_objs[0]:
            results.append(self.update_result(image,obj))
        return results


