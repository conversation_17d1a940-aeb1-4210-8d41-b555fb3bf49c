import cv2
import numpy as np
import os
import sys
import re
from typing import List, <PERSON>ple

import calmcardms
from skimage.feature import graycomatrix, graycoprops

# 常量定义
TARGET_SIZE = (96, 96)
BLOCK_SIZE = 96
GRID_SIZE = TARGET_SIZE[0]//BLOCK_SIZE#6
GAP_WIDTH = 10
SEPARATOR_HEIGHT = 10

# 支持的文件格式
IMAGE_EXTENSIONS = {'jpg', 'jpeg', 'png', 'bmp', 'webp'}
VIDEO_EXTENSIONS = {'mp4', 'ts', 'mkv', 'h264', 'avi', 'mov'}
ALL_EXTENSIONS = IMAGE_EXTENSIONS | VIDEO_EXTENSIONS

# 全局变量用于鼠标交互
eye_analysis_data = {
    'original_image': None,      # 原始眼部分析图
    'current_image': None,       # 当前显示的图像（可能包含高亮）
    'layout_info': None,         # 布局信息
    'highlight_grid': None,      # 当前高亮的方格位置
    'window_name': 'DMS Detection Result'
}


def natural_sort_key(text: str) -> List:
    """
    自然排序的键函数，正确处理数字序列
    例如：file1.jpg, file2.jpg, file10.jpg 会按正确顺序排列
    """
    return [int(c) if c.isdigit() else c.lower() for c in re.split(r'(\d+)', text)]


def find_file(directory: str, sort_by: str = 'name') -> List[Tuple[str, str]]:
    """
    查找目录中的图片和视频文件，并按指定方式排序

    Args:
        directory: 要搜索的目录路径
        sort_by: 排序方式 ('name', 'mtime', 'size')

    Returns:
        排序后的文件列表，每个元素为 (文件路径, 文件扩展名) 的元组
    """
    file_list = []

    for dirpath, _, files in os.walk(directory):
        for file in files:
            name_parts = file.split('.')
            if len(name_parts) > 1:
                extension = name_parts[-1].lower()
                if extension in ALL_EXTENSIONS:
                    full_path = os.path.join(dirpath, file)
                    file_list.append((full_path, extension))

    # 根据指定方式排序
    if sort_by == 'name':
        file_list.sort(key=lambda x: natural_sort_key(os.path.basename(x[0])))
    elif sort_by == 'mtime':
        file_list.sort(key=lambda x: os.path.getmtime(x[0]))
    elif sort_by == 'size':
        file_list.sort(key=lambda x: os.path.getsize(x[0]))

    return file_list



def draw_face_landmarks(frame, result):
    """绘制人脸关键点"""
    # 绘制人脸边界框
    cv2.rectangle(frame, (result.faceBbox.xmin, result.faceBbox.ymin),
                 (result.faceBbox.xmax, result.faceBbox.ymax), (0, 255, 0), 2)

    # 绘制人脸关键点
    for i in range(8):
        point = result.face_keypoint[i + 4]
        cv2.circle(frame, (int(point[1] + result.alignment_roi[0]),
                          int(point[2] + result.alignment_roi[1])), 3, (0, 0, 255), -1)

    for i in range(len(result.face_keypoint) - 16):
        point = result.face_keypoint[i + 16]
        cv2.circle(frame, (int(point[1] + result.alignment_roi[0]),
                          int(point[2] + result.alignment_roi[1])), 3, (0, 0, 255), -1)


def draw_eye_landmarks(frame, result, eye_index):
    """
    绘制单个眼部的关键点

    Args:
        frame: 图像帧
        result: 检测结果
        eye_index: 眼部索引 (0=左眼, 1=右眼)
    """
    eye_landmarks = result.eyes_landmarks[eye_index]
    eye_roi = result.eyes_roi[eye_index]
    alignment_roi = result.alignment_roi

    # 绘制眼部轮廓点 (前8个点)
    for i in range(8):
        point = eye_landmarks[i]
        if point[0] > 0.5:
            x = int(point[1] + eye_roi[0] + alignment_roi[0])
            y = int(point[2] + eye_roi[1] + alignment_roi[1])
            cv2.circle(frame, (x, y), 1, (0, 0, 255), -1)

    # 绘制眼部内部点 (第9-16个点)
    for i in range(8):
        point = eye_landmarks[8 + i]
        if point[0] > 0.5:
            x = int(point[1] + eye_roi[0] + alignment_roi[0])
            y = int(point[2] + eye_roi[1] + alignment_roi[1])
            cv2.circle(frame, (x, y), 1, (255, 0, 0), -1)

    # 绘制瞳孔点 (第17-20个点)
    for i in range(4):
        point = eye_landmarks[16 + i]
        if point[0] > 0.5:
            x = int(point[1] + eye_roi[0] + alignment_roi[0])
            y = int(point[2] + eye_roi[1] + alignment_roi[1])
            cv2.circle(frame, (x, y), 1, (255, 255, 0), -1)


def draw_eye_status_rectangle(frame, result, eye_index):
    """
    根据眼部状态绘制不同颜色的矩形框

    Args:
        frame: 图像帧
        result: 检测结果
        eye_index: 眼部索引 (0=左眼, 1=右眼)
    """
    eye_roi = result.eyes_roi[eye_index]
    alignment_roi = result.alignment_roi

    # 计算眼部区域坐标
    x1 = alignment_roi[0] + eye_roi[0]
    y1 = alignment_roi[1] + eye_roi[1]
    x2 = alignment_roi[0] + eye_roi[2]
    y2 = alignment_roi[1] + eye_roi[3]

    # 根据眼部大小和闭合状态选择颜色
    eye_width = eye_roi[2] - eye_roi[0]
    if eye_width < 24:
        color = (255, 255, 0)  # 黄色：眼部区域太小
    elif result.eyes_close_score[eye_index] <= 0.9:
        color = (0, 255, 0)    # 绿色：眼部睁开
    else:
        color = (255, 0, 0)    # 红色：眼部闭合

    cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)


def draw_result(results, frame):
    """
    在图像上绘制检测结果

    Args:
        results: DMS检测结果列表
        frame: 输入图像帧

    Returns:
        绘制了检测结果的图像帧
    """
    for result in results:
        # 绘制人脸关键点
        draw_face_landmarks(frame, result)

        # 绘制双眼关键点
        draw_eye_landmarks(frame, result, 0)  # 左眼
        draw_eye_landmarks(frame, result, 1)  # 右眼

        # 绘制眼部状态矩形框
        draw_eye_status_rectangle(frame, result, 0)  # 左眼
        draw_eye_status_rectangle(frame, result, 1)  # 右眼

    return frame

# def process_eye_image(eye_image):
#     # 转换为灰度图
#     gray_eye = cv2.cvtColor(eye_image, cv2.COLOR_BGR2GRAY)
#     gray_eye = cv2.medianBlur(gray_eye, 3)
#     # 调整尺寸为 96x96
#     gray_eye = (cv2.resize(gray_eye, TARGET_SIZE)/16).astype(np.uint8)
#     color_map = np.zeros((96, 96, 4), dtype=np.uint8)

#     # 由于是 16x16 小格，循环次数变为 6 次
#     for i in range(GRID_SIZE):
#         for j in range(GRID_SIZE):
#             # 分割 16x16 小块
#             block = gray_eye[i*BLOCK_SIZE:(i+1)*BLOCK_SIZE, j*BLOCK_SIZE:(j+1)*BLOCK_SIZE]
#             # 计算灰度共生矩阵，使用 0 度、45 度、90 度、135 度，距离 d 为 1
#             glcm = graycomatrix(block, distances=[1], angles=[0, np.pi/2], levels=16, symmetric=True, normed=True)
#             # 计算对比度、熵、能量、相关性，取 4 个方向的平均值
#             contrast = np.mean(graycoprops(glcm, 'contrast'))
#             energy = np.mean(graycoprops(glcm, 'energy'))
#             homogeneity = np.mean(graycoprops(glcm, 'homogeneity'))
#             correlation = np.mean(graycoprops(glcm, 'correlation'))

#             # 能量高表示纹理丰富，用 R 通道表示
#             a = int(np.clip(energy * 255, 0, 255))
#             # 调整映射逻辑，更突出镜片倒影特征
#             # 对比度高通常表示有明显的倒影边缘，用 A 通道（透明度）表示
#             r = int(np.clip(contrast * 510, 0, 255))  # 放大对比度映射范围
#             # 同质性低可能意味着有复杂的倒影，用 G 通道表示
#             g = int(np.clip((1 - homogeneity) * 255, 0, 255))
#             # 相关性低表示纹理不规则，用 B 通道表示
#             b = int(np.clip((1 - correlation) * 255, 0, 255))

#             # 填充颜色图
#             color_map[i*BLOCK_SIZE:(i+1)*BLOCK_SIZE, j*BLOCK_SIZE:(j+1)*BLOCK_SIZE, 0] = 255
#             color_map[i*BLOCK_SIZE:(i+1)*BLOCK_SIZE, j*BLOCK_SIZE:(j+1)*BLOCK_SIZE, 1] = r#(r > 150) * 255#0
#             color_map[i*BLOCK_SIZE:(i+1)*BLOCK_SIZE, j*BLOCK_SIZE:(j+1)*BLOCK_SIZE, 2] = 0#(g > 60) * 255
#             color_map[i*BLOCK_SIZE:(i+1)*BLOCK_SIZE, j*BLOCK_SIZE:(j+1)*BLOCK_SIZE, 3] = 0
#     return color_map

def process_eye_image(eye_image, eye_label="", landmarks=None):
    """
    处理眼部图像，使用改进的反光检测算法，支持眼镜区域掩码

    Args:
        eye_image: 眼部图像
        eye_label: 眼部标识（如"左眼"、"右眼"），用于调试输出
        landmarks: 眼部轮廓关键点，用于创建眼镜检测掩码
    """
    # 转换为灰度图
    gray_eye = cv2.cvtColor(eye_image, cv2.COLOR_BGR2GRAY)
    # 调整尺寸为 96x96
    gray_eye = cv2.resize(gray_eye, TARGET_SIZE).astype(np.uint8)
    color_map = np.zeros((96, 96, 4), dtype=np.uint8)

    # 创建眼镜检测掩码
    detection_mask = None
    if landmarks and len(landmarks) >= 4:
        # 将关键点坐标缩放到96x96
        original_size = eye_image.shape[:2]
        scale_x = TARGET_SIZE[0] / original_size[1]
        scale_y = TARGET_SIZE[1] / original_size[0]

        scaled_landmarks = []
        for x, y in landmarks:
            scaled_x = x * scale_x
            scaled_y = y * scale_y
            scaled_landmarks.append((scaled_x, scaled_y))

        detection_mask = create_glasses_detection_mask(gray_eye, scaled_landmarks)

        if eye_label:
            mask_coverage = np.sum(detection_mask) / detection_mask.size
            print(f"{eye_label} - 眼镜检测掩码覆盖率: {mask_coverage:.3f}")

    # 由于是 96x96 小格，循环次数为 1 次 (GRID_SIZE=1, BLOCK_SIZE=96)
    for i in range(GRID_SIZE):
        for j in range(GRID_SIZE):
            # 分割块
            block = gray_eye[i*BLOCK_SIZE:(i+1)*BLOCK_SIZE, j*BLOCK_SIZE:(j+1)*BLOCK_SIZE]

            # 获取对应的掩码块
            mask_block = None
            if detection_mask is not None:
                mask_block = detection_mask[i*BLOCK_SIZE:(i+1)*BLOCK_SIZE, j*BLOCK_SIZE:(j+1)*BLOCK_SIZE]

            # 改进的反光检测算法，使用掩码
            reflection_features = detect_glass_reflection(block, eye_label, mask_block)

            # 提取各个特征值
            brightness_score = reflection_features['brightness_score']
            edge_score = reflection_features['edge_score']
            variance_score = reflection_features['variance_score']
            reflection_confidence = reflection_features['reflection_confidence']

            # 详细调试输出，包含眼部标识
            if eye_label:
                print(f"\n=== {eye_label} - 块({i},{j}) 详细分析（掩码检测）===")
                print(f"反光置信度: {reflection_confidence:.3f}")
                print(f"亮度评分: {brightness_score:.2f}")
                print(f"边缘评分: {edge_score:.2f}")
                print(f"方差评分: {variance_score:.2f}")
                print(f"高亮像素比例: {reflection_features['high_brightness_ratio']:.3f}")
                print(f"局部方差: {reflection_features['local_variance']:.2f}")
                print(f"边缘阈值: {reflection_features.get('edge_threshold', 0):.2f}")
                print(f"强边缘像素数: {reflection_features.get('strong_edge_count', 0)}")
                print(f"亮度集中度: {reflection_features.get('brightness_concentration', 0):.3f}")
                print(f"掩码覆盖率: {reflection_features.get('mask_coverage', 0):.3f}")
                print("=" * 50)

            # 创建可视化的掩码显示
            # 基础颜色图：反光置信度
            base_confidence = int(np.clip(reflection_confidence * 255, 0, 255))

            # 填充颜色图 - 改进的可视化方案
            color_map[i*BLOCK_SIZE:(i+1)*BLOCK_SIZE, j*BLOCK_SIZE:(j+1)*BLOCK_SIZE, 0] = 255

            if detection_mask is not None:
                # 有掩码的情况：显示掩码区域和检测结果
                mask_region = mask_block > 0
                non_mask_region = mask_block == 0

                # 掩码区域：显示反光检测结果
                color_map[i*BLOCK_SIZE:(i+1)*BLOCK_SIZE, j*BLOCK_SIZE:(j+1)*BLOCK_SIZE, 1][mask_region] = base_confidence  # R通道：反光置信度
                color_map[i*BLOCK_SIZE:(i+1)*BLOCK_SIZE, j*BLOCK_SIZE:(j+1)*BLOCK_SIZE, 2][mask_region] = int(np.clip(edge_score, 0, 255))  # G通道：边缘强度
                color_map[i*BLOCK_SIZE:(i+1)*BLOCK_SIZE, j*BLOCK_SIZE:(j+1)*BLOCK_SIZE, 3][mask_region] = int(np.clip(brightness_score, 0, 255))  # B通道：亮度信息

                # 非掩码区域：显示为暗蓝色表示被忽略的区域
                color_map[i*BLOCK_SIZE:(i+1)*BLOCK_SIZE, j*BLOCK_SIZE:(j+1)*BLOCK_SIZE, 1][non_mask_region] = 0   # R通道：0
                color_map[i*BLOCK_SIZE:(i+1)*BLOCK_SIZE, j*BLOCK_SIZE:(j+1)*BLOCK_SIZE, 2][non_mask_region] = 0   # G通道：0
                color_map[i*BLOCK_SIZE:(i+1)*BLOCK_SIZE, j*BLOCK_SIZE:(j+1)*BLOCK_SIZE, 3][non_mask_region] = 100 # B通道：暗蓝色
            else:
                # 无掩码的情况：全图显示
                color_map[i*BLOCK_SIZE:(i+1)*BLOCK_SIZE, j*BLOCK_SIZE:(j+1)*BLOCK_SIZE, 1] = base_confidence
                color_map[i*BLOCK_SIZE:(i+1)*BLOCK_SIZE, j*BLOCK_SIZE:(j+1)*BLOCK_SIZE, 2] = int(np.clip(edge_score, 0, 255))
                color_map[i*BLOCK_SIZE:(i+1)*BLOCK_SIZE, j*BLOCK_SIZE:(j+1)*BLOCK_SIZE, 3] = int(np.clip(brightness_score, 0, 255))

    return color_map


# 全局参数配置 - 方便调整
REFLECTION_PARAMS = {
    'brightness_threshold_high': 160,  # 调整亮度阈值
    'edge_percentile': 35,             # 边缘分位数
    'optimal_variance': 1000,          # 最优方差值
    'brightness_weight': 0.45,         # 亮度权重
    'edge_weight': 0.2,                # 边缘权重
    'variance_weight': 0.1,            # 方差权重
    'concentration_weight': 0.25       # 大幅提高亮度集中度权重（关键区分特征）
}

def print_current_params():
    """打印当前参数设置"""
    print("\n=== 当前反光检测参数 ===")
    for key, value in REFLECTION_PARAMS.items():
        print(f"{key}: {value}")
    print("=" * 30)

def detect_glass_reflection(gray_block, eye_label="", detection_mask=None):
    """
    改进的眼镜反光检测算法，基于多特征融合，支持掩码检测

    Args:
        gray_block: 灰度图像块 (96x96)
        eye_label: 眼部标识，用于调试（暂未使用）
        detection_mask: 检测掩码，1表示需要检测的区域，0表示忽略的区域

    Returns:
        dict: 包含各种特征评分的字典
    """
    # 使用全局参数
    brightness_threshold_high = REFLECTION_PARAMS['brightness_threshold_high']

    # 如果有掩码，只分析掩码区域
    if detection_mask is not None:
        # 确保掩码尺寸匹配
        if detection_mask.shape != gray_block.shape:
            detection_mask = cv2.resize(detection_mask, gray_block.shape[::-1])

        # 只分析掩码区域的像素
        masked_pixels = gray_block[detection_mask > 0]
        if len(masked_pixels) == 0:
            # 如果掩码区域为空，返回零评分
            return {
                'brightness_score': 0,
                'edge_score': 0,
                'variance_score': 0,
                'reflection_confidence': 0,
                'high_brightness_ratio': 0,
                'local_variance': 0,
                'edge_threshold': 0,
                'strong_edge_count': 0,
                'brightness_concentration': 0,
                'total_pixels': 0,
                'high_brightness_pixels': 0,
                'brightness_threshold': brightness_threshold_high,
                'optimal_variance': REFLECTION_PARAMS['optimal_variance'],
                'mask_coverage': 0
            }

        # 使用掩码区域进行分析
        analysis_region = masked_pixels
        total_pixels = len(masked_pixels)
    else:
        # 使用整个图像块
        analysis_region = gray_block.flat
        total_pixels = gray_block.size

    # 1. 亮度特征分析
    if detection_mask is not None:
        # 使用掩码区域分析
        high_brightness_pixels = np.sum(masked_pixels > brightness_threshold_high)
        mask_coverage = np.sum(detection_mask) / detection_mask.size
    else:
        # 使用整个图像块
        high_brightness_pixels = np.sum(gray_block > brightness_threshold_high)
        mask_coverage = 1.0

    # 亮度评分：高亮像素比例
    brightness_ratio = high_brightness_pixels / total_pixels if total_pixels > 0 else 0
    brightness_score = brightness_ratio * 255

    # 2. 边缘强度检测
    # 使用Sobel算子检测边缘
    sobel_x = cv2.Sobel(gray_block, cv2.CV_64F, 1, 0, ksize=3)
    sobel_y = cv2.Sobel(gray_block, cv2.CV_64F, 0, 1, ksize=3)
    edge_magnitude = np.sqrt(sobel_x**2 + sobel_y**2)

    # 如果有掩码，只分析掩码区域的边缘
    if detection_mask is not None:
        masked_edges = edge_magnitude[detection_mask > 0]
        if len(masked_edges) > 0:
            edge_threshold = np.percentile(masked_edges, REFLECTION_PARAMS['edge_percentile'])
            strong_edges_mask = (edge_magnitude > edge_threshold) & (detection_mask > 0)
            strong_edge_count = np.sum(strong_edges_mask)
            edge_score = np.mean(edge_magnitude[strong_edges_mask]) if strong_edge_count > 0 else 0
        else:
            edge_threshold = 0
            strong_edge_count = 0
            edge_score = 0
    else:
        # 使用整个图像块
        edge_threshold = np.percentile(edge_magnitude, REFLECTION_PARAMS['edge_percentile'])
        strong_edges = edge_magnitude > edge_threshold
        strong_edge_count = np.sum(strong_edges)
        edge_score = np.mean(edge_magnitude[strong_edges]) if np.any(strong_edges) else 0

    edge_score = np.clip(edge_score, 0, 255)

    # 3. 局部方差分析
    # 计算局部方差，反光区域内部应该相对平滑
    if detection_mask is not None and len(masked_pixels) > 0:
        local_variance = np.var(masked_pixels)
    else:
        local_variance = np.var(gray_block)

    # 方差评分：改进的计算方式，更加稳定
    # 使用相对方差评分，避免绝对值差异过大的问题
    optimal_variance = REFLECTION_PARAMS['optimal_variance']

    # 使用更宽松的高斯函数，标准差设为optimal_variance的50%
    sigma = optimal_variance * 0.5
    variance_score = 255 * np.exp(-((local_variance - optimal_variance) ** 2) / (2 * (sigma ** 2)))

    # 如果方差在合理范围内，给予基础分数
    if 500 <= local_variance <= 5000:  # 经验范围
        variance_score = max(variance_score, 128)  # 至少给50%的分数

    # 4. 亮度分布特征
    # 分析亮度直方图，反光区域应该在高亮度段有集中分布
    if detection_mask is not None and len(masked_pixels) > 0:
        hist = np.histogram(masked_pixels, bins=32, range=(0, 256))[0]
    else:
        hist = np.histogram(gray_block, bins=32, range=(0, 256))[0]

    high_brightness_bins = hist[24:]  # 高亮度段（192-255）
    total_hist = np.sum(hist)
    high_brightness_concentration = np.sum(high_brightness_bins) / total_hist if total_hist > 0 else 0

    # 5. 综合反光置信度计算
    # 使用加权融合多个特征
    weights = {
        'brightness': REFLECTION_PARAMS['brightness_weight'],
        'edge': REFLECTION_PARAMS['edge_weight'],
        'variance': REFLECTION_PARAMS['variance_weight'],
        'concentration': REFLECTION_PARAMS['concentration_weight']
    }

    # 归一化各个特征到0-1范围
    brightness_norm = brightness_ratio
    edge_norm = edge_score / 255.0
    variance_norm = variance_score / 255.0
    concentration_norm = high_brightness_concentration

    # 计算基础置信度
    base_confidence = (
        weights['brightness'] * brightness_norm +
        weights['edge'] * edge_norm +
        weights['variance'] * variance_norm +
        weights['concentration'] * concentration_norm
    )

    # 增强因子：基于多个特征的智能调整
    enhancement_factor = 1.0

    if detection_mask is not None:
        # 掩码检测基础加成
        enhancement_factor += 0.2

        # 亮度集中度加成：这是区分真假反光的关键特征
        if high_brightness_concentration > 0.15:  # 左眼0.179，右眼0.016
            enhancement_factor += 0.4  # 大幅加成
        elif high_brightness_concentration > 0.08:
            enhancement_factor += 0.2  # 中等加成
        else:
            enhancement_factor -= 0.1  # 集中度太低，减分

        # 高亮度比例加成
        if brightness_ratio > 0.4:
            enhancement_factor += 0.15
        elif brightness_ratio > 0.25:
            enhancement_factor += 0.05

        # 边缘强度加成
        if edge_score > 115:
            enhancement_factor += 0.1

    # 应用增强因子，但限制在合理范围内
    reflection_confidence = max(0.0, min(base_confidence * enhancement_factor, 1.0))

    return {
        'brightness_score': brightness_score,
        'edge_score': edge_score,
        'variance_score': variance_score,
        'reflection_confidence': reflection_confidence,
        'high_brightness_ratio': brightness_ratio,
        'local_variance': local_variance,
        'edge_threshold': edge_threshold,
        'strong_edge_count': strong_edge_count,
        'brightness_concentration': high_brightness_concentration,
        'total_pixels': total_pixels,
        'high_brightness_pixels': high_brightness_pixels,
        'brightness_threshold': brightness_threshold_high,
        'optimal_variance': optimal_variance,
        'mask_coverage': mask_coverage
    }

def extract_eye_regions_with_landmarks(frame, result):
    """
    从检测结果中提取左右眼区域和关键点信息

    Args:
        frame: 输入图像帧
        result: DMS检测结果

    Returns:
        tuple: (left_eye_data, right_eye_data) 包含图像和关键点信息的字典，如果提取失败则为None
    """
    try:
        eye_data = []

        for eye_index in range(2):  # 0=左眼, 1=右眼
            # 提取眼部区域坐标
            eye_x1 = result.alignment_roi[0] + result.eyes_roi[eye_index][0]
            eye_y1 = result.alignment_roi[1] + result.eyes_roi[eye_index][1]
            eye_x2 = result.alignment_roi[0] + result.eyes_roi[eye_index][2]
            eye_y2 = result.alignment_roi[1] + result.eyes_roi[eye_index][3]
            eye_image = frame[eye_y1:eye_y2, eye_x1:eye_x2]

            # 提取眼部关键点（相对于眼部ROI的坐标）
            eye_landmarks = result.eyes_landmarks[eye_index]
            eye_roi = result.eyes_roi[eye_index]

            # 转换关键点坐标为相对于眼部图像的坐标
            relative_landmarks = []
            for i in range(8):  # 只使用前8个轮廓点
                point = eye_landmarks[i]
                if point[0] > 0.5:  # 置信度检查
                    rel_x = point[1]  # 已经是相对于eye_roi的坐标
                    rel_y = point[2]
                    relative_landmarks.append((rel_x, rel_y))

            eye_data.append({
                'image': eye_image,
                'landmarks': relative_landmarks,
                'roi': eye_roi,
                'global_offset': (eye_x1, eye_y1)
            })

        return eye_data[0], eye_data[1]
    except Exception as e:
        print(f"提取眼部区域和关键点时出错: {e}")
        return None, None


def extract_eye_regions(frame, result):
    """
    保持向后兼容的眼部区域提取函数
    """
    left_data, right_data = extract_eye_regions_with_landmarks(frame, result)
    left_eye = left_data['image'] if left_data else None
    right_eye = right_data['image'] if right_data else None
    return left_eye, right_eye


def create_glasses_detection_mask(eye_image, landmarks, expand_factor=1.8):
    """
    创建眼镜检测掩码，只检测可能有眼镜镜片的区域

    Args:
        eye_image: 眼部图像
        landmarks: 眼部轮廓关键点列表 [(x, y), ...]
        expand_factor: 扩展倍数，默认1.8倍

    Returns:
        mask: 检测掩码，1表示需要检测的区域，0表示忽略的区域
    """
    if len(landmarks) < 4:  # 至少需要4个点来构建轮廓
        # 如果关键点不足，返回全图掩码
        return np.ones(eye_image.shape[:2], dtype=np.uint8)

    h, w = eye_image.shape[:2]

    # 创建眼部轮廓掩码
    eye_contour = np.array(landmarks, dtype=np.int32)
    eye_mask = np.zeros((h, w), dtype=np.uint8)
    cv2.fillPoly(eye_mask, [eye_contour], 1)

    # 计算眼部中心点
    center_x = np.mean([pt[0] for pt in landmarks])
    center_y = np.mean([pt[1] for pt in landmarks])

    # 创建扩展的检测区域
    expanded_landmarks = []
    for x, y in landmarks:
        # 从中心点向外扩展
        new_x = center_x + (x - center_x) * expand_factor
        new_y = center_y + (y - center_y) * expand_factor
        # 确保坐标在图像范围内
        new_x = max(0, min(w-1, new_x))
        new_y = max(0, min(h-1, new_y))
        expanded_landmarks.append((new_x, new_y))

    # 创建扩展区域掩码
    expanded_contour = np.array(expanded_landmarks, dtype=np.int32)
    expanded_mask = np.zeros((h, w), dtype=np.uint8)
    cv2.fillPoly(expanded_mask, [expanded_contour], 1)

    # 检测掩码 = 扩展区域 - 眼部区域（只检测眼镜镜片可能的区域）
    detection_mask = expanded_mask - eye_mask
    detection_mask = np.clip(detection_mask, 0, 1)

    return detection_mask


def create_eye_analysis_display(left_eye, right_eye):
    """
    创建眼部分析显示图像，包含原图和纹理特征图

    Args:
        left_eye: 左眼图像
        right_eye: 右眼图像

    Returns:
        tuple: (最终拼接的显示图像, 布局信息字典)
    """
    # 创建间隔和分隔条
    img_gap = np.zeros((TARGET_SIZE[1], GAP_WIDTH, 3), dtype=np.uint8)
    color_gap = np.zeros((TARGET_SIZE[1], GAP_WIDTH, 4), dtype=np.uint8)

    # 布局信息
    layout_info = {
        'has_left_eye': False,
        'has_right_eye': False,
        'eye_width': TARGET_SIZE[0],
        'eye_height': TARGET_SIZE[1],
        'gap_width': GAP_WIDTH,
        'separator_height': SEPARATOR_HEIGHT,
        'grid_size': GRID_SIZE,
        'block_size': BLOCK_SIZE
    }

    if left_eye is not None and left_eye.size > 0 and right_eye is not None and right_eye.size > 0:
        # 双眼都存在的情况
        left_eye_resized = cv2.resize(left_eye, TARGET_SIZE)
        right_eye_resized = cv2.resize(right_eye, TARGET_SIZE)

        left_color_map = process_eye_image(left_eye, "左眼")
        right_color_map = process_eye_image(right_eye, "右眼")

        # 水平拼接原图
        combined_eyes = cv2.hconcat([left_eye_resized, img_gap, right_eye_resized])
        # 水平拼接颜色图
        combined_color_map = np.hstack([left_color_map, color_gap, right_color_map])

        layout_info['has_left_eye'] = True
        layout_info['has_right_eye'] = True
        layout_info['total_width'] = TARGET_SIZE[0] * 2 + GAP_WIDTH

    elif left_eye is not None and left_eye.size > 0:
        # 只有左眼的情况
        left_eye_resized = cv2.resize(left_eye, TARGET_SIZE)
        left_color_map = process_eye_image(left_eye, "左眼")

        combined_eyes = left_eye_resized
        combined_color_map = left_color_map

        layout_info['has_left_eye'] = True
        layout_info['total_width'] = TARGET_SIZE[0]

    elif right_eye is not None and right_eye.size > 0:
        # 只有右眼的情况
        right_eye_resized = cv2.resize(right_eye, TARGET_SIZE)
        right_color_map = process_eye_image(right_eye, "右眼")

        combined_eyes = right_eye_resized
        combined_color_map = right_color_map

        layout_info['has_right_eye'] = True
        layout_info['total_width'] = TARGET_SIZE[0]

    else:
        return None, None

    # 转换颜色图为3通道以便拼接
    combined_color_map_rgb = combined_color_map[..., 1:]

    # 创建分隔条
    separator = np.zeros((SEPARATOR_HEIGHT, combined_eyes.shape[1], 3), dtype=np.uint8)

    # 垂直拼接
    final_image = np.vstack([combined_eyes, separator, combined_color_map_rgb])

    # 计算各部分在最终图像中的位置
    layout_info['eyes_region'] = (0, 0, combined_eyes.shape[1], combined_eyes.shape[0])
    layout_info['separator_region'] = (0, combined_eyes.shape[0], combined_eyes.shape[1], SEPARATOR_HEIGHT)
    layout_info['texture_region'] = (0, combined_eyes.shape[0] + SEPARATOR_HEIGHT,
                                   combined_color_map_rgb.shape[1], combined_color_map_rgb.shape[0])

    return final_image, layout_info


def create_eye_analysis_display_with_mask(left_eye, right_eye, left_landmarks=None, right_landmarks=None):
    """
    创建眼部分析显示图像，使用眼镜检测掩码

    Args:
        left_eye: 左眼图像
        right_eye: 右眼图像
        left_landmarks: 左眼关键点
        right_landmarks: 右眼关键点

    Returns:
        tuple: (最终拼接的显示图像, 布局信息字典)
    """
    # 创建间隔和分隔条
    img_gap = np.zeros((TARGET_SIZE[1], GAP_WIDTH, 3), dtype=np.uint8)
    color_gap = np.zeros((TARGET_SIZE[1], GAP_WIDTH, 4), dtype=np.uint8)

    # 布局信息
    layout_info = {
        'has_left_eye': False,
        'has_right_eye': False,
        'eye_width': TARGET_SIZE[0],
        'eye_height': TARGET_SIZE[1],
        'gap_width': GAP_WIDTH,
        'separator_height': SEPARATOR_HEIGHT,
        'grid_size': GRID_SIZE,
        'block_size': BLOCK_SIZE
    }

    if left_eye is not None and left_eye.size > 0 and right_eye is not None and right_eye.size > 0:
        # 双眼都存在的情况
        left_eye_resized = cv2.resize(left_eye, TARGET_SIZE)
        right_eye_resized = cv2.resize(right_eye, TARGET_SIZE)

        left_color_map = process_eye_image(left_eye, "左眼", left_landmarks)
        right_color_map = process_eye_image(right_eye, "右眼", right_landmarks)

        # 水平拼接原图
        combined_eyes = cv2.hconcat([left_eye_resized, img_gap, right_eye_resized])
        # 水平拼接颜色图
        combined_color_map = np.hstack([left_color_map, color_gap, right_color_map])

        layout_info['has_left_eye'] = True
        layout_info['has_right_eye'] = True
        layout_info['total_width'] = TARGET_SIZE[0] * 2 + GAP_WIDTH

    elif left_eye is not None and left_eye.size > 0:
        # 只有左眼的情况
        left_eye_resized = cv2.resize(left_eye, TARGET_SIZE)
        left_color_map = process_eye_image(left_eye, "左眼", left_landmarks)

        combined_eyes = left_eye_resized
        combined_color_map = left_color_map

        layout_info['has_left_eye'] = True
        layout_info['total_width'] = TARGET_SIZE[0]

    elif right_eye is not None and right_eye.size > 0:
        # 只有右眼的情况
        right_eye_resized = cv2.resize(right_eye, TARGET_SIZE)
        right_color_map = process_eye_image(right_eye, "右眼", right_landmarks)

        combined_eyes = right_eye_resized
        combined_color_map = right_color_map

        layout_info['has_right_eye'] = True
        layout_info['total_width'] = TARGET_SIZE[0]

    else:
        return None, None

    # 转换颜色图为3通道以便拼接
    combined_color_map_rgb = combined_color_map[..., 1:]

    # 创建分隔条
    separator = np.zeros((SEPARATOR_HEIGHT, combined_eyes.shape[1], 3), dtype=np.uint8)

    # 垂直拼接
    final_image = np.vstack([combined_eyes, separator, combined_color_map_rgb])

    # 计算各部分在最终图像中的位置
    layout_info['eyes_region'] = (0, 0, combined_eyes.shape[1], combined_eyes.shape[0])
    layout_info['separator_region'] = (0, combined_eyes.shape[0], combined_eyes.shape[1], SEPARATOR_HEIGHT)
    layout_info['texture_region'] = (0, combined_eyes.shape[0] + SEPARATOR_HEIGHT,
                                   combined_color_map_rgb.shape[1], combined_color_map_rgb.shape[0])

    return final_image, layout_info


def mouse_callback(event, x, y, flags, param):
    """
    鼠标回调函数，处理眼部分析图的交互

    Args:
        event: 鼠标事件类型
        x, y: 鼠标坐标
        flags: 鼠标标志
        param: 传递的参数
    """
    global eye_analysis_data

    if eye_analysis_data['original_image'] is None or eye_analysis_data['layout_info'] is None:
        return

    if event == cv2.EVENT_MOUSEMOVE:
        # 处理鼠标移动事件
        handle_mouse_hover(x, y)


def handle_mouse_hover(mouse_x, mouse_y):
    """
    处理鼠标悬浮事件

    Args:
        mouse_x, mouse_y: 鼠标在主窗口中的坐标
    """
    global eye_analysis_data

    layout = eye_analysis_data['layout_info']
    if layout is None:
        return

    # 检查鼠标是否在眼部分析图区域内（叠加在主图像左上角）
    overlay_x, overlay_y = 10, 10  # 叠加位置
    analysis_width = layout['total_width']
    analysis_height = layout['eye_height'] + layout['separator_height'] + layout['eye_height']

    # 转换为眼部分析图内的相对坐标
    rel_x = mouse_x - overlay_x
    rel_y = mouse_y - overlay_y

    # 检查是否在眼部分析图范围内
    if rel_x < 0 or rel_x >= analysis_width or rel_y < 0 or rel_y >= analysis_height:
        # 鼠标不在眼部分析图区域，清除高亮
        if eye_analysis_data['highlight_grid'] is not None:
            eye_analysis_data['highlight_grid'] = None
            update_eye_analysis_display()
        return

    # 检查是否在下半部分纹理图区域
    texture_start_y = layout['eye_height'] + layout['separator_height']
    if rel_y < texture_start_y:
        # 鼠标在上半部分或分隔条，清除高亮
        if eye_analysis_data['highlight_grid'] is not None:
            eye_analysis_data['highlight_grid'] = None
            update_eye_analysis_display()
        return

    # 计算在纹理图中的位置
    texture_y = rel_y - texture_start_y

    # 确定是左眼还是右眼区域
    eye_region = None
    grid_x_offset = 0

    if layout['has_left_eye'] and layout['has_right_eye']:
        # 双眼情况
        if rel_x < layout['eye_width']:
            eye_region = 'left'
            grid_x_offset = 0
        elif rel_x >= layout['eye_width'] + layout['gap_width']:
            eye_region = 'right'
            grid_x_offset = layout['eye_width'] + layout['gap_width']
        else:
            # 在间隔区域，清除高亮
            if eye_analysis_data['highlight_grid'] is not None:
                eye_analysis_data['highlight_grid'] = None
                update_eye_analysis_display()
            return
    elif layout['has_left_eye']:
        eye_region = 'left'
        grid_x_offset = 0
    elif layout['has_right_eye']:
        eye_region = 'right'
        grid_x_offset = 0

    # 计算方格坐标
    grid_x_in_eye = rel_x - grid_x_offset
    grid_i = int(texture_y // layout['block_size'])
    grid_j = int(grid_x_in_eye // layout['block_size'])

    # 检查方格坐标是否有效
    if 0 <= grid_i < layout['grid_size'] and 0 <= grid_j < layout['grid_size']:
        new_highlight = (eye_region, grid_i, grid_j)
        if eye_analysis_data['highlight_grid'] != new_highlight:
            eye_analysis_data['highlight_grid'] = new_highlight
            update_eye_analysis_display()
    else:
        # 方格坐标无效，清除高亮
        if eye_analysis_data['highlight_grid'] is not None:
            eye_analysis_data['highlight_grid'] = None
            update_eye_analysis_display()


def update_eye_analysis_display():
    """
    更新眼部分析图显示，添加或移除高亮效果
    """
    global eye_analysis_data

    if eye_analysis_data['original_image'] is None:
        return

    # 复制原始图像
    current_image = eye_analysis_data['original_image'].copy()

    # 如果有高亮方格，绘制高亮效果
    if eye_analysis_data['highlight_grid'] is not None:
        eye_region, grid_i, grid_j = eye_analysis_data['highlight_grid']
        layout = eye_analysis_data['layout_info']

        # 计算上半部分对应区域的坐标
        if eye_region == 'left':
            start_x = grid_j * layout['block_size']
        else:  # right eye
            if layout['has_left_eye'] and layout['has_right_eye']:
                start_x = layout['eye_width'] + layout['gap_width'] + grid_j * layout['block_size']
            else:
                start_x = grid_j * layout['block_size']

        start_y = grid_i * layout['block_size']
        end_x = start_x + layout['block_size']
        end_y = start_y + layout['block_size']

        # 在上半部分绘制高亮边框
        cv2.rectangle(current_image, (start_x, start_y), (end_x, end_y), (0, 255, 255), 2)

        # 可选：添加半透明覆盖
        overlay = current_image.copy()
        cv2.rectangle(overlay, (start_x, start_y), (end_x, end_y), (0, 255, 255), -1)
        cv2.addWeighted(current_image, 0.8, overlay, 0.2, 0, current_image)

    eye_analysis_data['current_image'] = current_image

    # 立即更新显示
    refresh_main_display()


def refresh_main_display():
    """
    刷新主显示窗口，显示更新后的眼部分析图
    """
    global eye_analysis_data

    # 这里需要重新合成整个主图像
    # 由于我们需要访问原始的frame_copy，我们将在全局变量中保存它
    if 'main_frame' in eye_analysis_data and eye_analysis_data['main_frame'] is not None:
        frame_copy = eye_analysis_data['main_frame'].copy()

        # 重新叠加眼部分析图
        if eye_analysis_data['current_image'] is not None:
            frame_copy = overlay_eye_display_on_frame(frame_copy, eye_analysis_data['current_image'], position=(10, 10))

        # 更新显示
        cv2.imshow(eye_analysis_data['window_name'], frame_copy)


def handle_user_interaction(is_video: bool, filename: str, frame_copy, frame_count: int = 0):
    """
    处理用户交互逻辑

    Args:
        is_video: 是否为视频文件
        filename: 文件名（不含扩展名）
        frame_copy: 处理后的图像帧
        frame_count: 视频帧计数（仅视频文件使用）

    Returns:
        用户操作结果：'continue', 'save', 'quit', 'previous'
    """
    if is_video:
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q') or key == 27:  # 'q' 或 ESC 键退出
            return 'quit'
        elif key == ord('s'):  # 's' 键保存当前帧
            save_path = f"result_{filename}_frame_{frame_count}.jpg"
            cv2.imwrite(save_path, frame_copy)
            print(f"结果已保存到: {save_path}")
            return 'save'
        return 'continue'
    else:
        # 图片文件的交互处理
        while True:
            print("按键控制: 'f'=下一张, 'd'=上一张, 's'=保存, 'q'=退出, ESC=退出")
            key = cv2.waitKey(0) & 0xFF

            if key == ord('q') or key == 27:  # 'q' 或 ESC 键退出
                return 'quit'
            elif key == ord('s'):  # 's' 键保存
                save_path = f"result_{filename}_image.jpg"
                cv2.imwrite(save_path, frame_copy)
                print(f"结果已保存到: {save_path}")
                return 'save'
            elif key == ord('f'):  # 'f' 键继续下一张
                print("继续处理下一张...")
                return 'continue'
            elif key == ord('d'):  # 'd' 键返回上一张
                print("返回上一张...")
                return 'previous'
            else:
                print(f"未识别的按键 (按键码: {key})，请使用: 'f'=下一张, 'd'=上一张, 's'=保存, 'q'=退出")
                continue


def process_single_file(file_path: str, file_ext: str, calmcar_dms):
    """
    处理单个文件（图片或视频）

    Args:
        file_path: 文件路径
        file_ext: 文件扩展名
        calmcar_dms: DMS检测器实例

    Returns:
        str: 用户操作结果 ('continue', 'quit', 'previous')
    """
    try:
        is_video = file_ext.lower() in VIDEO_EXTENSIONS
        print(f"\n正在处理: {os.path.basename(file_path)}")

        if is_video:
            return process_video_file(file_path, calmcar_dms)
        else:
            return process_image_file(file_path, calmcar_dms)

    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")
        return 'continue'


def process_video_file(file_path: str, calmcar_dms):
    """处理视频文件"""
    cap = cv2.VideoCapture(file_path)
    if not cap.isOpened():
        print(f"错误: 无法打开视频文件 {file_path}")
        return 'continue'

    filename = os.path.splitext(os.path.basename(file_path))[0]
    frame_count = 0

    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break

            frame_count += 1
            print(f"  处理第 {frame_count} 帧")

            # 运行DMS检测并处理结果
            action = process_frame(frame, filename, calmcar_dms, True, frame_count)
            if action == 'quit':
                return 'quit'  # 用户选择退出

    finally:
        cap.release()

    return 'continue'


def process_image_file(file_path: str, calmcar_dms):
    """处理图片文件"""
    frame = cv2.imread(file_path)
    if frame is None:
        print(f"错误: 无法读取图片文件 {file_path}")
        return 'continue'

    print(f"  成功读取图片，尺寸: {frame.shape}")
    filename = os.path.splitext(os.path.basename(file_path))[0]

    # 运行DMS检测并处理结果
    return process_frame(frame, filename, calmcar_dms, False)


def overlay_eye_display_on_frame(frame, eye_display, position=(10, 10)):
    """
    将眼部分析图叠加到主图像的指定位置

    Args:
        frame: 主图像
        eye_display: 眼部分析图像
        position: 叠加位置 (x, y)，默认为左上角

    Returns:
        叠加后的图像
    """
    global eye_analysis_data

    # 使用当前图像（可能包含高亮效果）
    current_eye_display = eye_analysis_data.get('current_image', eye_display)
    if current_eye_display is None:
        return frame

    x, y = position
    h, w = current_eye_display.shape[:2]

    # 确保叠加区域不超出主图像边界
    frame_h, frame_w = frame.shape[:2]
    if x + w > frame_w:
        w = frame_w - x
        current_eye_display = current_eye_display[:, :w]
    if y + h > frame_h:
        h = frame_h - y
        current_eye_display = current_eye_display[:h, :]

    # 将眼部分析图叠加到主图像上
    frame[y:y+h, x:x+w] = current_eye_display

    return frame


def process_frame(frame, filename: str, calmcar_dms, is_video: bool, frame_count: int = 0):
    """
    处理单帧图像

    Returns:
        str: 用户操作结果 ('continue', 'quit', 'previous')
    """
    global eye_analysis_data

    # 运行DMS检测
    results = calmcar_dms.run(frame)

    # 绘制检测结果
    frame_copy = frame.copy()
    frame_copy = draw_result(results, frame_copy)

    # 处理每个检测结果，将眼部分析图叠加到主图像上
    for result in results:
        # 提取眼部区域和关键点信息
        left_eye_data, right_eye_data = extract_eye_regions_with_landmarks(frame, result)

        # 提取图像和关键点
        left_eye = left_eye_data['image'] if left_eye_data else None
        right_eye = right_eye_data['image'] if right_eye_data else None
        left_landmarks = left_eye_data['landmarks'] if left_eye_data else None
        right_landmarks = right_eye_data['landmarks'] if right_eye_data else None

        # 创建眼部分析显示，使用掩码检测
        eye_display, layout_info = create_eye_analysis_display_with_mask(
            left_eye, right_eye, left_landmarks, right_landmarks)

        # 更新全局数据
        if eye_display is not None:
            eye_analysis_data['original_image'] = eye_display.copy()
            eye_analysis_data['current_image'] = eye_display.copy()
            eye_analysis_data['layout_info'] = layout_info
            eye_analysis_data['highlight_grid'] = None

            # 将眼部分析图叠加到主图像的左上角
            frame_copy = overlay_eye_display_on_frame(frame_copy, eye_display, position=(10, 10))

    # 保存主图像用于刷新显示
    eye_analysis_data['main_frame'] = frame_copy.copy()

    # 显示合成后的结果
    window_name = eye_analysis_data['window_name']
    cv2.imshow(window_name, frame_copy)

    # 设置鼠标回调（只在第一次设置）
    cv2.setMouseCallback(window_name, mouse_callback)

    # 处理用户交互
    action = handle_user_interaction(is_video, filename, frame_copy, frame_count)

    if action == 'quit':
        print("用户退出")
        cv2.destroyAllWindows()

    return action


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("请提供目录路径作为命令行参数，例如：python test_glass.py your_directory")
        sys.exit(1)

    # 打印当前参数设置
    print_current_params()

    # 打印可视化说明
    print("\n=== 可视化说明 ===")
    print("眼部分析图下半部分颜色含义：")
    print("- 亮色区域：检测到的眼镜镜片区域（掩码区域）")
    print("- 暗蓝色区域：被忽略的眼部区域")
    print("- 红色强度：反光置信度")
    print("- 绿色强度：边缘强度")
    print("- 蓝色强度：亮度信息")
    print("=" * 30)

    # 初始化DMS检测器
    calmcar_dms = calmcardms.CalmcarDms("config.json")

    # 获取文件列表
    directory = sys.argv[1]
    file_lists = find_file(directory, sort_by='name')  # 按文件名排序

    print(f"找到 {len(file_lists)} 个文件:")
    # for i, file_info in enumerate(file_lists):
    #     print(f"  {i+1}. {file_info[0]} ({file_info[1]})")

    # 处理文件，支持前后导航
    current_index = 0
    while current_index < len(file_lists):
        file_path, file_ext = file_lists[current_index]
        action = process_single_file(file_path, file_ext, calmcar_dms)

        if action == 'quit':
            break  # 用户选择退出
        elif action == 'previous':
            # 返回上一张图片
            if current_index > 0:
                current_index -= 1
                print(f"返回到第 {current_index + 1} 个文件")
            else:
                print("已经是第一个文件了")
        else:  # action == 'continue' or action == 'save'
            # 继续下一张图片
            current_index += 1

    # 清理资源
    cv2.destroyAllWindows()
    print("处理完成！")


if __name__ == "__main__":
    main()