#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本 - 验证修改后的 test_glass.py 功能
"""

import os
import sys
import cv2
import numpy as np

def test_find_file():
    """测试文件查找功能"""
    # 直接定义 find_file 函数来测试
    def find_file(dir):
        """查找目录中的图片和视频文件"""
        file_list = []
        # 支持的图片和视频格式
        image_extensions = {'jpg', 'jpeg', 'png', 'bmp', 'webp'}
        video_extensions = {'mp4', 'ts', 'mkv', 'h264', 'avi', 'mov'}
        all_extensions = image_extensions | video_extensions

        for dirpath, _, files in os.walk(dir):
            for file in files:
                name_parts = file.split('.')
                if len(name_parts) > 1:
                    extension = name_parts[-1].lower()
                    if extension in all_extensions:
                        # 使用 os.path.join 确保跨平台兼容性
                        full_path = os.path.join(dirpath, file)
                        file_list.append((full_path, extension))
        return file_list
    
    # 创建测试目录和文件
    test_dir = "test_images"
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    # 创建一个测试图片
    test_image = np.zeros((100, 100, 3), dtype=np.uint8)
    test_image[:, :] = [100, 150, 200]  # 设置颜色
    
    test_files = [
        "test1.jpg",
        "test2.png", 
        "test3.bmp",
        "video1.mp4",
        "document.txt"  # 这个不应该被找到
    ]
    
    # 保存测试图片
    for filename in test_files[:3]:  # 只保存图片文件
        cv2.imwrite(os.path.join(test_dir, filename), test_image)
    
    # 创建空的视频文件（仅用于测试文件查找）
    for filename in test_files[3:4]:  # 视频文件
        with open(os.path.join(test_dir, filename), 'w') as f:
            f.write("")
    
    # 创建文本文件
    with open(os.path.join(test_dir, test_files[4]), 'w') as f:
        f.write("test")
    
    # 测试文件查找
    found_files = find_file(test_dir)
    
    print("测试文件查找功能:")
    print(f"创建的测试文件: {test_files}")
    print(f"找到的文件: {[os.path.basename(f[0]) for f in found_files]}")
    
    # 验证结果
    expected_files = {"test1.jpg", "test2.png", "test3.bmp", "video1.mp4"}
    found_basenames = {os.path.basename(f[0]) for f in found_files}
    
    if expected_files == found_basenames:
        print("✅ 文件查找功能测试通过")
    else:
        print("❌ 文件查找功能测试失败")
        print(f"期望: {expected_files}")
        print(f"实际: {found_basenames}")
    
    # 清理测试文件
    for filename in test_files:
        file_path = os.path.join(test_dir, filename)
        if os.path.exists(file_path):
            os.remove(file_path)
    os.rmdir(test_dir)
    
    return len(found_files) == 4

def main():
    """主测试函数"""
    print("开始测试修改后的 test_glass.py 功能...")
    
    # 测试文件查找功能
    if test_find_file():
        print("所有测试通过！")
        return 0
    else:
        print("测试失败！")
        return 1

if __name__ == "__main__":
    sys.exit(main())
