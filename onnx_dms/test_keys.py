#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试按键功能的简单脚本
"""

import cv2
import numpy as np

def test_key_controls():
    """测试按键控制功能"""
    
    # 创建测试图片
    images = []
    colors = [(100, 50, 200), (50, 200, 100), (200, 100, 50)]  # BGR
    texts = ["Image 1 - Press 'f' to continue", "Image 2 - Press 's' to save", "Image 3 - Press 'q' to quit"]
    
    for i, (color, text) in enumerate(zip(colors, texts)):
        img = np.zeros((400, 800, 3), dtype=np.uint8)
        img[:, :] = color
        
        # 添加文字说明
        cv2.putText(img, text, (50, 200), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        cv2.putText(img, "Controls: 'f'=next, 's'=save, 'q'=quit", (50, 300), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
        
        images.append((f"test_image_{i+1}.jpg", img))
    
    print("按键控制测试")
    print("=" * 50)
    print("控制说明:")
    print("  'f' 键 - 继续下一张图片")
    print("  's' 键 - 保存当前图片")
    print("  'q' 键或 ESC - 退出")
    print("=" * 50)
    
    for i, (filename, img) in enumerate(images):
        print(f"\n显示图片 {i+1}/{len(images)}: {filename}")
        
        cv2.imshow("Key Control Test", img)
        
        while True:
            print("按键控制: 'f'=继续, 's'=保存, 'q'=退出, ESC=退出")
            key = cv2.waitKey(0) & 0xFF
            
            if key == ord('q') or key == 27:  # 'q' 或 ESC
                print("用户退出")
                cv2.destroyAllWindows()
                return
            elif key == ord('s'):  # 's' 保存
                save_path = f"saved_{filename}"
                cv2.imwrite(save_path, img)
                print(f"图片已保存到: {save_path}")
            elif key == ord('f'):  # 'f' 继续
                print("继续下一张...")
                break
            else:
                print(f"未识别的按键 (code: {key})，请使用: 'f'=继续, 's'=保存, 'q'=退出")
    
    print("\n所有图片处理完成！")
    cv2.destroyAllWindows()

if __name__ == "__main__":
    test_key_controls()
