#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建演示图片的脚本
"""

import cv2
import numpy as np
import os

def create_demo_images():
    """创建一些演示图片"""
    
    # 确保目录存在
    os.makedirs(".", exist_ok=True)
    
    # 创建几个不同的测试图片
    images = []
    
    # 图片1: 红色背景
    img1 = np.zeros((480, 640, 3), dtype=np.uint8)
    img1[:, :] = [50, 50, 200]  # BGR格式，红色
    cv2.putText(img1, "Demo Image 1", (200, 240), cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 3)
    images.append(("demo1.jpg", img1))
    
    # 图片2: 绿色背景
    img2 = np.zeros((480, 640, 3), dtype=np.uint8)
    img2[:, :] = [50, 200, 50]  # BGR格式，绿色
    cv2.putText(img2, "Demo Image 2", (200, 240), cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 3)
    images.append(("demo2.png", img2))
    
    # 图片3: 蓝色背景
    img3 = np.zeros((480, 640, 3), dtype=np.uint8)
    img3[:, :] = [200, 50, 50]  # BGR格式，蓝色
    cv2.putText(img3, "Demo Image 3", (200, 240), cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 3)
    images.append(("demo3.bmp", img3))
    
    # 保存图片
    for filename, img in images:
        cv2.imwrite(filename, img)
        print(f"创建了演示图片: {filename}")
    
    print(f"总共创建了 {len(images)} 个演示图片")
    print("现在可以运行: python ../test_glass.py .")

if __name__ == "__main__":
    create_demo_images()
