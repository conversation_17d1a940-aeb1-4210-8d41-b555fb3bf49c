# DMS自动化流水线

## 项目概述

基于现有的 `dms_postmortem_optimised.py` 和 `cut_video_fromwhatyouwant_optimized.py`，开发端到端自动化DMS分析流水线，解决手动流程痛点。

## 核心功能

- **一键式执行**: 通过配置文件驱动的完整自动化流程
- **智能去重**: 基于文件命名的重复检测机制
- **环境自动化**: C++文件自动部署和配置确认
- **远程验证**: SSH连接、服务管理和模型同步
- **断点续传**: 支持任务中断后从断点继续执行

## 快速开始

```bash
# 切换到项目目录
cd /home/<USER>/tool_kit/dms_postmortem_end2end/dms_automation_pipeline

# 运行自动化流水线
python dms_automation_main.py --config config/pipeline_config.json

# 查看帮助
python dms_automation_main.py --help
```

## 目录结构

```
dms_automation_pipeline/
├── dms_automation_main.py          # 主入口程序
├── core/                           # 核心模块
│   ├── task_orchestrator.py       # 任务编排器
│   ├── video_preprocessor.py      # 视频预处理器
│   ├── environment_manager.py     # 环境管理器
│   ├── remote_validator.py        # 远程验证器
│   └── rendering_engine.py        # 渲染引擎
├── config/                         # 配置文件
│   ├── pipeline_config_template.json
│   └── validation_schemas.json
├── utils/                          # 工具模块
│   ├── file_utils.py              # 文件操作工具
│   ├── validation_utils.py        # 验证工具
│   └── logger_config.py           # 日志配置
└── tests/                          # 测试模块
```

## 配置说明

主配置文件 `config/pipeline_config.json` 包含：
- 视频预处理配置
- 环境管理配置  
- 远程验证配置
- 渲染引擎配置

详细配置说明请参考 `config/pipeline_config_template.json`

## 开发状态

当前版本: v1.0.0
开发阶段: 阶段一 - 基础框架搭建

## 许可证

内部项目，仅供DMS分析使用。
