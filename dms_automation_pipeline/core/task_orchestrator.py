"""
任务编排器 - DMS自动化流水线的核心控制器
实现状态机管理工作流，支持断点续传和进度跟踪
"""

import os
import json
import time
import logging
from enum import Enum
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from datetime import datetime

from rich.console import Console
from rich.progress import Progress, TaskID, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
from rich.panel import Panel
from rich.table import Table

console = Console()


class PipelineState(Enum):
    """流水线状态枚举"""
    INIT = "INIT"
    VIDEO_PROCESSING = "VIDEO_PROCESSING"
    ENV_SETUP = "ENV_SETUP"
    REMOTE_VALIDATION = "REMOTE_VALIDATION"
    RENDERING = "RENDERING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"


@dataclass
class StageResult:
    """阶段执行结果"""
    stage: str
    success: bool
    start_time: str
    end_time: Optional[str] = None
    error_message: Optional[str] = None
    output_data: Optional[Dict[str, Any]] = None


@dataclass
class PipelineStatus:
    """流水线状态信息"""
    current_state: PipelineState
    start_time: str
    last_update: str
    stage_results: Dict[str, StageResult]
    config_hash: str
    total_stages: int = 5


class TaskOrchestrator:
    """任务编排器 - 管理整个DMS自动化流水线"""
    
    def __init__(self, config: Dict[str, Any], state_file: str = ".pipeline_state.json"):
        """
        初始化任务编排器
        
        Args:
            config: 流水线配置
            state_file: 状态持久化文件路径
        """
        self.config = config
        self.state_file = state_file
        self.logger = self._setup_logger()
        
        # 初始化状态
        self.status = self._load_or_create_status()
        
        # 阶段定义
        self.stages = [
            PipelineState.VIDEO_PROCESSING,
            PipelineState.ENV_SETUP,
            PipelineState.REMOTE_VALIDATION,
            PipelineState.RENDERING,
            PipelineState.COMPLETED
        ]
        
        # 进度显示
        self.console = console
        self.progress = None
        self.task_ids = {}
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger("TaskOrchestrator")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _calculate_config_hash(self) -> str:
        """计算配置文件哈希值，用于检测配置变更"""
        import hashlib
        config_str = json.dumps(self.config, sort_keys=True)
        return hashlib.md5(config_str.encode()).hexdigest()
    
    def _load_or_create_status(self) -> PipelineStatus:
        """加载或创建流水线状态"""
        if os.path.exists(self.state_file):
            try:
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 检查配置是否变更
                current_hash = self._calculate_config_hash()
                if data.get('config_hash') != current_hash:
                    self.logger.warning("配置文件已变更，重置流水线状态")
                    return self._create_new_status()
                
                # 恢复状态
                status = PipelineStatus(
                    current_state=PipelineState(data['current_state']),
                    start_time=data['start_time'],
                    last_update=data['last_update'],
                    stage_results={
                        k: StageResult(**v) for k, v in data['stage_results'].items()
                    },
                    config_hash=data['config_hash'],
                    total_stages=data.get('total_stages', 5)
                )
                
                self.logger.info(f"从状态文件恢复: {status.current_state.value}")
                return status
                
            except Exception as e:
                self.logger.error(f"加载状态文件失败: {e}")
                return self._create_new_status()
        else:
            return self._create_new_status()
    
    def _create_new_status(self) -> PipelineStatus:
        """创建新的流水线状态"""
        return PipelineStatus(
            current_state=PipelineState.INIT,
            start_time=datetime.now().isoformat(),
            last_update=datetime.now().isoformat(),
            stage_results={},
            config_hash=self._calculate_config_hash(),
            total_stages=5
        )
    
    def save_state(self):
        """保存当前状态到文件"""
        try:
            self.status.last_update = datetime.now().isoformat()
            
            data = {
                'current_state': self.status.current_state.value,
                'start_time': self.status.start_time,
                'last_update': self.status.last_update,
                'stage_results': {
                    k: asdict(v) for k, v in self.status.stage_results.items()
                },
                'config_hash': self.status.config_hash,
                'total_stages': self.status.total_stages
            }
            
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
            self.logger.debug(f"状态已保存: {self.status.current_state.value}")
            
        except Exception as e:
            self.logger.error(f"保存状态失败: {e}")
    
    def get_current_state(self) -> PipelineState:
        """获取当前状态"""
        return self.status.current_state

    def set_state(self, new_state: PipelineState):
        """设置新状态"""
        old_state = self.status.current_state
        self.status.current_state = new_state
        self.logger.info(f"状态转换: {old_state.value} -> {new_state.value}")
    
    def get_stage_result(self, stage: str) -> Optional[StageResult]:
        """获取阶段执行结果"""
        return self.status.stage_results.get(stage)
    
    def can_skip_stage(self, stage: str) -> bool:
        """检查是否可以跳过某个阶段（已成功完成）"""
        result = self.get_stage_result(stage)
        return result is not None and result.success
    
    def display_status(self):
        """显示当前流水线状态"""
        table = Table(title="DMS自动化流水线状态", show_header=True, header_style="bold blue")
        table.add_column("阶段", style="cyan", width=20)
        table.add_column("状态", style="magenta", width=15)
        table.add_column("开始时间", style="green", width=20)
        table.add_column("结束时间", style="green", width=20)
        table.add_column("备注", style="yellow")
        
        for stage in self.stages[:-1]:  # 排除COMPLETED状态
            stage_name = stage.value
            result = self.get_stage_result(stage_name)
            
            if result:
                status = "✅ 成功" if result.success else "❌ 失败"
                start_time = result.start_time[:19] if result.start_time else "-"
                end_time = result.end_time[:19] if result.end_time else "-"
                note = result.error_message if result.error_message else "完成"
            elif self.status.current_state == stage:
                status = "🔄 进行中"
                start_time = "-"
                end_time = "-"
                note = "正在执行"
            else:
                status = "⏳ 等待"
                start_time = "-"
                end_time = "-"
                note = "未开始"
            
            table.add_row(stage_name, status, start_time, end_time, note)
        
        panel = Panel(
            table,
            title=f"[bold green]当前状态: {self.status.current_state.value}[/bold green]",
            border_style="green"
        )
        
        self.console.print(panel)
    
    @classmethod
    def load_from_state(cls, state_file: str = ".pipeline_state.json") -> 'TaskOrchestrator':
        """从状态文件加载TaskOrchestrator实例"""
        if not os.path.exists(state_file):
            raise FileNotFoundError(f"状态文件不存在: {state_file}")
        
        with open(state_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 这里需要重新构造config，实际使用时需要从配置文件加载
        config = {}  # 临时空配置，实际应该从配置文件加载
        
        return cls(config, state_file)
    
    def reset_pipeline(self):
        """重置流水线状态"""
        self.status = self._create_new_status()
        self.save_state()
        self.logger.info("流水线状态已重置")
    
    def get_next_stage(self) -> Optional[PipelineState]:
        """获取下一个要执行的阶段"""
        current_index = None
        for i, stage in enumerate(self.stages):
            if stage == self.status.current_state:
                current_index = i
                break

        if current_index is not None and current_index < len(self.stages) - 1:
            return self.stages[current_index + 1]

        return None

    def execute_stage(self, stage_name: str) -> bool:
        """执行指定阶段"""
        try:
            if stage_name == "VIDEO_PROCESSING":
                return self._execute_video_processing()
            elif stage_name == "ENV_SETUP":
                return self._execute_env_setup()
            elif stage_name == "REMOTE_VALIDATION":
                return self._execute_remote_validation()
            elif stage_name == "RENDERING":
                return self._execute_rendering()
            else:
                self.logger.error(f"未知阶段: {stage_name}")
                return False
        except Exception as e:
            self.logger.error(f"执行阶段 {stage_name} 失败: {e}")
            return False

    def _execute_video_processing(self) -> bool:
        """执行视频预处理阶段"""
        self.logger.info("开始视频预处理阶段")

        try:
            from .video_preprocessor import VideoPreprocessor

            # 创建视频预处理器
            processor = VideoPreprocessor(self.config)

            # 获取视频配置
            video_configs = self.config.get('video_config', {}).get('source_videos', [])
            if not video_configs:
                self.logger.warning("没有配置视频文件")
                return True  # 空配置也算成功

            # 执行批量处理
            result = processor.process_videos(video_configs)

            # 记录结果
            success = result['failed_count'] == 0
            error_msg = None if success else f"处理失败 {result['failed_count']} 个片段"

            # 简化的结果记录
            self.status.stage_results["VIDEO_PROCESSING"] = {
                'success': success,
                'error_message': error_msg,
                'output_data': result
            }

            self.logger.info(f"视频预处理完成: 成功 {result['success_count']}, 失败 {result['failed_count']}")
            return success

        except Exception as e:
            self.logger.error(f"视频预处理异常: {e}")
            self.status.stage_results["VIDEO_PROCESSING"] = StageResult(
                stage="VIDEO_PROCESSING",
                success=False,
                start_time=datetime.now().isoformat(),
                end_time=datetime.now().isoformat(),
                error_message=str(e)
            )
            return False

    def _execute_env_setup(self) -> bool:
        """执行环境设置阶段"""
        self.logger.info("开始环境设置阶段")

        try:
            from .environment_manager import EnvironmentManager

            # 创建环境管理器
            env_manager = EnvironmentManager(self.config)

            # 1. 部署C++文件
            self.logger.info("开始C++文件部署...")
            deploy_success = env_manager.deploy_cpp_files()
            if not deploy_success:
                error_msg = "C++文件部署失败"
                self.logger.error(error_msg)
                self.status.stage_results["ENV_SETUP"] = StageResult(
                    stage="ENV_SETUP",
                    success=False,
                    start_time=datetime.now().isoformat(),
                    end_time=datetime.now().isoformat(),
                    error_message=error_msg
                )
                return False

            # 2. 配置文件验证和确认
            self.logger.info("开始配置文件验证和确认...")
            config_success = env_manager.validate_and_confirm_configs()
            if not config_success:
                error_msg = "配置文件验证或确认失败"
                self.logger.error(error_msg)
                self.status.stage_results["ENV_SETUP"] = StageResult(
                    stage="ENV_SETUP",
                    success=False,
                    start_time=datetime.now().isoformat(),
                    end_time=datetime.now().isoformat(),
                    error_message=error_msg
                )
                return False

            # 3. 环境验证
            self.logger.info("开始环境完整性验证...")
            validation_result = env_manager.validate_environment()

            overall_success = (
                validation_result['all_files_exist'] and
                validation_result['permissions_correct'] and
                validation_result['configs_valid']
            )

            if overall_success:
                self.logger.info("环境设置阶段完成")
                self.status.stage_results["ENV_SETUP"] = StageResult(
                    stage="ENV_SETUP",
                    success=True,
                    start_time=datetime.now().isoformat(),
                    end_time=datetime.now().isoformat(),
                    output_data={
                        "deploy_success": deploy_success,
                        "config_success": config_success,
                        "validation_result": validation_result
                    }
                )
                return True
            else:
                error_msg = f"环境验证失败: {validation_result}"
                self.logger.error(error_msg)
                self.status.stage_results["ENV_SETUP"] = StageResult(
                    stage="ENV_SETUP",
                    success=False,
                    start_time=datetime.now().isoformat(),
                    end_time=datetime.now().isoformat(),
                    error_message=error_msg,
                    output_data=validation_result
                )
                return False

        except Exception as e:
            error_msg = f"环境设置异常: {e}"
            self.logger.error(error_msg)
            self.status.stage_results["ENV_SETUP"] = StageResult(
                stage="ENV_SETUP",
                success=False,
                start_time=datetime.now().isoformat(),
                end_time=datetime.now().isoformat(),
                error_message=error_msg
            )
            return False

    def _execute_remote_validation(self) -> bool:
        """执行远程验证阶段"""
        self.logger.info("开始远程验证阶段")

        try:
            from .remote_validator import RemoteValidator

            # 创建远程验证器
            remote_validator = RemoteValidator(self.config)

            # 执行远程验证流程
            validation_success = remote_validator.execute_validation()

            if validation_success:
                self.logger.info("远程验证阶段完成")
                self.status.stage_results["REMOTE_VALIDATION"] = StageResult(
                    stage="REMOTE_VALIDATION",
                    success=True,
                    start_time=datetime.now().isoformat(),
                    end_time=datetime.now().isoformat(),
                    output_data={
                        "validation_success": True,
                        "enabled": remote_validator.enabled,
                        "target_ip": getattr(remote_validator, 'target_ip', None),
                        "service_port": getattr(remote_validator, 'service_port', None)
                    }
                )
                return True
            else:
                error_msg = "远程验证失败"
                self.logger.error(error_msg)
                self.status.stage_results["REMOTE_VALIDATION"] = StageResult(
                    stage="REMOTE_VALIDATION",
                    success=False,
                    start_time=datetime.now().isoformat(),
                    end_time=datetime.now().isoformat(),
                    error_message=error_msg,
                    output_data={
                        "validation_success": False,
                        "enabled": remote_validator.enabled
                    }
                )
                return False

        except Exception as e:
            error_msg = f"远程验证异常: {e}"
            self.logger.error(error_msg)
            self.status.stage_results["REMOTE_VALIDATION"] = StageResult(
                stage="REMOTE_VALIDATION",
                success=False,
                start_time=datetime.now().isoformat(),
                end_time=datetime.now().isoformat(),
                error_message=error_msg
            )
            return False

    def _execute_rendering(self) -> bool:
        """执行渲染阶段"""
        self.logger.info("开始渲染阶段")

        try:
            from .rendering_engine import RenderingEngine, RenderingTask

            # 创建渲染引擎
            rendering_engine = RenderingEngine(self.config)

            # 获取渲染配置
            rendering_config = self.config.get('rendering_config', {})
            render_tasks_config = rendering_config.get('render_tasks', [])

            if not render_tasks_config:
                self.logger.warning("没有配置渲染任务")
                # 尝试从视频预处理结果中获取任务
                video_stage_result = self.status.stage_results.get('VIDEO_PROCESSING')
                if video_stage_result and video_stage_result.output_data:
                    output_files = video_stage_result.output_data.get('output_files', [])
                    if output_files:
                        # 为每个处理过的视频文件创建渲染任务
                        render_tasks_config = [
                            {
                                'task_id': f'auto_task_{i:03d}',
                                'video_paths': [output_file],
                                'output_path': f"{rendering_engine.output_dir}/rendered_{i:03d}.mp4"
                            }
                            for i, output_file in enumerate(output_files, 1)
                        ]
                        self.logger.info(f"自动创建了 {len(render_tasks_config)} 个渲染任务")

            if not render_tasks_config:
                self.logger.info("没有视频需要渲染，跳过渲染阶段")
                self.status.stage_results["RENDERING"] = StageResult(
                    stage="RENDERING",
                    success=True,
                    start_time=datetime.now().isoformat(),
                    end_time=datetime.now().isoformat(),
                    output_data={"message": "没有视频需要渲染", "tasks_count": 0}
                )
                return True

            # 创建渲染任务
            render_tasks = []
            for task_config in render_tasks_config:
                task = RenderingTask(
                    task_id=task_config['task_id'],
                    video_paths=task_config['video_paths'],
                    output_path=task_config['output_path'],
                    fps=task_config.get('fps', 10.0),
                    roi_region=task_config.get('roi_region'),
                    expected_size=task_config.get('expected_size'),
                    release_tmp_source=task_config.get('release_tmp_source', True)
                )
                render_tasks.append(task)

            # 执行批量渲染
            self.logger.info(f"开始执行 {len(render_tasks)} 个渲染任务")
            render_results = rendering_engine.render_videos_batch(render_tasks)

            # 统计结果
            success_count = sum(1 for r in render_results if r.success)
            failed_count = len(render_results) - success_count
            total_frames = sum(r.total_frames for r in render_results if r.success)
            total_time = sum(r.processing_time for r in render_results)

            if failed_count == 0:
                self.logger.info("渲染阶段完成")
                self.status.stage_results["RENDERING"] = StageResult(
                    stage="RENDERING",
                    success=True,
                    start_time=datetime.now().isoformat(),
                    end_time=datetime.now().isoformat(),
                    output_data={
                        "success_count": success_count,
                        "failed_count": failed_count,
                        "total_frames": total_frames,
                        "total_processing_time": total_time,
                        "output_files": [r.output_file for r in render_results if r.success],
                        "render_results": [
                            {
                                "task_id": r.task_id,
                                "success": r.success,
                                "output_file": r.output_file,
                                "total_frames": r.total_frames,
                                "processing_time": r.processing_time
                            }
                            for r in render_results
                        ]
                    }
                )
                return True
            else:
                error_msg = f"渲染失败: 成功 {success_count}, 失败 {failed_count}"
                self.logger.error(error_msg)
                self.status.stage_results["RENDERING"] = StageResult(
                    stage="RENDERING",
                    success=False,
                    start_time=datetime.now().isoformat(),
                    end_time=datetime.now().isoformat(),
                    error_message=error_msg,
                    output_data={
                        "success_count": success_count,
                        "failed_count": failed_count,
                        "failed_tasks": [r.task_id for r in render_results if not r.success]
                    }
                )
                return False

        except Exception as e:
            error_msg = f"渲染阶段异常: {e}"
            self.logger.error(error_msg)
            self.status.stage_results["RENDERING"] = StageResult(
                stage="RENDERING",
                success=False,
                start_time=datetime.now().isoformat(),
                end_time=datetime.now().isoformat(),
                error_message=error_msg
            )
            return False

    def display_status(self):
        """显示当前流水线状态"""
        if HAS_RICH:
            table = Table(title="DMS自动化流水线状态", show_header=True, header_style="bold blue")
            table.add_column("阶段", style="cyan", width=20)
            table.add_column("状态", style="magenta", width=15)

            for stage in self.stages[:-1]:
                stage_name = stage.value
                result = self.status.stage_results.get(stage_name)

                if result:
                    status = "✅ 成功" if result.success else "❌ 失败"
                elif self.status.current_state == stage:
                    status = "🔄 进行中"
                else:
                    status = "⏳ 等待"

                table.add_row(stage_name, status)

            panel = Panel(
                table,
                title=f"[bold green]当前状态: {self.status.current_state.value}[/bold green]",
                border_style="green"
            )

            console.print(panel)
        else:
            print(f"\n=== DMS自动化流水线状态 ===")
            print(f"当前状态: {self.status.current_state.value}")
            print("\n阶段状态:")

            for stage in self.stages[:-1]:
                stage_name = stage.value
                result = self.status.stage_results.get(stage_name)

                if result:
                    status = "✅ 成功" if result.success else "❌ 失败"
                elif self.status.current_state == stage:
                    status = "🔄 进行中"
                else:
                    status = "⏳ 等待"

                print(f"  {stage_name}: {status}")
            print("=" * 30)

    def reset_pipeline(self):
        """重置流水线状态"""
        self.status = self._create_new_status()
        self.logger.info("流水线状态已重置")

    def _execute_env_setup(self) -> bool:
        """执行环境设置阶段"""
        self.logger.info("开始环境设置阶段")

        try:
            from .environment_manager import EnvironmentManager

            # 创建环境管理器
            env_manager = EnvironmentManager(self.config)

            # 1. 部署C++文件
            self.logger.info("开始C++文件部署...")
            deploy_success = env_manager.deploy_cpp_files()
            if not deploy_success:
                error_msg = "C++文件部署失败"
                self.logger.error(error_msg)
                self.status.stage_results["ENV_SETUP"] = StageResult(
                    stage="ENV_SETUP",
                    success=False,
                    start_time=datetime.now().isoformat(),
                    end_time=datetime.now().isoformat(),
                    error_message=error_msg
                )
                return False

            # 2. 配置文件验证和确认
            self.logger.info("开始配置文件验证和确认...")
            config_success = env_manager.validate_and_confirm_configs()
            if not config_success:
                error_msg = "配置文件验证或确认失败"
                self.logger.error(error_msg)
                self.status.stage_results["ENV_SETUP"] = StageResult(
                    stage="ENV_SETUP",
                    success=False,
                    start_time=datetime.now().isoformat(),
                    end_time=datetime.now().isoformat(),
                    error_message=error_msg
                )
                return False

            # 3. 环境验证
            self.logger.info("开始环境完整性验证...")
            validation_result = env_manager.validate_environment()

            overall_success = (
                validation_result['all_files_exist'] and
                validation_result['permissions_correct'] and
                validation_result['configs_valid']
            )

            if overall_success:
                self.logger.info("环境设置阶段完成")
                self.status.stage_results["ENV_SETUP"] = StageResult(
                    stage="ENV_SETUP",
                    success=True,
                    start_time=datetime.now().isoformat(),
                    end_time=datetime.now().isoformat(),
                    output_data={
                        "deploy_success": deploy_success,
                        "config_success": config_success,
                        "validation_result": validation_result
                    }
                )
                return True
            else:
                error_msg = f"环境验证失败: {validation_result}"
                self.logger.error(error_msg)
                self.status.stage_results["ENV_SETUP"] = StageResult(
                    stage="ENV_SETUP",
                    success=False,
                    start_time=datetime.now().isoformat(),
                    end_time=datetime.now().isoformat(),
                    error_message=error_msg,
                    output_data=validation_result
                )
                return False

        except Exception as e:
            error_msg = f"环境设置异常: {e}"
            self.logger.error(error_msg)
            self.status.stage_results["ENV_SETUP"] = StageResult(
                stage="ENV_SETUP",
                success=False,
                start_time=datetime.now().isoformat(),
                end_time=datetime.now().isoformat(),
                error_message=error_msg
            )
            return False

    def _execute_remote_validation(self) -> bool:
        """执行远程验证阶段"""
        self.logger.info("开始远程验证阶段")

        try:
            from .remote_validator import RemoteValidator

            # 创建远程验证器
            remote_validator = RemoteValidator(self.config)

            # 执行远程验证流程
            validation_success = remote_validator.execute_validation()

            if validation_success:
                self.logger.info("远程验证阶段完成")
                self.status.stage_results["REMOTE_VALIDATION"] = StageResult(
                    stage="REMOTE_VALIDATION",
                    success=True,
                    start_time=datetime.now().isoformat(),
                    end_time=datetime.now().isoformat(),
                    output_data={
                        "validation_success": True,
                        "enabled": remote_validator.enabled,
                        "target_ip": getattr(remote_validator, 'target_ip', None),
                        "service_port": getattr(remote_validator, 'service_port', None)
                    }
                )
                return True
            else:
                error_msg = "远程验证失败"
                self.logger.error(error_msg)
                self.status.stage_results["REMOTE_VALIDATION"] = StageResult(
                    stage="REMOTE_VALIDATION",
                    success=False,
                    start_time=datetime.now().isoformat(),
                    end_time=datetime.now().isoformat(),
                    error_message=error_msg,
                    output_data={
                        "validation_success": False,
                        "enabled": remote_validator.enabled
                    }
                )
                return False

        except Exception as e:
            error_msg = f"远程验证异常: {e}"
            self.logger.error(error_msg)
            self.status.stage_results["REMOTE_VALIDATION"] = StageResult(
                stage="REMOTE_VALIDATION",
                success=False,
                start_time=datetime.now().isoformat(),
                end_time=datetime.now().isoformat(),
                error_message=error_msg
            )
            return False

    def _execute_rendering(self) -> bool:
        """执行渲染阶段"""
        self.logger.info("开始渲染阶段")
        # TODO: 实现渲染逻辑
        self.status.stage_results["RENDERING"] = StageResult(
            stage="RENDERING",
            success=True,
            start_time=datetime.now().isoformat(),
            end_time=datetime.now().isoformat(),
            output_data={"message": "渲染完成（待实现）"}
        )
        return True

    def display_status(self):
        """显示当前流水线状态"""
        if HAS_RICH:
            table = Table(title="DMS自动化流水线状态", show_header=True, header_style="bold blue")
            table.add_column("阶段", style="cyan", width=20)
            table.add_column("状态", style="magenta", width=15)

            for stage in self.stages[:-1]:
                stage_name = stage.value
                result = self.status.stage_results.get(stage_name)

                if result:
                    status = "✅ 成功" if result.success else "❌ 失败"
                elif self.status.current_state == stage:
                    status = "🔄 进行中"
                else:
                    status = "⏳ 等待"

                table.add_row(stage_name, status)

            panel = Panel(
                table,
                title=f"[bold green]当前状态: {self.status.current_state.value}[/bold green]",
                border_style="green"
            )

            console.print(panel)
        else:
            print(f"\n=== DMS自动化流水线状态 ===")
            print(f"当前状态: {self.status.current_state.value}")
            print("\n阶段状态:")

            for stage in self.stages[:-1]:
                stage_name = stage.value
                result = self.status.stage_results.get(stage_name)

                if result:
                    status = "✅ 成功" if result.success else "❌ 失败"
                elif self.status.current_state == stage:
                    status = "🔄 进行中"
                else:
                    status = "⏳ 等待"

                print(f"  {stage_name}: {status}")
            print("=" * 30)

    def reset_pipeline(self):
        """重置流水线状态"""
        self.status = self._create_new_status()
        self.logger.info("流水线状态已重置")
