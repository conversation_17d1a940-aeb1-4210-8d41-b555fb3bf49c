#!/usr/bin/env python3
"""
DMS自动化流水线主入口程序
支持命令行参数和配置文件驱动的端到端自动化处理
"""

import os
import sys
import json
import argparse
import logging
from typing import Dict, Any, Optional
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from core.task_orchestrator import TaskOrchestrator, PipelineState
from utils.logger_config import setup_pipeline_logger, configure_rich_logging
from utils.validation_utils import validate_json_file, validate_config_structure
from utils.file_utils import ensure_directory_exists

# 尝试导入Rich库进行美化显示
try:
    from rich.console import Console
    from rich.panel import Panel
    from rich.text import Text
    console = Console()
    HAS_RICH = True
except ImportError:
    console = None
    HAS_RICH = False


class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger("ConfigManager")
    
    def load_config(self, config_path: str) -> Optional[Dict[str, Any]]:
        """
        加载配置文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            Dict[str, Any]: 配置字典，失败返回None
        """
        try:
            # 验证配置文件
            valid, error, config_data = validate_json_file(config_path)
            if not valid:
                self.logger.error(f"配置文件验证失败: {error}")
                return None
            
            # 加载验证模式
            schema_path = current_dir / "config" / "validation_schemas.json"
            if schema_path.exists():
                with open(schema_path, 'r', encoding='utf-8') as f:
                    schemas = json.load(f)
                
                # 验证配置结构
                schema = schemas.get("pipeline_config_schema", {})
                valid, errors = validate_config_structure(config_data, schema)
                if not valid:
                    self.logger.error(f"配置结构验证失败: {errors}")
                    return None
            
            self.logger.info(f"配置文件加载成功: {config_path}")
            return config_data
            
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {config_path}, 错误: {e}")
            return None
    
    def validate_config_paths(self, config: Dict[str, Any]) -> bool:
        """
        验证配置中的路径有效性
        
        Args:
            config: 配置字典
            
        Returns:
            bool: 验证是否通过
        """
        try:
            # 验证C++源路径
            cpp_source_path = config.get("environment_config", {}).get("cpp_source_path")
            if cpp_source_path and not os.path.exists(cpp_source_path):
                self.logger.error(f"C++源路径不存在: {cpp_source_path}")
                return False
            
            # 验证视频文件路径
            video_config = config.get("video_config", {})
            for video_info in video_config.get("source_videos", []):
                video_path = video_info.get("path")
                if video_path and not os.path.exists(video_path):
                    self.logger.warning(f"视频文件不存在: {video_path}")
            
            # 验证远程配置文件路径
            remote_config = config.get("remote_config", {})
            if remote_config.get("enabled"):
                ip_port_path = remote_config.get("ip_port_config_path")
                if ip_port_path and not os.path.exists(ip_port_path):
                    self.logger.error(f"远程配置文件不存在: {ip_port_path}")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"路径验证失败: {e}")
            return False


def setup_logging(config: Dict[str, Any]) -> logging.Logger:
    """
    设置日志系统
    
    Args:
        config: 配置字典
        
    Returns:
        logging.Logger: 主日志记录器
    """
    logging_config = config.get("logging_config", {})
    
    # 创建日志目录
    log_dir = logging_config.get("log_dir", "./logs/")
    ensure_directory_exists(log_dir)
    
    # 配置Rich日志（如果可用）
    if HAS_RICH and logging_config.get("console_output", True):
        configure_rich_logging()
    
    # 设置主日志记录器
    logger = setup_pipeline_logger(log_dir)
    
    # 设置日志级别
    level = logging_config.get("level", "INFO")
    logger.setLevel(getattr(logging, level.upper(), logging.INFO))
    
    return logger


def display_banner():
    """显示程序横幅"""
    banner_text = """
╔══════════════════════════════════════════════════════════════╗
║                    DMS自动化流水线                           ║
║                                                              ║
║  基于现有dms_postmortem_optimised.py和cut_video_optimized   ║
║  开发的端到端自动化DMS分析流水线                             ║
║                                                              ║
║  版本: v1.0.0                                                ║
║  作者: AI Assistant                                          ║
╚══════════════════════════════════════════════════════════════╝
    """
    
    if HAS_RICH:
        console.print(Panel(
            Text(banner_text.strip(), style="bold blue"),
            title="[bold green]欢迎使用[/bold green]",
            border_style="green"
        ))
    else:
        print(banner_text)


def create_sample_config(output_path: str) -> bool:
    """
    创建示例配置文件
    
    Args:
        output_path: 输出路径
        
    Returns:
        bool: 创建是否成功
    """
    try:
        template_path = current_dir / "config" / "pipeline_config_template.json"
        
        if not template_path.exists():
            print(f"错误: 配置模板文件不存在: {template_path}")
            return False
        
        # 拷贝模板文件
        import shutil
        shutil.copy2(template_path, output_path)
        
        print(f"示例配置文件已创建: {output_path}")
        print("请根据实际情况修改配置文件中的路径和参数")
        return True
        
    except Exception as e:
        print(f"创建示例配置文件失败: {e}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="DMS自动化流水线 - 端到端自动化DMS分析处理",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 运行完整流水线
  python dms_automation_main.py --config config/my_task.json
  
  # 验证配置文件
  python dms_automation_main.py --config config/my_task.json --validate-only
  
  # 干运行（不执行实际处理）
  python dms_automation_main.py --config config/my_task.json --dry-run
  
  # 从特定阶段开始
  python dms_automation_main.py --config config/my_task.json --start-from VIDEO_PROCESSING
  
  # 重置流水线状态
  python dms_automation_main.py --config config/my_task.json --reset
  
  # 创建示例配置文件
  python dms_automation_main.py --create-config my_config.json
        """
    )
    
    parser.add_argument(
        "--config", "-c",
        required=False,
        help="配置文件路径"
    )
    
    parser.add_argument(
        "--validate-only",
        action="store_true",
        help="仅验证配置文件，不执行流水线"
    )
    
    parser.add_argument(
        "--dry-run",
        action="store_true", 
        help="干运行模式，验证配置但不执行实际处理"
    )
    
    parser.add_argument(
        "--start-from",
        choices=["VIDEO_PROCESSING", "ENV_SETUP", "REMOTE_VALIDATION", "RENDERING"],
        help="从指定阶段开始执行"
    )
    
    parser.add_argument(
        "--reset",
        action="store_true",
        help="重置流水线状态"
    )
    
    parser.add_argument(
        "--create-config",
        metavar="OUTPUT_PATH",
        help="创建示例配置文件"
    )
    
    parser.add_argument(
        "--status",
        action="store_true",
        help="显示当前流水线状态"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="详细输出模式"
    )
    
    args = parser.parse_args()
    
    # 显示横幅
    display_banner()
    
    # 创建示例配置文件
    if args.create_config:
        success = create_sample_config(args.create_config)
        return 0 if success else 1
    
    # 检查配置文件参数
    if not args.config:
        parser.error("需要指定配置文件路径 (--config) 或使用 --create-config 创建示例配置")
    
    try:
        # 加载配置
        config_manager = ConfigManager()
        config = config_manager.load_config(args.config)
        if config is None:
            return 1
        
        # 设置日志
        logger = setup_logging(config)
        logger.info("DMS自动化流水线启动")
        
        # 验证配置路径
        if not config_manager.validate_config_paths(config):
            logger.error("配置路径验证失败")
            return 1
        
        # 仅验证模式
        if args.validate_only:
            logger.info("配置文件验证通过")
            if HAS_RICH:
                console.print("[green]✅ 配置文件验证通过[/green]")
            else:
                print("✅ 配置文件验证通过")
            return 0
        
        # 创建任务编排器
        orchestrator = TaskOrchestrator(config)
        
        # 显示状态
        if args.status:
            orchestrator.display_status()
            return 0
        
        # 重置状态
        if args.reset:
            orchestrator.reset_pipeline()
            logger.info("流水线状态已重置")
            return 0
        
        # 干运行模式
        if args.dry_run:
            logger.info("干运行模式 - 配置验证通过，流水线准备就绪")
            orchestrator.display_status()
            return 0
        
        # 设置起始阶段
        if args.start_from:
            start_state = PipelineState(args.start_from)
            orchestrator.set_state(start_state)
            logger.info(f"从阶段 {args.start_from} 开始执行")
        
        # 执行流水线
        logger.info("开始执行DMS自动化流水线")
        orchestrator.display_status()
        
        # TODO: 实现完整的流水线执行逻辑
        # 这里暂时只是框架，具体的执行逻辑将在后续阶段实现
        logger.info("流水线执行完成（当前为框架版本）")
        
        return 0
        
    except KeyboardInterrupt:
        if HAS_RICH:
            console.print("\n[yellow]用户中断执行[/yellow]")
        else:
            print("\n用户中断执行")
        return 130
    
    except Exception as e:
        error_msg = f"程序执行异常: {e}"
        if 'logger' in locals():
            logger.error(error_msg)
        else:
            print(f"错误: {error_msg}")
        
        if args.verbose:
            import traceback
            traceback.print_exc()
        
        return 1


if __name__ == "__main__":
    sys.exit(main())
