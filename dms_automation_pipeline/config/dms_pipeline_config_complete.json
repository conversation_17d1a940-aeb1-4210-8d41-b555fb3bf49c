{"task_info": {"task_name": "DMS自动化分析与渲染", "task_description": "完整的DMS视频分析和渲染流水线", "version": "1.0.0", "created_by": "DMS Automation Pipeline", "created_at": "2025-07-14"}, "video_config": {"source_videos": [{"path": "/path/to/your/video1.mkv", "time_ranges": ["00:04:57-00:05:17", "00:15:10-00:15:30"], "roi": "1920:1080:0:0"}, {"path": "/path/to/your/video2.mkv", "time_ranges": ["00:02:00-00:02:30"], "roi": "1280:800:0:0"}], "output_dir": "./processed_videos/", "enable_deduplication": true, "overwrite_existing": false, "max_workers": 4, "ffmpeg_preset": "medium", "ffmpeg_crf": 23}, "environment_config": {"cpp_source_path": "../BYD_HKH_R_2.01.07.2025.07.08.4_x86/", "target_dir": "./runtime_env/", "required_files": ["test_dms_internal_postmortem", "libtx_dms.so", "calidata.json", "ip_port.json"], "model_files": ["FaceDetection.ovm", "FaceKeypoints.ovm", "eye.ovm"], "auto_set_permissions": true, "backup_existing": true}, "remote_config": {"enabled": false, "ip_port_config_path": "./runtime_env/ip_port.json", "ssh_config": {"ssh_port": 22, "username": "root", "password": null, "private_key_path": "~/.ssh/id_rsa", "connection_timeout": 30, "max_retries": 3}, "service_config": {"remote_service_path": "/userfs/tx_dms_oax_test_tool_update", "remote_model_path": "/userfs/models/", "startup_wait_time": 3, "service_check_interval": 1}, "model_sync_enabled": true, "force_restart_service": false}, "rendering_config": {"output_dir": "./rendered_videos/", "temp_dir": "./temp_rendering/", "cpp_program_path": "./test_dms_internal_postmortem", "cpp_working_dir": "./runtime_env/", "save_cpp_output": true, "max_concurrent_tasks": 2, "video_codec": "mp4v", "roi_region": [0, 0, 1280, 800], "expected_size": [1280, 800], "render_tasks": [{"task_id": "render_task_001", "video_paths": ["./processed_videos/video1_000457-000517_roi_1920_1080_0_0.mp4"], "output_path": "./rendered_videos/dms_analysis_001.mp4", "fps": 10.0, "roi_region": [0, 0, 1280, 800], "expected_size": [1280, 800], "release_tmp_source": true}]}, "logging_config": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file_output": "./logs/dms_pipeline.log", "console_output": true}, "pipeline_config": {"auto_run_full_pipeline": false, "stop_on_error": true, "save_intermediate_results": true, "cleanup_temp_files": true, "state_file": ".pipeline_state.json"}}