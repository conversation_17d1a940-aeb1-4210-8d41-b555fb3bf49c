{"pipeline_config_schema": {"type": "object", "required": ["task_info", "video_config", "environment_config"], "properties": {"task_info": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string", "minLength": 1}, "description": {"type": "string"}, "version": {"type": "string"}}}, "video_config": {"type": "object", "required": ["source_videos", "output_dir"], "properties": {"source_videos": {"type": "array", "minItems": 1, "items": {"type": "object", "required": ["path", "time_ranges"], "properties": {"path": {"type": "string", "minLength": 1}, "time_ranges": {"type": "array", "minItems": 1, "items": {"type": "string", "pattern": "^[0-9]{2}:[0-9]{2}:[0-9]{2}-[0-9]{2}:[0-9]{2}:[0-9]{2}$"}}, "roi": {"type": "string", "pattern": "^[0-9]+:[0-9]+:[0-9]+:[0-9]+$"}}}}, "output_dir": {"type": "string", "minLength": 1}, "enable_deduplication": {"type": "boolean"}, "overwrite_existing": {"type": "boolean"}, "supported_formats": {"type": "array", "items": {"type": "string"}}, "desired_fps": {"type": "number", "minimum": 1, "maximum": 60}}}, "environment_config": {"type": "object", "required": ["cpp_source_path", "target_dir", "required_files"], "properties": {"cpp_source_path": {"type": "string", "minLength": 1}, "target_dir": {"type": "string", "minLength": 1}, "required_files": {"type": "array", "minItems": 1, "items": {"type": "string", "minLength": 1}}, "model_files": {"type": "array", "items": {"type": "string", "minLength": 1}}, "auto_set_permissions": {"type": "boolean"}, "backup_existing": {"type": "boolean"}}}, "remote_config": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "ip_port_config_path": {"type": "string"}, "ssh_config": {"type": "object", "required": ["username"], "properties": {"username": {"type": "string", "minLength": 1}, "ssh_port": {"type": "integer", "minimum": 1, "maximum": 65535}, "private_key_path": {"type": "string"}, "password": {"type": ["string", "null"]}, "connection_timeout": {"type": "integer", "minimum": 1}, "max_retries": {"type": "integer", "minimum": 0}}}, "service_config": {"type": "object", "properties": {"remote_service_path": {"type": "string", "minLength": 1}, "remote_model_path": {"type": "string", "minLength": 1}, "startup_wait_time": {"type": "number", "minimum": 0}, "service_check_interval": {"type": "number", "minimum": 0.1}}}, "model_sync_enabled": {"type": "boolean"}, "force_restart_service": {"type": "boolean"}}}, "rendering_config": {"type": "object", "properties": {"output_video_path": {"type": "string", "minLength": 1}, "cleanup_temp_files": {"type": "boolean"}, "show_eye_flag": {"type": "boolean"}, "desired_fps": {"type": "number", "minimum": 1, "maximum": 60}, "video_quality": {"type": "object", "properties": {"preset": {"type": "string", "enum": ["ultrafast", "superfast", "veryfast", "faster", "fast", "medium", "slow", "slower", "veryslow"]}, "crf": {"type": "integer", "minimum": 0, "maximum": 51}}}, "parallel_processing": {"type": "boolean"}, "max_workers": {"type": "integer", "minimum": 1}}}, "logging_config": {"type": "object", "properties": {"level": {"type": "string", "enum": ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]}, "log_dir": {"type": "string"}, "console_output": {"type": "boolean"}, "file_output": {"type": "boolean"}, "max_file_size_mb": {"type": "number", "minimum": 1}, "backup_count": {"type": "integer", "minimum": 0}}}, "advanced_config": {"type": "object", "properties": {"max_memory_usage_percent": {"type": "number", "minimum": 10, "maximum": 95}, "disk_space_check_mb": {"type": "number", "minimum": 100}, "progress_update_interval": {"type": "number", "minimum": 0.1}, "state_save_interval": {"type": "number", "minimum": 1}, "enable_performance_monitoring": {"type": "boolean"}}}}}, "ip_port_schema": {"type": "object", "required": ["ip", "port"], "properties": {"ip": {"type": "string", "pattern": "^(?:[0-9]{1,3}\\.){3}[0-9]{1,3}$"}, "port": {"type": "integer", "minimum": 1, "maximum": 65535}}}, "calidata_schema": {"type": "object", "required": ["head_yaw", "head_pitch", "head_roll"], "properties": {"head_yaw": {"type": "number"}, "head_pitch": {"type": "number"}, "head_roll": {"type": "number"}, "left_eye_yaw": {"type": "number"}, "left_eye_pitch": {"type": "number"}, "right_eye_yaw": {"type": "number"}, "right_eye_pitch": {"type": "number"}, "left_eye_curve_mean": {"type": "number"}, "right_eye_curve_mean": {"type": "number"}}}}