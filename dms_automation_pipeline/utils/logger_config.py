"""
日志配置模块
提供统一的日志配置和管理功能
"""

import os
import logging
import logging.handlers
from typing import Optional, Dict, Any
from datetime import datetime


def setup_logger(name: str = "DMS_Automation", 
                level: str = "INFO",
                log_file: Optional[str] = None,
                console_output: bool = True,
                max_file_size: int = 10 * 1024 * 1024,  # 10MB
                backup_count: int = 5) -> logging.Logger:
    """
    设置日志记录器
    
    Args:
        name: 日志记录器名称
        level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: 日志文件路径，None表示不写文件
        console_output: 是否输出到控制台
        max_file_size: 日志文件最大大小（字节）
        backup_count: 备份文件数量
        
    Returns:
        logging.Logger: 配置好的日志记录器
    """
    # 创建日志记录器
    logger = logging.getLogger(name)
    
    # 避免重复添加处理器
    if logger.handlers:
        return logger
    
    # 设置日志级别
    log_level = getattr(logging, level.upper(), logging.INFO)
    logger.setLevel(log_level)
    
    # 创建格式化器
    formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台处理器
    if console_output:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(log_level)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    # 文件处理器
    if log_file:
        # 确保日志目录存在
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
        
        # 使用RotatingFileHandler实现日志轮转
        file_handler = logging.handlers.RotatingFileHandler(
            filename=log_file,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


def setup_pipeline_logger(log_dir: str = "logs") -> logging.Logger:
    """
    设置流水线专用日志记录器
    
    Args:
        log_dir: 日志目录
        
    Returns:
        logging.Logger: 流水线日志记录器
    """
    # 创建日志文件名（包含时间戳）
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"dms_pipeline_{timestamp}.log")
    
    return setup_logger(
        name="DMS_Pipeline",
        level="INFO",
        log_file=log_file,
        console_output=True
    )


def setup_component_logger(component_name: str, log_dir: str = "logs") -> logging.Logger:
    """
    设置组件专用日志记录器
    
    Args:
        component_name: 组件名称
        log_dir: 日志目录
        
    Returns:
        logging.Logger: 组件日志记录器
    """
    log_file = os.path.join(log_dir, f"{component_name.lower()}.log")
    
    return setup_logger(
        name=f"DMS_{component_name}",
        level="DEBUG",
        log_file=log_file,
        console_output=False  # 组件日志只写文件
    )


class LoggerContext:
    """日志上下文管理器，用于临时改变日志级别"""
    
    def __init__(self, logger: logging.Logger, level: str):
        self.logger = logger
        self.new_level = getattr(logging, level.upper(), logging.INFO)
        self.old_level = None
    
    def __enter__(self):
        self.old_level = self.logger.level
        self.logger.setLevel(self.new_level)
        return self.logger
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.old_level is not None:
            self.logger.setLevel(self.old_level)


def log_function_call(logger: logging.Logger):
    """
    装饰器：记录函数调用
    
    Args:
        logger: 日志记录器
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            logger.debug(f"调用函数: {func.__name__}, 参数: args={args}, kwargs={kwargs}")
            try:
                result = func(*args, **kwargs)
                logger.debug(f"函数 {func.__name__} 执行成功")
                return result
            except Exception as e:
                logger.error(f"函数 {func.__name__} 执行失败: {e}")
                raise
        return wrapper
    return decorator


def log_execution_time(logger: logging.Logger):
    """
    装饰器：记录函数执行时间
    
    Args:
        logger: 日志记录器
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            import time
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                end_time = time.time()
                execution_time = end_time - start_time
                logger.info(f"函数 {func.__name__} 执行时间: {execution_time:.3f}秒")
                return result
            except Exception as e:
                end_time = time.time()
                execution_time = end_time - start_time
                logger.error(f"函数 {func.__name__} 执行失败 (耗时 {execution_time:.3f}秒): {e}")
                raise
        return wrapper
    return decorator


class ProgressLogger:
    """进度日志记录器"""
    
    def __init__(self, logger: logging.Logger, total_steps: int, log_interval: int = 10):
        """
        初始化进度日志记录器
        
        Args:
            logger: 日志记录器
            total_steps: 总步数
            log_interval: 日志记录间隔（每多少步记录一次）
        """
        self.logger = logger
        self.total_steps = total_steps
        self.log_interval = log_interval
        self.current_step = 0
        self.start_time = datetime.now()
    
    def update(self, step: int = 1, message: str = ""):
        """
        更新进度
        
        Args:
            step: 步数增量
            message: 附加消息
        """
        self.current_step += step
        
        if self.current_step % self.log_interval == 0 or self.current_step == self.total_steps:
            progress_percent = (self.current_step / self.total_steps) * 100
            elapsed_time = datetime.now() - self.start_time
            
            log_message = f"进度: {self.current_step}/{self.total_steps} ({progress_percent:.1f}%)"
            if message:
                log_message += f" - {message}"
            log_message += f" [耗时: {elapsed_time}]"
            
            self.logger.info(log_message)
    
    def finish(self, message: str = "完成"):
        """
        完成进度记录
        
        Args:
            message: 完成消息
        """
        total_time = datetime.now() - self.start_time
        self.logger.info(f"{message} - 总耗时: {total_time}")


def configure_rich_logging():
    """配置Rich库的日志美化显示"""
    try:
        from rich.logging import RichHandler
        from rich.console import Console
        
        console = Console()
        
        # 创建Rich处理器
        rich_handler = RichHandler(
            console=console,
            show_time=True,
            show_level=True,
            show_path=True,
            markup=True
        )
        
        # 设置格式
        rich_handler.setFormatter(
            logging.Formatter(
                fmt="%(message)s",
                datefmt="[%X]"
            )
        )
        
        # 获取根日志记录器并添加Rich处理器
        root_logger = logging.getLogger()
        
        # 移除默认处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        root_logger.addHandler(rich_handler)
        root_logger.setLevel(logging.INFO)
        
        return True
        
    except ImportError:
        # Rich库未安装，使用标准日志
        return False


def get_log_summary(log_file: str) -> Dict[str, Any]:
    """
    获取日志文件摘要信息
    
    Args:
        log_file: 日志文件路径
        
    Returns:
        Dict[str, Any]: 日志摘要信息
    """
    summary = {
        "total_lines": 0,
        "error_count": 0,
        "warning_count": 0,
        "info_count": 0,
        "debug_count": 0,
        "file_size": 0,
        "last_modified": None
    }
    
    try:
        if not os.path.exists(log_file):
            return summary
        
        # 文件信息
        stat = os.stat(log_file)
        summary["file_size"] = stat.st_size
        summary["last_modified"] = datetime.fromtimestamp(stat.st_mtime).isoformat()
        
        # 分析日志内容
        with open(log_file, 'r', encoding='utf-8') as f:
            for line in f:
                summary["total_lines"] += 1
                
                if " - ERROR - " in line:
                    summary["error_count"] += 1
                elif " - WARNING - " in line:
                    summary["warning_count"] += 1
                elif " - INFO - " in line:
                    summary["info_count"] += 1
                elif " - DEBUG - " in line:
                    summary["debug_count"] += 1
        
        return summary
        
    except Exception as e:
        summary["error"] = str(e)
        return summary
