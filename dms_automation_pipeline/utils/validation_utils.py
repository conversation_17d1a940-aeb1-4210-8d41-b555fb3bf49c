"""
验证工具模块
提供路径验证、时间格式验证、配置验证等功能
"""

import os
import re
import json
import logging
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path


logger = logging.getLogger(__name__)


def validate_time_format(time_str: str) -> bool:
    """
    验证时间格式 HH:MM:SS
    
    Args:
        time_str: 时间字符串
        
    Returns:
        bool: 格式是否正确
    """
    try:
        pattern = r'^([0-1]?[0-9]|2[0-3]):([0-5]?[0-9]):([0-5]?[0-9])$'
        if not re.match(pattern, time_str):
            return False
        
        parts = time_str.split(':')
        if len(parts) != 3:
            return False
        
        h, m, s = map(int, parts)
        return 0 <= h <= 23 and 0 <= m <= 59 and 0 <= s <= 59
        
    except (ValueError, AttributeError):
        return False


def validate_path(path: str, must_exist: bool = True, must_be_file: bool = False, 
                 must_be_dir: bool = False) -> Tuple[bool, str]:
    """
    验证路径有效性
    
    Args:
        path: 路径字符串
        must_exist: 路径必须存在
        must_be_file: 必须是文件
        must_be_dir: 必须是目录
        
    Returns:
        Tuple[bool, str]: (是否有效, 错误信息)
    """
    try:
        if not path or not isinstance(path, str):
            return False, "路径不能为空"
        
        path = os.path.expanduser(path)  # 展开 ~ 符号
        
        if must_exist and not os.path.exists(path):
            return False, f"路径不存在: {path}"
        
        if must_be_file and os.path.exists(path) and not os.path.isfile(path):
            return False, f"路径不是文件: {path}"
        
        if must_be_dir and os.path.exists(path) and not os.path.isdir(path):
            return False, f"路径不是目录: {path}"
        
        return True, ""
        
    except Exception as e:
        return False, f"路径验证失败: {e}"


def validate_video_file(file_path: str, supported_formats: List[str] = None) -> Tuple[bool, str]:
    """
    验证视频文件
    
    Args:
        file_path: 视频文件路径
        supported_formats: 支持的格式列表
        
    Returns:
        Tuple[bool, str]: (是否有效, 错误信息)
    """
    if supported_formats is None:
        supported_formats = ['.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv']
    
    # 路径验证
    valid, error = validate_path(file_path, must_exist=True, must_be_file=True)
    if not valid:
        return False, error
    
    # 格式验证
    file_ext = Path(file_path).suffix.lower()
    if file_ext not in supported_formats:
        return False, f"不支持的视频格式: {file_ext}, 支持格式: {supported_formats}"
    
    return True, ""


def validate_roi_format(roi_str: str) -> Tuple[bool, str]:
    """
    验证ROI格式 width:height:x:y
    
    Args:
        roi_str: ROI字符串
        
    Returns:
        Tuple[bool, str]: (是否有效, 错误信息)
    """
    try:
        if not roi_str:
            return True, ""  # ROI是可选的
        
        parts = roi_str.split(':')
        if len(parts) != 4:
            return False, "ROI格式错误，应为 width:height:x:y"
        
        w, h, x, y = map(int, parts)
        
        if w <= 0 or h <= 0:
            return False, "ROI宽度和高度必须大于0"
        
        if x < 0 or y < 0:
            return False, "ROI坐标不能为负数"
        
        return True, ""
        
    except ValueError:
        return False, "ROI参数必须是整数"
    except Exception as e:
        return False, f"ROI验证失败: {e}"


def validate_ip_address(ip: str) -> Tuple[bool, str]:
    """
    验证IP地址格式
    
    Args:
        ip: IP地址字符串
        
    Returns:
        Tuple[bool, str]: (是否有效, 错误信息)
    """
    try:
        import ipaddress
        ipaddress.ip_address(ip)
        return True, ""
        
    except ValueError:
        return False, f"无效的IP地址: {ip}"
    except Exception as e:
        return False, f"IP地址验证失败: {e}"


def validate_port_number(port: int) -> Tuple[bool, str]:
    """
    验证端口号
    
    Args:
        port: 端口号
        
    Returns:
        Tuple[bool, str]: (是否有效, 错误信息)
    """
    try:
        if not isinstance(port, int):
            return False, "端口号必须是整数"
        
        if port < 1 or port > 65535:
            return False, "端口号必须在1-65535范围内"
        
        return True, ""
        
    except Exception as e:
        return False, f"端口号验证失败: {e}"


def validate_json_file(file_path: str, required_keys: List[str] = None) -> Tuple[bool, str, Optional[Dict]]:
    """
    验证JSON文件格式和内容
    
    Args:
        file_path: JSON文件路径
        required_keys: 必需的键列表
        
    Returns:
        Tuple[bool, str, Optional[Dict]]: (是否有效, 错误信息, 解析后的数据)
    """
    # 文件存在性验证
    valid, error = validate_path(file_path, must_exist=True, must_be_file=True)
    if not valid:
        return False, error, None
    
    try:
        # JSON格式验证
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 必需键验证
        if required_keys:
            missing_keys = [key for key in required_keys if key not in data]
            if missing_keys:
                return False, f"缺少必需的键: {missing_keys}", None
        
        return True, "", data
        
    except json.JSONDecodeError as e:
        return False, f"JSON格式错误: {e}", None
    except Exception as e:
        return False, f"JSON文件验证失败: {e}", None


def validate_config_structure(config: Dict[str, Any], schema: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """
    验证配置结构
    
    Args:
        config: 配置字典
        schema: 验证模式
        
    Returns:
        Tuple[bool, List[str]]: (是否有效, 错误信息列表)
    """
    errors = []
    
    def validate_recursive(data: Any, schema_part: Any, path: str = ""):
        if isinstance(schema_part, dict):
            if not isinstance(data, dict):
                errors.append(f"{path}: 期望字典类型，实际为 {type(data).__name__}")
                return
            
            # 检查必需字段
            required = schema_part.get('required', [])
            for req_field in required:
                if req_field not in data:
                    errors.append(f"{path}.{req_field}: 缺少必需字段")
            
            # 递归验证字段
            properties = schema_part.get('properties', {})
            for key, value in data.items():
                if key in properties:
                    validate_recursive(value, properties[key], f"{path}.{key}" if path else key)
        
        elif isinstance(schema_part, list):
            if not isinstance(data, list):
                errors.append(f"{path}: 期望列表类型，实际为 {type(data).__name__}")
                return
            
            # 验证列表元素
            if schema_part and len(data) > 0:
                element_schema = schema_part[0]
                for i, item in enumerate(data):
                    validate_recursive(item, element_schema, f"{path}[{i}]")
        
        elif isinstance(schema_part, type):
            if not isinstance(data, schema_part):
                errors.append(f"{path}: 期望 {schema_part.__name__} 类型，实际为 {type(data).__name__}")
    
    try:
        validate_recursive(config, schema)
        return len(errors) == 0, errors
        
    except Exception as e:
        return False, [f"配置验证异常: {e}"]


def validate_disk_space(path: str, required_mb: int) -> Tuple[bool, str]:
    """
    验证磁盘空间是否足够
    
    Args:
        path: 路径
        required_mb: 需要的空间（MB）
        
    Returns:
        Tuple[bool, str]: (空间是否足够, 信息)
    """
    try:
        from .file_utils import get_disk_usage
        
        usage = get_disk_usage(path)
        if usage is None:
            return False, f"无法获取磁盘使用情况: {path}"
        
        total, used, free = usage
        free_mb = free / (1024 * 1024)
        
        if free_mb < required_mb:
            return False, f"磁盘空间不足: 需要 {required_mb}MB，可用 {free_mb:.1f}MB"
        
        return True, f"磁盘空间充足: 可用 {free_mb:.1f}MB"
        
    except Exception as e:
        return False, f"磁盘空间检查失败: {e}"


def validate_executable_permissions(file_path: str) -> Tuple[bool, str]:
    """
    验证文件是否有可执行权限
    
    Args:
        file_path: 文件路径
        
    Returns:
        Tuple[bool, str]: (是否有可执行权限, 信息)
    """
    try:
        valid, error = validate_path(file_path, must_exist=True, must_be_file=True)
        if not valid:
            return False, error
        
        if os.access(file_path, os.X_OK):
            return True, "文件有可执行权限"
        else:
            return False, f"文件没有可执行权限: {file_path}"
        
    except Exception as e:
        return False, f"权限检查失败: {e}"


def validate_time_range(start_time: str, end_time: str) -> Tuple[bool, str]:
    """
    验证时间范围
    
    Args:
        start_time: 开始时间
        end_time: 结束时间
        
    Returns:
        Tuple[bool, str]: (是否有效, 错误信息)
    """
    # 验证时间格式
    if not validate_time_format(start_time):
        return False, f"开始时间格式错误: {start_time}"
    
    if not validate_time_format(end_time):
        return False, f"结束时间格式错误: {end_time}"
    
    # 转换为秒数比较
    def time_to_seconds(time_str: str) -> int:
        h, m, s = map(int, time_str.split(':'))
        return h * 3600 + m * 60 + s
    
    start_seconds = time_to_seconds(start_time)
    end_seconds = time_to_seconds(end_time)
    
    if start_seconds >= end_seconds:
        return False, f"开始时间必须早于结束时间: {start_time} >= {end_time}"
    
    return True, ""
