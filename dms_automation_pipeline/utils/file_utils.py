"""
文件操作工具模块
提供文件拷贝、权限设置、MD5计算等功能
"""

import os
import shutil
import hashlib
import stat
import logging
from typing import Optional, List, Tuple
from pathlib import Path


logger = logging.getLogger(__name__)


def copy_with_permissions(src: str, dst: str, preserve_permissions: bool = True) -> bool:
    """
    拷贝文件并保持权限
    
    Args:
        src: 源文件路径
        dst: 目标文件路径
        preserve_permissions: 是否保持原文件权限
        
    Returns:
        bool: 拷贝是否成功
    """
    try:
        # 检查源文件是否存在
        if not os.path.exists(src):
            logger.error(f"源文件不存在: {src}")
            return False
        
        # 创建目标目录
        dst_dir = os.path.dirname(dst)
        if dst_dir and not os.path.exists(dst_dir):
            os.makedirs(dst_dir, exist_ok=True)
        
        # 拷贝文件
        if preserve_permissions:
            shutil.copy2(src, dst)  # 保持元数据
        else:
            shutil.copy(src, dst)   # 只拷贝内容
        
        logger.info(f"文件拷贝成功: {src} -> {dst}")
        return True
        
    except Exception as e:
        logger.error(f"文件拷贝失败: {src} -> {dst}, 错误: {e}")
        return False


def set_executable_permission(file_path: str) -> bool:
    """
    设置文件为可执行权限
    
    Args:
        file_path: 文件路径
        
    Returns:
        bool: 设置是否成功
    """
    try:
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            return False
        
        # 获取当前权限
        current_permissions = os.stat(file_path).st_mode
        
        # 添加可执行权限
        new_permissions = current_permissions | stat.S_IXUSR | stat.S_IXGRP | stat.S_IXOTH
        
        # 设置新权限
        os.chmod(file_path, new_permissions)
        
        logger.info(f"可执行权限设置成功: {file_path}")
        return True
        
    except Exception as e:
        logger.error(f"设置可执行权限失败: {file_path}, 错误: {e}")
        return False


def calculate_md5(file_path: str, chunk_size: int = 8192) -> Optional[str]:
    """
    计算文件MD5哈希值
    
    Args:
        file_path: 文件路径
        chunk_size: 读取块大小
        
    Returns:
        str: MD5哈希值，失败返回None
    """
    try:
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            return None
        
        md5_hash = hashlib.md5()
        
        with open(file_path, 'rb') as f:
            while chunk := f.read(chunk_size):
                md5_hash.update(chunk)
        
        result = md5_hash.hexdigest()
        logger.debug(f"MD5计算完成: {file_path} -> {result}")
        return result
        
    except Exception as e:
        logger.error(f"MD5计算失败: {file_path}, 错误: {e}")
        return None


def ensure_directory_exists(dir_path: str) -> bool:
    """
    确保目录存在，不存在则创建
    
    Args:
        dir_path: 目录路径
        
    Returns:
        bool: 操作是否成功
    """
    try:
        if not os.path.exists(dir_path):
            os.makedirs(dir_path, exist_ok=True)
            logger.info(f"目录创建成功: {dir_path}")
        return True
        
    except Exception as e:
        logger.error(f"目录创建失败: {dir_path}, 错误: {e}")
        return False


def get_file_size(file_path: str) -> Optional[int]:
    """
    获取文件大小（字节）
    
    Args:
        file_path: 文件路径
        
    Returns:
        int: 文件大小，失败返回None
    """
    try:
        if not os.path.exists(file_path):
            return None
        
        return os.path.getsize(file_path)
        
    except Exception as e:
        logger.error(f"获取文件大小失败: {file_path}, 错误: {e}")
        return None


def list_files_with_extension(directory: str, extensions: List[str]) -> List[str]:
    """
    列出目录中指定扩展名的文件
    
    Args:
        directory: 目录路径
        extensions: 扩展名列表，如 ['.py', '.json']
        
    Returns:
        List[str]: 文件路径列表
    """
    try:
        if not os.path.exists(directory):
            logger.error(f"目录不存在: {directory}")
            return []
        
        files = []
        for root, dirs, filenames in os.walk(directory):
            for filename in filenames:
                file_path = os.path.join(root, filename)
                file_ext = os.path.splitext(filename)[1].lower()
                
                if file_ext in extensions:
                    files.append(file_path)
        
        return files
        
    except Exception as e:
        logger.error(f"列出文件失败: {directory}, 错误: {e}")
        return []


def clean_temp_files(temp_dir: str, patterns: List[str] = None) -> Tuple[int, int]:
    """
    清理临时文件
    
    Args:
        temp_dir: 临时文件目录
        patterns: 文件模式列表，如 ['*.tmp', '*.log']
        
    Returns:
        Tuple[int, int]: (清理文件数量, 释放空间字节数)
    """
    import glob
    
    if patterns is None:
        patterns = ['*.tmp', '*.temp', '*.log', '*~']
    
    cleaned_count = 0
    freed_space = 0
    
    try:
        if not os.path.exists(temp_dir):
            return 0, 0
        
        for pattern in patterns:
            pattern_path = os.path.join(temp_dir, pattern)
            for file_path in glob.glob(pattern_path):
                try:
                    file_size = get_file_size(file_path) or 0
                    os.remove(file_path)
                    cleaned_count += 1
                    freed_space += file_size
                    logger.debug(f"删除临时文件: {file_path}")
                except Exception as e:
                    logger.warning(f"删除文件失败: {file_path}, 错误: {e}")
        
        logger.info(f"临时文件清理完成: 删除 {cleaned_count} 个文件，释放 {freed_space} 字节")
        return cleaned_count, freed_space
        
    except Exception as e:
        logger.error(f"清理临时文件失败: {temp_dir}, 错误: {e}")
        return 0, 0


def backup_file(file_path: str, backup_suffix: str = ".bak") -> Optional[str]:
    """
    备份文件
    
    Args:
        file_path: 原文件路径
        backup_suffix: 备份文件后缀
        
    Returns:
        str: 备份文件路径，失败返回None
    """
    try:
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            return None
        
        backup_path = file_path + backup_suffix
        
        # 如果备份文件已存在，添加时间戳
        if os.path.exists(backup_path):
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"{file_path}.{timestamp}{backup_suffix}"
        
        shutil.copy2(file_path, backup_path)
        logger.info(f"文件备份成功: {file_path} -> {backup_path}")
        return backup_path
        
    except Exception as e:
        logger.error(f"文件备份失败: {file_path}, 错误: {e}")
        return None


def compare_files_md5(file1: str, file2: str) -> bool:
    """
    通过MD5比较两个文件是否相同
    
    Args:
        file1: 文件1路径
        file2: 文件2路径
        
    Returns:
        bool: 文件是否相同
    """
    md5_1 = calculate_md5(file1)
    md5_2 = calculate_md5(file2)
    
    if md5_1 is None or md5_2 is None:
        return False
    
    return md5_1 == md5_2


def get_disk_usage(path: str) -> Optional[Tuple[int, int, int]]:
    """
    获取磁盘使用情况
    
    Args:
        path: 路径
        
    Returns:
        Tuple[int, int, int]: (总空间, 已用空间, 可用空间) 字节数，失败返回None
    """
    try:
        statvfs = os.statvfs(path)
        
        # 计算字节数
        total = statvfs.f_frsize * statvfs.f_blocks
        used = statvfs.f_frsize * (statvfs.f_blocks - statvfs.f_available)
        free = statvfs.f_frsize * statvfs.f_available
        
        return total, used, free
        
    except Exception as e:
        logger.error(f"获取磁盘使用情况失败: {path}, 错误: {e}")
        return None
