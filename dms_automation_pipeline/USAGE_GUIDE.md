# DMS自动化平台使用指南

## 🚀 快速开始

### 第一步：环境检查

确保您的系统已安装必要的依赖：

```bash
# 检查Python版本（需要3.7+）
python3 --version

# 检查FFmpeg（用于视频处理）
ffmpeg -version

# 安装Python依赖
pip install rich opencv-python paramiko
```

### 第二步：创建配置文件

```bash
cd dms_automation_pipeline/
python dms_automation_main.py --create-config my_config.json
```

### 第三步：编辑配置文件

编辑 `my_config.json`，根据您的实际情况修改以下关键配置：

```json
{
  "video_config": {
    "source_videos": [
      {
        "path": "/path/to/your/video.mkv",
        "time_ranges": ["00:04:57-00:05:17"],
        "roi": "1920:1080:0:0"
      }
    ],
    "output_dir": "./processed_videos/"
  },
  "environment_config": {
    "cpp_source_path": "../BYD_HKH_R_2.01.07.2025.07.08.4_x86/",
    "target_dir": "./runtime_env/"
  }
}
```

### 第四步：验证配置

```bash
python dms_automation_main.py --config my_config.json --validate-only
```

### 第五步：运行流水线

```bash
python dms_automation_main.py --config my_config.json
```

## 📋 详细配置说明

### 视频配置 (video_config)

```json
{
  "video_config": {
    "source_videos": [
      {
        "path": "/absolute/path/to/video.mkv",
        "time_ranges": ["00:04:57-00:05:17", "00:15:10-00:15:30"],
        "roi": "1920:1080:0:0"
      }
    ],
    "output_dir": "./processed_videos/",
    "enable_deduplication": true,
    "max_workers": 4,
    "ffmpeg_preset": "medium",
    "ffmpeg_crf": 23
  }
}
```

**重要说明：**
- `path`: 必须是绝对路径
- `time_ranges`: 格式为 "HH:MM:SS-HH:MM:SS"
- `roi`: 格式为 "width:height:x:y"

### 环境配置 (environment_config)

```json
{
  "environment_config": {
    "cpp_source_path": "../BYD_HKH_R_2.01.07.2025.07.08.4_x86/",
    "target_dir": "./runtime_env/",
    "required_files": [
      "test_dms_internal_postmortem",
      "libtx_dms.so",
      "calidata.json",
      "ip_port.json"
    ],
    "model_files": [
      "FaceDetection.ovm",
      "FaceKeypoints.ovm",
      "eye.ovm"
    ]
  }
}
```

### 远程配置 (remote_config)

```json
{
  "remote_config": {
    "enabled": true,
    "ip_port_config_path": "./runtime_env/ip_port.json",
    "ssh_config": {
      "username": "root",
      "private_key_path": "~/.ssh/id_rsa"
    }
  }
}
```

## 🔧 分阶段执行

您可以单独执行某个阶段：

```bash
# 仅执行视频预处理
python dms_automation_main.py --config my_config.json --stage video_processing

# 仅执行环境设置
python dms_automation_main.py --config my_config.json --stage env_setup

# 仅执行远程验证
python dms_automation_main.py --config my_config.json --stage remote_validation

# 仅执行渲染
python dms_automation_main.py --config my_config.json --stage rendering
```

## 🐛 常见问题排除

### 问题1：FFmpeg处理失败

**错误信息：** `FFmpeg处理失败，返回码: 1`

**解决方案：**
1. 检查视频文件是否存在且可读
2. 检查时间范围格式是否正确
3. 确保FFmpeg已正确安装

```bash
# 检查视频文件
ls -la /path/to/your/video.mkv

# 测试FFmpeg
ffmpeg -i /path/to/your/video.mkv -t 5 test_output.mp4
```

### 问题2：配置文件验证失败

**错误信息：** `配置文件 ip_port.json 缺少必需键: ['ip', 'port']`

**解决方案：**
确保 `runtime_env/ip_port.json` 包含正确格式：

```json
{
  "ip": "***********",
  "port": 1180
}
```

### 问题3：C++程序执行失败

**错误信息：** `C++程序没有可执行权限`

**解决方案：**
```bash
chmod +x ./runtime_env/test_dms_internal_postmortem
```

### 问题4：SSH连接失败

**错误信息：** `SSH连接失败`

**解决方案：**
1. 检查网络连通性
2. 验证SSH密钥
3. 确认远程服务器状态

```bash
# 测试网络连通性
ping ***********

# 测试SSH连接
ssh -i ~/.ssh/id_rsa root@***********
```

## 📊 状态监控

系统提供美观的状态显示：

```
╭──────────────────────────────────────────────────────────────────────────── 当前状态: RENDERING ────────────────────────────────────────────────────────────────────────────╮
│            DMS自动化流水线状态                                                                                                                                              │
│ ┏━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━┓                                                                                                                                  │
│ ┃ 阶段                 ┃ 状态            ┃                                                                                                                                  │
│ ┡━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━┩                                                                                                                                  │
│ │ VIDEO_PROCESSING     │ ✅ 成功         │                                                                                                                                  │
│ │ ENV_SETUP            │ ✅ 成功         │                                                                                                                                  │
│ │ REMOTE_VALIDATION    │ ✅ 成功         │                                                                                                                                  │
│ │ RENDERING            │ 🔄 进行中       │                                                                                                                                  │
│ └──────────────────────┴─────────────────┘                                                                                                                                  │
╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯
```

## 📝 日志分析

查看详细日志：

```bash
# 实时查看日志
tail -f logs/dms_pipeline.log

# 搜索错误信息
grep "ERROR" logs/dms_pipeline.log

# 查看特定阶段日志
grep "VIDEO_PROCESSING" logs/dms_pipeline.log
```

## 🎯 最佳实践

### 1. 配置文件管理
- 为不同项目创建不同的配置文件
- 使用版本控制管理配置文件
- 定期备份重要配置

### 2. 目录结构建议
```
project/
├── dms_automation_pipeline/    # 主程序目录
├── configs/                    # 配置文件目录
│   ├── project1_config.json
│   └── project2_config.json
├── videos/                     # 源视频目录
├── processed/                  # 处理后视频
└── results/                    # 最终结果
```

### 3. 性能优化
- 根据机器性能调整 `max_workers`
- 使用SSD存储临时文件
- 定期清理临时目录

### 4. 安全建议
- 使用SSH密钥而非密码
- 定期更新依赖库
- 限制网络访问权限

## 🆘 获取帮助

如果遇到问题：

1. **查看帮助信息**
   ```bash
   python dms_automation_main.py --help
   ```

2. **检查系统状态**
   ```bash
   python -c "from core.task_orchestrator import TaskOrchestrator; print('系统正常')"
   ```

3. **验证依赖**
   ```bash
   python -c "import cv2, rich, paramiko; print('依赖正常')"
   ```

4. **查看版本信息**
   ```bash
   python dms_automation_main.py --version
   ```

## 📞 技术支持

- **文档**: README.md
- **配置模板**: config/dms_pipeline_config_complete.json
- **示例配置**: 使用 `--create-config` 生成
