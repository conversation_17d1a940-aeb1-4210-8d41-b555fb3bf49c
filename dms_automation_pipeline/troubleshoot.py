#!/usr/bin/env python3
"""
DMS自动化平台故障排除工具
自动检测和诊断常见问题

使用方法:
    python troubleshoot.py
    python troubleshoot.py --config my_config.json
    python troubleshoot.py --check-deps
"""

import os
import sys
import json
import subprocess
import argparse
from pathlib import Path

# 尝试导入Rich库
try:
    from rich.console import Console
    from rich.panel import Panel
    from rich.table import Table
    from rich.text import Text
    console = Console()
    HAS_RICH = True
except ImportError:
    console = None
    HAS_RICH = False


def print_header():
    """显示工具标题"""
    header = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    DMS自动化平台故障排除工具                                 ║
║                                                                              ║
║  🔍 自动检测和诊断常见问题                                                   ║
║  🛠️ 提供详细的解决方案                                                      ║
║  📊 生成系统健康报告                                                        ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    
    if HAS_RICH:
        console.print(Panel(
            Text(header.strip(), style="bold cyan"),
            border_style="blue"
        ))
    else:
        print(header)


def check_python_version():
    """检查Python版本"""
    print("\n🐍 检查Python版本...")
    
    version = sys.version_info
    if version.major >= 3 and version.minor >= 7:
        print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro} (满足要求)")
        return True
    else:
        print(f"❌ Python版本: {version.major}.{version.minor}.{version.micro} (需要3.7+)")
        print("💡 解决方案: 升级Python到3.7或更高版本")
        return False


def check_dependencies():
    """检查Python依赖"""
    print("\n📦 检查Python依赖...")
    
    dependencies = [
        ('rich', 'Rich库 - 美观界面显示'),
        ('cv2', 'OpenCV - 视频处理'),
        ('paramiko', 'Paramiko - SSH连接')
    ]
    
    all_ok = True
    for module, description in dependencies:
        try:
            __import__(module)
            print(f"✅ {description}")
        except ImportError:
            print(f"❌ {description} - 未安装")
            print(f"💡 解决方案: pip install {module if module != 'cv2' else 'opencv-python'}")
            all_ok = False
    
    return all_ok


def check_ffmpeg():
    """检查FFmpeg"""
    print("\n🎬 检查FFmpeg...")
    
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"✅ FFmpeg已安装: {version_line}")
            return True
        else:
            print("❌ FFmpeg执行失败")
            return False
    except FileNotFoundError:
        print("❌ FFmpeg未安装")
        print("💡 解决方案:")
        print("  Ubuntu/Debian: sudo apt install ffmpeg")
        print("  CentOS/RHEL: sudo yum install ffmpeg")
        print("  macOS: brew install ffmpeg")
        return False
    except subprocess.TimeoutExpired:
        print("❌ FFmpeg响应超时")
        return False


def check_core_modules():
    """检查核心模块"""
    print("\n🔧 检查核心模块...")
    
    modules = [
        'core.task_orchestrator',
        'core.video_preprocessor',
        'core.environment_manager',
        'core.remote_validator',
        'core.rendering_engine'
    ]
    
    all_ok = True
    for module in modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module} - 导入失败: {e}")
            all_ok = False
    
    return all_ok


def check_config_file(config_path):
    """检查配置文件"""
    print(f"\n📋 检查配置文件: {config_path}")
    
    if not os.path.exists(config_path):
        print(f"❌ 配置文件不存在: {config_path}")
        print("💡 解决方案: python dms_automation_main.py --create-config my_config.json")
        return False
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        print("✅ 配置文件格式正确")
        
        # 检查必需的配置部分
        required_sections = ['video_config', 'environment_config', 'rendering_config']
        missing_sections = []
        
        for section in required_sections:
            if section not in config:
                missing_sections.append(section)
        
        if missing_sections:
            print(f"❌ 缺少配置部分: {missing_sections}")
            return False
        else:
            print("✅ 配置部分完整")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ 配置文件JSON格式错误: {e}")
        print("💡 解决方案: 检查JSON语法，确保括号和引号匹配")
        return False


def check_video_files(config_path):
    """检查视频文件"""
    print(f"\n🎬 检查视频文件...")
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        video_config = config.get('video_config', {})
        source_videos = video_config.get('source_videos', [])
        
        if not source_videos:
            print("⚠️ 没有配置源视频文件")
            return True
        
        all_ok = True
        for i, video_info in enumerate(source_videos):
            video_path = video_info.get('path', '')
            print(f"  检查视频 {i+1}: {video_path}")
            
            if not video_path:
                print(f"    ❌ 视频路径为空")
                all_ok = False
                continue
            
            if not os.path.exists(video_path):
                print(f"    ❌ 视频文件不存在")
                all_ok = False
                continue
            
            if not os.access(video_path, os.R_OK):
                print(f"    ❌ 视频文件无读取权限")
                all_ok = False
                continue
            
            print(f"    ✅ 视频文件正常")
        
        return all_ok
        
    except Exception as e:
        print(f"❌ 检查视频文件时出错: {e}")
        return False


def check_cpp_environment(config_path):
    """检查C++环境"""
    print(f"\n🔧 检查C++环境...")
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        env_config = config.get('environment_config', {})
        cpp_source_path = env_config.get('cpp_source_path', '')
        target_dir = env_config.get('target_dir', '')
        
        # 检查源路径
        if not cpp_source_path:
            print("❌ 未配置cpp_source_path")
            return False
        
        if not os.path.exists(cpp_source_path):
            print(f"❌ C++源路径不存在: {cpp_source_path}")
            return False
        
        print(f"✅ C++源路径存在: {cpp_source_path}")
        
        # 检查目标路径
        if target_dir:
            if os.path.exists(target_dir):
                print(f"✅ 目标路径存在: {target_dir}")
            else:
                print(f"⚠️ 目标路径不存在，将自动创建: {target_dir}")
        
        # 检查必需文件
        required_files = env_config.get('required_files', [])
        for file_name in required_files:
            file_path = os.path.join(cpp_source_path, file_name)
            if os.path.exists(file_path):
                print(f"  ✅ {file_name}")
            else:
                print(f"  ❌ {file_name} - 文件不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查C++环境时出错: {e}")
        return False


def check_permissions():
    """检查文件权限"""
    print(f"\n🔐 检查文件权限...")
    
    current_dir = os.getcwd()
    
    # 检查当前目录写权限
    if os.access(current_dir, os.W_OK):
        print(f"✅ 当前目录可写: {current_dir}")
    else:
        print(f"❌ 当前目录不可写: {current_dir}")
        return False
    
    # 检查临时目录
    import tempfile
    temp_dir = tempfile.gettempdir()
    if os.access(temp_dir, os.W_OK):
        print(f"✅ 临时目录可写: {temp_dir}")
    else:
        print(f"❌ 临时目录不可写: {temp_dir}")
        return False
    
    return True


def generate_health_report():
    """生成系统健康报告"""
    print("\n📊 生成系统健康报告...")
    
    checks = [
        ("Python版本", check_python_version()),
        ("Python依赖", check_dependencies()),
        ("FFmpeg", check_ffmpeg()),
        ("核心模块", check_core_modules()),
        ("文件权限", check_permissions())
    ]
    
    if HAS_RICH:
        table = Table(title="系统健康报告", show_header=True, header_style="bold blue")
        table.add_column("检查项目", style="cyan")
        table.add_column("状态", style="magenta")
        
        for check_name, result in checks:
            status = "✅ 正常" if result else "❌ 异常"
            table.add_row(check_name, status)
        
        console.print(table)
    else:
        print("\n=== 系统健康报告 ===")
        for check_name, result in checks:
            status = "✅ 正常" if result else "❌ 异常"
            print(f"{check_name}: {status}")
    
    success_count = sum(1 for _, result in checks if result)
    total_count = len(checks)
    health_score = success_count / total_count * 100
    
    print(f"\n🎯 系统健康评分: {health_score:.1f}% ({success_count}/{total_count})")
    
    if health_score == 100:
        print("🎉 系统完全健康，可以正常使用！")
    elif health_score >= 80:
        print("⚠️ 系统基本健康，但有一些问题需要解决")
    else:
        print("🚨 系统存在严重问题，请先解决后再使用")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="DMS自动化平台故障排除工具")
    
    parser.add_argument(
        '--config', '-c',
        type=str,
        help='配置文件路径'
    )
    
    parser.add_argument(
        '--check-deps',
        action='store_true',
        help='仅检查依赖'
    )
    
    args = parser.parse_args()
    
    print_header()
    
    if args.check_deps:
        check_python_version()
        check_dependencies()
        check_ffmpeg()
        return
    
    # 基础检查
    generate_health_report()
    
    # 配置文件检查
    if args.config:
        if check_config_file(args.config):
            check_video_files(args.config)
            check_cpp_environment(args.config)
    else:
        print("\n💡 提示: 使用 --config 参数检查特定配置文件")
        print("例如: python troubleshoot.py --config my_config.json")


if __name__ == "__main__":
    main()
