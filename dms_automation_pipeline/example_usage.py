#!/usr/bin/env python3
"""
DMS自动化平台使用示例
演示完整的使用流程和最佳实践

使用方法:
    python example_usage.py --demo
    python example_usage.py --create-example-config
    python example_usage.py --test-basic
"""

import os
import sys
import json
import argparse
import tempfile
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.task_orchestrator import TaskOrchestrator, PipelineState

# 尝试导入Rich库
try:
    from rich.console import Console
    from rich.panel import Panel
    from rich.text import Text
    from rich.progress import Progress, SpinnerColumn, TextColumn
    console = Console()
    HAS_RICH = True
except ImportError:
    console = None
    HAS_RICH = False


def print_banner():
    """显示横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    DMS自动化平台使用示例                                     ║
║                                                                              ║
║  📚 完整的使用流程演示                                                       ║
║  🎯 最佳实践指导                                                            ║
║  🧪 功能测试验证                                                            ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    
    if HAS_RICH:
        console.print(Panel(
            Text(banner.strip(), style="bold green"),
            border_style="green"
        ))
    else:
        print(banner)


def create_example_config():
    """创建示例配置文件"""
    print("\n📋 创建示例配置文件...")
    
    example_config = {
        "task_info": {
            "task_name": "DMS分析示例项目",
            "task_description": "演示DMS自动化分析与渲染流程",
            "version": "1.0.0",
            "created_by": "示例脚本",
            "created_at": "2025-07-14"
        },
        
        "video_config": {
            "source_videos": [
                {
                    "path": "/path/to/your/video1.mkv",
                    "time_ranges": ["00:04:57-00:05:17", "00:15:10-00:15:30"],
                    "roi": "1920:1080:0:0",
                    "description": "主要测试视频"
                },
                {
                    "path": "/path/to/your/video2.mkv", 
                    "time_ranges": ["00:02:00-00:02:30"],
                    "roi": "1280:800:0:0",
                    "description": "辅助测试视频"
                }
            ],
            "output_dir": "./processed_videos/",
            "enable_deduplication": True,
            "overwrite_existing": False,
            "max_workers": 4,
            "ffmpeg_preset": "medium",
            "ffmpeg_crf": 23
        },
        
        "environment_config": {
            "cpp_source_path": "../BYD_HKH_R_2.01.07.2025.07.08.4_x86/",
            "target_dir": "./runtime_env/",
            "required_files": [
                "test_dms_internal_postmortem",
                "libtx_dms.so",
                "calidata.json",
                "ip_port.json"
            ],
            "model_files": [
                "FaceDetection.ovm",
                "FaceKeypoints.ovm", 
                "eye.ovm"
            ],
            "auto_set_permissions": True,
            "backup_existing": True
        },
        
        "remote_config": {
            "enabled": False,
            "ip_port_config_path": "./runtime_env/ip_port.json",
            "ssh_config": {
                "ssh_port": 22,
                "username": "root",
                "password": None,
                "private_key_path": "~/.ssh/id_rsa",
                "connection_timeout": 30,
                "max_retries": 3
            },
            "service_config": {
                "remote_service_path": "/userfs/tx_dms_oax_test_tool_update",
                "remote_model_path": "/userfs/models/",
                "startup_wait_time": 3,
                "service_check_interval": 1
            },
            "model_sync_enabled": True,
            "force_restart_service": False
        },
        
        "rendering_config": {
            "output_dir": "./rendered_videos/",
            "temp_dir": "./temp_rendering/",
            "cpp_program_path": "./test_dms_internal_postmortem",
            "cpp_working_dir": "./runtime_env/",
            "save_cpp_output": True,
            "max_concurrent_tasks": 2,
            "video_codec": "mp4v",
            "roi_region": [0, 0, 1280, 800],
            "expected_size": [1280, 800],
            "render_tasks": [
                {
                    "task_id": "example_task_001",
                    "video_paths": ["./processed_videos/video1_000457-000517_roi_1920_1080_0_0.mp4"],
                    "output_path": "./rendered_videos/dms_analysis_001.mp4",
                    "fps": 10.0,
                    "roi_region": [0, 0, 1280, 800],
                    "expected_size": [1280, 800],
                    "release_tmp_source": True
                }
            ]
        },
        
        "logging_config": {
            "level": "INFO",
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            "file_output": "./logs/dms_pipeline.log",
            "console_output": True
        },
        
        "pipeline_config": {
            "auto_run_full_pipeline": False,
            "stop_on_error": True,
            "save_intermediate_results": True,
            "cleanup_temp_files": True,
            "state_file": ".pipeline_state.json"
        }
    }
    
    config_file = "example_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(example_config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 示例配置文件已创建: {config_file}")
    print("\n📝 配置文件说明:")
    print("  1. 修改 video_config.source_videos 中的视频路径")
    print("  2. 修改 environment_config.cpp_source_path 为实际C++文件路径")
    print("  3. 根据需要启用或禁用 remote_config")
    print("  4. 调整 rendering_config 中的渲染参数")
    
    return config_file


def demo_basic_usage():
    """演示基本使用流程"""
    print("\n🎯 演示基本使用流程...")
    
    # 创建临时配置
    temp_config = {
        "video_config": {
            "source_videos": [],
            "output_dir": "./demo_processed/"
        },
        "environment_config": {
            "cpp_source_path": "./demo_cpp/",
            "target_dir": "./demo_runtime/"
        },
        "remote_config": {
            "enabled": False
        },
        "rendering_config": {
            "output_dir": "./demo_rendered/",
            "render_tasks": []
        }
    }
    
    print("\n步骤1: 创建TaskOrchestrator")
    orchestrator = TaskOrchestrator(temp_config)
    print(f"✅ 当前状态: {orchestrator.get_current_state().value}")
    
    print("\n步骤2: 显示初始状态")
    orchestrator.display_status()
    
    print("\n步骤3: 演示状态转换")
    states = [
        (PipelineState.VIDEO_PROCESSING, "视频预处理"),
        (PipelineState.ENV_SETUP, "环境设置"),
        (PipelineState.REMOTE_VALIDATION, "远程验证"),
        (PipelineState.RENDERING, "渲染引擎")
    ]
    
    for state, description in states:
        print(f"\n  转换到: {description}")
        orchestrator.set_state(state)
        print(f"  当前状态: {orchestrator.get_current_state().value}")
    
    print("\n步骤4: 显示最终状态")
    orchestrator.display_status()
    
    print("\n✅ 基本使用流程演示完成")


def test_module_imports():
    """测试模块导入"""
    print("\n🧪 测试模块导入...")
    
    modules = [
        ("TaskOrchestrator", "core.task_orchestrator", "TaskOrchestrator"),
        ("VideoPreprocessor", "core.video_preprocessor", "VideoPreprocessor"),
        ("EnvironmentManager", "core.environment_manager", "EnvironmentManager"),
        ("RemoteValidator", "core.remote_validator", "RemoteValidator"),
        ("RenderingEngine", "core.rendering_engine", "RenderingEngine")
    ]
    
    success_count = 0
    for name, module_path, class_name in modules:
        try:
            module = __import__(module_path, fromlist=[class_name])
            cls = getattr(module, class_name)
            print(f"  ✅ {name}")
            success_count += 1
        except Exception as e:
            print(f"  ❌ {name}: {e}")
    
    print(f"\n📊 模块导入测试: {success_count}/{len(modules)} 成功")
    return success_count == len(modules)


def test_data_structures():
    """测试数据结构"""
    print("\n🧪 测试数据结构...")
    
    try:
        from core.video_preprocessor import VideoSegment, ProcessingResult
        from core.rendering_engine import RenderingTask, RenderingResult
        
        # 测试VideoSegment
        segment = VideoSegment(
            video_path="/test/video.mp4",
            start_time="00:00:00",
            end_time="00:00:10",
            roi="1920:1080:0:0"
        )
        print("  ✅ VideoSegment")
        
        # 测试ProcessingResult
        result = ProcessingResult(success=True, segment=segment)
        print("  ✅ ProcessingResult")
        
        # 测试RenderingTask
        task = RenderingTask(
            task_id="test_task",
            video_paths=["/test/video.mp4"],
            output_path="/test/output.mp4"
        )
        print("  ✅ RenderingTask")
        
        # 测试RenderingResult
        render_result = RenderingResult(
            success=True,
            task_id="test_task"
        )
        print("  ✅ RenderingResult")
        
        print("\n📊 数据结构测试: 全部通过")
        return True
        
    except Exception as e:
        print(f"\n❌ 数据结构测试失败: {e}")
        return False


def run_comprehensive_test():
    """运行综合测试"""
    print("\n🔬 运行综合测试...")
    
    tests = [
        ("模块导入测试", test_module_imports),
        ("数据结构测试", test_data_structures),
        ("基本使用演示", lambda: (demo_basic_usage(), True)[1])
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n📊 综合测试结果:")
    success_count = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            success_count += 1
    
    total_tests = len(results)
    success_rate = success_count / total_tests * 100
    
    print(f"\n🎯 测试总结: {success_count}/{total_tests} 通过 ({success_rate:.1f}%)")
    
    if success_rate == 100:
        print("🎉 所有测试通过！系统运行正常")
    elif success_rate >= 80:
        print("⚠️ 大部分测试通过，但有一些问题需要注意")
    else:
        print("🚨 多个测试失败，请检查系统配置")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="DMS自动化平台使用示例")
    
    parser.add_argument(
        '--demo',
        action='store_true',
        help='运行完整演示'
    )
    
    parser.add_argument(
        '--create-example-config',
        action='store_true',
        help='创建示例配置文件'
    )
    
    parser.add_argument(
        '--test-basic',
        action='store_true',
        help='运行基础测试'
    )
    
    args = parser.parse_args()
    
    print_banner()
    
    if args.create_example_config:
        create_example_config()
    elif args.test_basic:
        run_comprehensive_test()
    elif args.demo:
        print("\n🚀 运行完整演示...")
        create_example_config()
        demo_basic_usage()
        run_comprehensive_test()
    else:
        print("\n💡 使用说明:")
        print("  --demo                 运行完整演示")
        print("  --create-example-config 创建示例配置文件")
        print("  --test-basic           运行基础测试")
        print("\n例如: python example_usage.py --demo")


if __name__ == "__main__":
    main()
