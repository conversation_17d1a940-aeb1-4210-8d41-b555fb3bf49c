#!/usr/bin/env bash
#
# 使用说明：
#   ./make_looped_video.sh <图片文件夹路径> <输出视频文件名>
#
# 示例：
#   ./make_looped_video.sh ./images output.mkv
#
# 需求：
#   - ffmpeg
#   - bash

set -e

####################################
# 参数及默认值
####################################
IMAGE_DIR=${1:-"./images"}          # 图片所在的文件夹
OUTPUT_FILE=${2:-"output.mkv"}      # 输出文件名
FPS=10                              # 帧率
DURATION=12                         # 视频时长（秒）
TOTAL_FRAMES=$((FPS * DURATION))    # 总帧数，1分钟30fps -> 30*60=1800帧

# 用于生成临时帧文件的目录
TEMP_DIR="./temp_frames"

####################################
# 函数：清理临时文件
####################################
cleanup() {
  echo "清理临时文件..."
  rm -rf "${TEMP_DIR}"
}
trap cleanup EXIT

####################################
# 1. 检查依赖和输入
####################################
if ! command -v ffmpeg &> /dev/null; then
  echo "错误：系统中未安装 ffmpeg，请先安装 ffmpeg。"
  exit 1
fi

if [ ! -d "${IMAGE_DIR}" ]; then
  echo "错误：图片文件夹不存在：${IMAGE_DIR}"
  exit 1
fi

####################################
# 2. 收集图片文件
####################################
# 通常按文件名排序
readarray -t images < <(find "${IMAGE_DIR}" -type f \( -iname "*.jpg" -o -iname "*.jpeg" -o -iname "*.png" -o -iname "*.bmp" \) | sort -V)
printf "图片文件列表：\n"
for img in "${images[@]}"; do
  printf "%s\n" "$img"
done
IMAGE_COUNT=${#images[@]}

if [ ${IMAGE_COUNT} -eq 0 ]; then
  echo "错误：在 ${IMAGE_DIR} 下未找到任何图片文件。"
  exit 1
fi

echo "在 ${IMAGE_DIR} 下共找到 ${IMAGE_COUNT} 张图片。"

####################################
# 3. 准备临时目录
####################################
mkdir -p "${TEMP_DIR}"

####################################
# 4. 按需循环图片并生成帧序列
####################################
echo "生成临时帧序列中，请稍候..."

for ((i=0; i<${TOTAL_FRAMES}; i++)); do
  # 当前要使用的图片下标，循环使用
  img_index=$(( i % IMAGE_COUNT ))
  # 构造目标帧文件名（例如 frame0001.jpg）
  frame_file=$(printf "${TEMP_DIR}/frame%04d.png" "$i")
  # 拷贝或软链接均可，这里用拷贝
  cp "${images[$img_index]}" "${frame_file}"
done

echo "帧序列生成完毕，共生成 ${TOTAL_FRAMES} 帧。"

####################################
# 5. 使用 ffmpeg 将帧序列合成为视频
####################################
echo "开始使用 ffmpeg 合成视频：${OUTPUT_FILE}"

# -r 30：输入帧率，匹配我们生成的帧序列
# -i frame%04d.jpg：读入我们临时目录的帧序列
# -c:v libx264：使用 x264 编码
# -pix_fmt yuv420p：常用像素格式，兼容性好
# -y：如果输出文件已存在，则覆盖
ffmpeg \
  -y \
  -framerate ${FPS} \
  -i "${TEMP_DIR}/frame%04d.png" \
  -c:v libx264 \
  -pix_fmt yuv420p \
  "${OUTPUT_FILE}"

echo "视频合成完成：${OUTPUT_FILE}"
