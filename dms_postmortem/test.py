import numpy as np
import matplotlib.pyplot as plt

# 定义 x 取值范围，避开 -90° 和 90° 两端（防止除零错误）
x = np.linspace(-50, 50, 1000)
# 注意：np.cos 以弧度为单位，因此需要转换角度为弧度
y = 1 / np.cos(np.deg2rad(x))

plt.figure(figsize=(8, 6))
plt.plot(x, y, label=r'$sec(x)=\frac{1}{\cos(x)}$')
plt.xlabel('x (°)')
plt.ylabel('sec(x)')
plt.title('Graph of sec(x) = 1/cos(x) on [-90°, 90°]')
plt.axhline(0, color='black', linewidth=0.5)
plt.axvline(0, color='black', linewidth=0.5)
plt.legend()
plt.grid(True)
plt.show()
