import os
import cv2
import time
import json
import threading
import subprocess
import sys
import numpy as np
import signal

region_hulls = None
img = None
is_drawregion = False
last_cali_headangle = None
last_cali_eyeangle = None
IMAGE_ROI_REGION = [None,None,None,None]
ALGO_EXPECTED_FRAME_SIZE = [None, None]
VIDEO_SIZE = [None, None]

color_list = [
        (255, 0, 0),   # 蓝色
        (0, 255, 0),   # 绿色
        (0, 0, 255),   # 红色
        (255, 255, 0), # 青色
        (255, 0, 255), # 洋红色
        (0, 255, 255), # 黄色
        (128, 0, 128), # 紫色
        (128, 128, 0), # 橄榄色
        (0, 128, 128), # 蓝绿色
        (128, 128, 128), # 灰色
        (255, 165, 0),  # 橙色
        (75, 0, 130),   # 靛蓝
        (240, 230, 140),# 卡其色
        (64, 224, 208), # 绿松石色
        (220, 20, 60),  # 猩红
        (255, 20, 147), # 深粉红
        (50, 205, 50),  # 酸橙绿
        (210, 105, 30), # 巧克力色
        (32, 178, 170), # 浅海蓝色
        (139, 69, 19)   # 棕色
    ]

def parse_input(input_str):
    """
    /path/to/video1.mkv,[:],/path/to/video2.mkv,[start:end],...
    """
    parts = input_str.split(',')
    video_infos = []

    if len(parts) % 2 != 0:
        raise ValueError("输入字符串格式不正确，每个视频路径后应跟一个帧范围。")

    for i in range(0, len(parts), 2):
        video_path = parts[i].strip()
        frame_range_str = parts[i+1].strip()

        # if not os.path.isfile(video_path):
        #     print(f"警告：视频文件 {video_path} 不存在，将跳过。")
        #     continue

        if frame_range_str.startswith('[') and frame_range_str.endswith(']'):
            frame_range_content = frame_range_str[1:-1]
            if frame_range_content == '':
                # 格式为 [:]，表示默认全范围
                frame_range = []
            else:
                # 解析 [start:end]
                start_str, end_str = frame_range_content.split(':')
                start = int(start_str) if start_str else 0
                end = int(end_str) if end_str else None
                frame_range = [start, end]
        else:
            # 如果帧范围格式不正确，默认全范围
            frame_range = []

        video_infos.append((video_path, frame_range))

    return video_infos

def crop_and_save_frame(frame, frame_id, folder_name, crop_range=[0,0,1280,800], expected_size=[1280,800]):
    # 验证裁剪参数有效性
    height, width = frame.shape[:2]
    if (crop_range[0] < 0 or crop_range[1] < 0 or
        crop_range[2] <= 0 or crop_range[3] <= 0 or
        crop_range[0] + crop_range[2] > width or
        crop_range[1] + crop_range[3] > height):
        print(f"Invalid crop parameters: frame_size={width}x{height}, crop_area=({crop_range[0]},{crop_range[1]},{crop_range[2]},{crop_range[3]})")
        return None

    cropped_frame = frame[crop_range[1]:crop_range[1]+crop_range[3], crop_range[0]:crop_range[0]+crop_range[2]]  # 裁剪为 (y1:y2, x1:x2)
    
    resized_frame = cropped_frame
    if cropped_frame.shape[:2] != expected_size:
        resized_frame = cv2.resize(cropped_frame,expected_size,interpolation=cv2.INTER_LINEAR)
        
    frame_filename = os.path.join(folder_name, f"{frame_id}.jpg")
    cv2.imwrite(frame_filename, resized_frame)

    if frame_id % 50 == 0:
        print(f"Crop and save {frame_filename=}")
    
    # 当原始帧和裁剪帧尺寸不同时，额外保存原始帧
    if frame.shape != cropped_frame.shape:
        raw_frame_filename = os.path.join(folder_name, f"{frame_id}_raw.jpg")
        cv2.imwrite(raw_frame_filename, frame)
        if frame_id % 50 == 0:
            print(f"Crop and save {raw_frame_filename=}")
            
    return frame_filename

def frame_extraction_worker(video_infos, folder_name, text_file_path, desired_fps, total_frames_dict, lock, extraction_done_event):
    global IMAGE_ROI_REGION
    global ALGO_EXPECTED_FRAME_SIZE
    global VIDEO_SIZE
    total_frames = 0
    video_extensions = ['.avi', '.mp4', '.mkv']

    with open(text_file_path, 'w') as f:
        pass
    for video_info in video_infos:
        video_name, frame_range = video_info
        if not video_name:
            continue
        # 不是视频当作是传入的文件夹路径,默认图片已经按照需求处理好了，不会进行额外的跳帧裁剪缩放操作
        if not any(video_name.lower().endswith(ext) for ext in video_extensions):                                                              
            print(f"1 {video_name=}")
            if os.path.isdir(video_name):
                for file in os.listdir(video_name):
                    file_name = os.path.join(video_name, file)
                    if file_name.lower().endswith((".png", ".jpg")):
                        with lock:
                            with open(text_file_path, 'a') as f:
                                f.write(f"{int(total_frames)},{file_name}\n")
                            total_frames += 1
                            total_frames_dict['count'] = total_frames # 更新总帧数
            else:
                print(f"{video_name} is not a valid dir path.")
 
        else:    
            cap = cv2.VideoCapture(video_name)
            # 打印视频信息
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fourcc = int(cap.get(cv2.CAP_PROP_FOURCC))
            fourcc_str = "".join([chr((fourcc >> 8 * i) & 0xFF) for i in range(4)])
            print(f"Video {video_name} info: {width}x{height}, {fps:.2f}fps, {frame_count}frames, codec:{fourcc_str}")
            
            VIDEO_SIZE = [1920, 1080]#[width, height]
            
            if not cap.isOpened():
                print(f"Video {video_name} open failed.")
                continue

            if not frame_range:
                frame_range = [0, frame_count - 10]  # 默认范围

            frame_start, frame_end = frame_range
            if frame_end is None or frame_end >= frame_count:
                frame_end = frame_count - 1

            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_start)

            frame_id = frame_start
            while frame_id <= frame_end:
                ret, frame = cap.read()
                if not ret:
                    print(f"Read {video_name} the {frame_id} frame failed...")
                    break

                if (frame_id - frame_start) % max(int(fps / desired_fps), 1) == 0:
                    if frame.shape[:2] != VIDEO_SIZE:
                        # print("resize ......")
                        frame = cv2.resize(frame, VIDEO_SIZE, interpolation=cv2.INTER_LINEAR)
                    frame_filename = crop_and_save_frame(frame, int(total_frames), folder_name, IMAGE_ROI_REGION)
                    if frame_filename:
                        with lock:
                            with open(text_file_path, 'a') as f:
                                # print(f"write {frame_filename} ...")
                                f.write(f"{int(total_frames)},{frame_filename}\n")
                            total_frames += 1
                            total_frames_dict['count'] = total_frames # 更新总帧数

                frame_id += 1
                if (frame_id % 10 == 0):
                    # print(f"{total_frames=}, {frame_id=}")
                    time.sleep(0.01)

            cap.release()

    extraction_done_event.set()
    print(f"All frames extract success, {total_frames} frames saved.")


def display_distraction_params(image, params, face_params, color=(255, 0, 0), start_x=10, start_y=270, line_height=20):
    try:
        cv2.putText(image, "head angle info", (start_x, start_y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.8, color, 1)
        cv2.rectangle(image, (start_x, start_y), (start_x + 180, start_y + 180), color, 1)
        center_position = (start_x + 90, start_y + 90)
        head_yaw_min = (params["head_yaw_min"])
        head_yaw_max = (params["head_yaw_max"])
        head_pitch_min = (params["head_pitch_min"])
        head_pitch_max = (params["head_pitch_max"])
            
        cv2.arrowedLine(image, center_position, (center_position[0]+120, center_position[1]), (0, 0, 255), 2)
        cv2.arrowedLine(image, center_position, (center_position[0], center_position[1]-120), (0, 255, 0), 2)
        
        inner_rect = [(center_position[0] + (head_yaw_min), center_position[1] + (-head_pitch_min)), 
                    (center_position[0] + (head_yaw_max), center_position[1] + (-head_pitch_max))]
        cv2.rectangle(image, (int(inner_rect[0][0]),int(inner_rect[0][1])), (int(inner_rect[1][0]),int(inner_rect[1][1])), (255, 255, 255), 1)
        cv2.putText(image, "min:("+ str(round(head_yaw_min, 2))+ ","+str(round(head_pitch_min, 2)) +")", 
                    (int(inner_rect[0][0]), int(inner_rect[0][1]+10)), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
        cv2.putText(image, "max:("+ str(round(head_yaw_max, 2))+ ","+str(round(head_pitch_max, 2)) +")", 
                    (int(inner_rect[1][0]+10), int(inner_rect[1][1])), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
        
        face_point = (center_position[0]+(face_params["yaw"]), center_position[1]+(-face_params["pitch"]))

        face_angle_color = (255, 255, 0)

        if face_point[0] < inner_rect[0][0] or face_point[0] > inner_rect[1][0] or face_point[1] > inner_rect[0][1] or face_point[1] < inner_rect[1][1]:
            # print("not in rectangle")
            face_angle_color = (0, 0, 255)
        cv2.putText(image, "("+str(round(face_params["yaw"], 2))+","+str(round(face_params["pitch"], 2))+","+str(round(face_params["roll"], 2))+")", 
                    (int(face_point[0]+10), int(face_point[1])), cv2.FONT_HERSHEY_SIMPLEX, 0.5, face_angle_color, 1)
        cv2.circle(image, (int(face_point[0]), int(face_point[1])), 2, face_angle_color, -1)

    except Exception as e:
        print("display_distraction_params:", e)
        
    return image


def draw_face_info(image, face_info):
    bbox_color = (0, 255, 127)  
    point_color = (3, 97, 255)  
    thickness = 2
    cnt = 0
    left_mouth_point = (0,0)
    right_mouth_point = (0,0)
    dis = 0
    cv2.rectangle(image, 
                (2*face_info["xmin"], 2*face_info["ymin"]),
                (2*face_info["xmax"], 2*face_info["ymax"]), 
                bbox_color, thickness)

    for landmark in face_info["landmarks"]:
        if cnt not in [12,13,14,15]: # 不绘制人脸关键点模型输出的眼睛关键点
            cv2.circle(image, (2*landmark["x"], 2*landmark["y"]), 2, point_color, -1)

        if cnt == 5:
            cv2.circle(image, (2*landmark["x"], 2*landmark["y"]), 2, (255, 0, 0), -1)
            left_mouth_point = (2*landmark["x"], 2*landmark["y"])
        if cnt == 6:
            cv2.circle(image, (2*landmark["x"], 2*landmark["y"]), 2, (0, 255, 0), -1)
        if cnt == 7:
            cv2.circle(image, (2*landmark["x"], 2*landmark["y"]), 2, (0, 0, 255), -1)
            right_mouth_point = (2*landmark["x"], 2*landmark["y"])
            dis = right_mouth_point[0]-left_mouth_point[0]
            cv2.putText(image, str(dis), (int(right_mouth_point[0]+5), int(right_mouth_point[1])), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
        if cnt == 8:
            cv2.circle(image, (2*landmark["x"], 2*landmark["y"]), 2, (0, 255, 255), -1)

        cnt+=1
    return image


def mapping_point(show, distraction_params, region_label=-1):
    global region_hulls
    global img
    global is_drawregion

    if region_hulls is None:
        hulls = distraction_params["region_hulls"]
        region_hulls = hulls
        print("set_region_hulls...")

    if img is None:
        img = np.ones((800, 800, 3), dtype=np.uint8) * 255

    if img is not None and region_hulls is not None and is_drawregion is False:
        contours = []
        for hull in region_hulls:
            scaled_hull = [(coord["x"], coord["y"]) for coord in hull]
            coords = np.array(scaled_hull, dtype=np.float32).reshape((-1, 2))
            coords = np.round(coords).astype(np.int32)
            coords = coords.reshape((-1, 1, 2))
            contours.append(coords)
        cv2.drawContours(img, contours, -1, (0, 255, 0), 1)
        is_drawregion = True
        
        line_color = (0, 0, 0)  
        line_thickness = 1      
        dash_length = 10        
        gap_length = 10         
        for y in range(0, 800, dash_length + gap_length):
            cv2.line(img, (400, y), (400, y + dash_length), line_color, line_thickness)
        for x in range(0, 800, dash_length + gap_length):
            cv2.line(img, (x, 400), (x + dash_length, 400), line_color, line_thickness)

    if img is not None:
        show_img = img.copy()
        mapping_x = int(distraction_params["mapping_x"])
        mapping_y = int(distraction_params["mapping_y"])
        if mapping_x > 0 and mapping_y > 0:
            color = (255, 255, 0)
            if int(distraction_params["predict_result"]) != 0:
                color = (0, 0, 255)
            cv2.circle(show_img, (mapping_x, mapping_y), 3, color, -1)
            cv2.putText(show_img, "("+str(mapping_x)+","+str(mapping_y)+")", (mapping_x+10, mapping_y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
            
            if region_label != -1:           
                color = color_list[region_label]
                cv2.circle(img, (mapping_x, mapping_y), 1, color, -1)
        
        crop_x, crop_width, crop_y, crop_height = 250 ,300 ,250 ,300
        crop_x_end = min(crop_x + crop_width, show_img.shape[1])
        crop_y_end = min(crop_y + crop_height, show_img.shape[0])
        
        cropped_region = show_img[crop_y:crop_y_end, crop_x:crop_x_end]

        overlay_height, overlay_width = cropped_region.shape[:2]
        target_x = show.shape[1] - overlay_width  # 右上角 x 坐标
        target_y = 0  # 右上角 y 坐标

        show[target_y:target_y + overlay_height, target_x:target_x + overlay_width] = cropped_region
        
        # cv2.imwrite("region_mapping.png", img)

    return show

def show_progress_bar(image, progress, total, start_pos, bar_length=20, default_total=3500, default_percent=0.8):
    if total > 0:
        # compare_total = default_total
        # if total >= compare_total:
        compare_total = total
            
        bar_color = (140, 90, 60)
        percent = (progress / compare_total) 
        bar_width = int(bar_length * percent) 
        
        if percent >= default_percent and total >= default_total: # 在占比和长度都符合时才表示满足条件
            bar_color = (0, 255, 0) 
        
        cv2.rectangle(image, start_pos, (start_pos[0] + bar_length * 10, start_pos[1] + 20), (255, 255, 255), -1)  
        cv2.rectangle(image, start_pos, (start_pos[0] + bar_width * 10, start_pos[1] + 20), bar_color, -1)  
        
        cv2.putText(image, f'{round((percent * 100), 2)}%', (start_pos[0] + bar_length * 5, start_pos[1] + 15),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 255), 1, cv2.LINE_AA)
        
        total_text = f'{compare_total}'
        cv2.putText(image, total_text, (start_pos[0] + bar_length * 10 + 10, start_pos[1] + 15),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 255), 1, cv2.LINE_AA)
    
    return image

def showinfo_linebyline(image, params, color=(255, 0, 0), start_x=10, start_y=270, line_height=20):
    y = start_y
    for key, value in params.items():
        text = f"{key}: {value}"
        cv2.putText(image, text, (start_x, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 1, cv2.LINE_AA)
        y += line_height
    return image

def add_text(image, text, position, color=(255, 0, 0), font_scale=0.6, thickness=1):    
    cv2.putText(image, text, position, cv2.FONT_HERSHEY_SIMPLEX, font_scale, color, thickness, cv2.LINE_AA)
    return image

def draw_eye_info(image, eye_info):
    eye_coutours_color = (0, 255, 0)
    iris_coutours_color = (0, 0, 255)
    pupil_color = (255, 0, 0)
    dis = 0
    
    for landmark in eye_info["eye_coutours"]:
        cv2.circle(image, (2*landmark["x"], 2*landmark["y"]), 1, eye_coutours_color, -1)
    if eye_info["eye_score"] in [1,-1, -2]: # 增加眼睛特征的分数为-1时的距离计算
        dis = abs(2*eye_info["eye_coutours"][0]["x"]-2*eye_info["eye_coutours"][4]["x"])
        cv2.putText(image, str(dis), (int(2*eye_info["eye_coutours"][4]["x"]+10), int(2*eye_info["eye_coutours"][4]["y"])), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
        
    for landmark in eye_info["iris_coutours"]:
        cv2.circle(image, (2*landmark["x"], 2*landmark["y"]), 1, iris_coutours_color, -1)
            
    if eye_info["pupil_score"]  in [1,-1, -2]:
        cv2.circle(image, (2*eye_info["pupil"]["x"], 2*eye_info["pupil"]["y"]), 1, pupil_color, -1)
    
    return image

def process_frame(image, message, show_eye_flag=True):
    global last_cali_headangle
    global last_cali_eyeangle

    face_info = message["dms_result"]["face_info"]
    face_score = face_info["score"]

    leye_info = message["dms_result"]["face_info"]["left_eye_landmark"]
    reye_info = message["dms_result"]["face_info"]["right_eye_landmark"]
    
    # show = cv2.resize(image, (1280, 720))
    show = cv2.resize(image, (1280, 800))

    show = draw_face_info(show, face_info)

    if show_eye_flag is True:
        show = draw_eye_info(show, leye_info)
        show = draw_eye_info(show, reye_info)
        
    distraction_params_str = message["dms_result"].get("distraction_params", "{}")
    distraction_params = {}
    try:
        distraction_params = json.loads(distraction_params_str)
    except json.JSONDecodeError as e:
        print(f"Error parsing distraction_params: {e}")
    
    tired_params = message["tiredInfo"]     
    # tired_params_str = message["tiredInfo"]
    # tired_params = {}
    # try:
    #     tired_params = json.loads(tired_params_str)
    # except json.JSONDecodeError as e:
    #     print(f"Error parsing tired_params: {e}")
        
    normal_color = (8, 101, 139)#(255, 0, 0)
    unnormal_color = (0, 255, 255)
    warning_color = (0, 0, 255)
    
    text_info = []
    
    color = normal_color

    text_info.append(("frame id: "+ str(message["dms_result"]["result_frame_id"]), (1000, 280), color))
    # face chaos info
    text_info.append(("is_mask: " + str(face_info["isMask"]), (1000, 350), color))
    text_info.append(("is_glass: " + str(face_info["isGlass"]), (1000, 370), color))
    text_info.append(("is_irblock: " + str(face_info["isIRBlock"]), (1000, 390), color))
    # hardware status
    if message["dms_result"]["camera_status"] != 0:
        color = warning_color
    text_info.append(("cam_status: " + str(message["dms_result"]["camera_status"]), (1000, 410), color))
    color = normal_color
    text_info.append(("car_gear: " + str(message["dms_result"]["car_gear"]), (1000, 430), color))
    text_info.append(("car_speed: " + str(message["dms_result"]["car_speed"]), (1000, 450), color))
    text_info.append(("steer_rad: " + str(message["dms_result"]["car_steer_whl_snsr_rad"])[:6], (1000, 470), color))
    text_info.append(("turn_light: " + str(message["dms_result"]["turn_light"]), (1000, 490), color))
    
    text_info.append((("SDK VERSION: "), (1000, 520), color))
    text_info.append((str(message["sdk_really_version"]), (950, 540), color))
    
    # 眼睛信息
    eyeinfo_start_pos = (5, 20)
    cv2.putText(show, "EYEINFO", eyeinfo_start_pos, cv2.FONT_HERSHEY_SIMPLEX, 0.8, color, 1, cv2.LINE_AA)
    text_info.append(("           left      right", (5, 35), color))
    text_info.append(("eye:", (5, 55), color))
    text_info.append(("iris:", (5, 70), color))
    text_info.append(("pupil:", (5, 85), color))
    text_info.append(("closed:", (5, 100), color))
    text_info.append(("up/down:", (5, 115), color))
    text_info.append(("yaw:", (5, 130), color))
    text_info.append(("pitch:", (5, 145), color))
    text_info.append(("opening:", (5, 160), color))
    text_info.append(("upcurve:", (5, 175), color))

    kMinEyeScore = 0.5
    kMinIrisScore = 0.7
    kMinPupilScore = 0.7
    if face_info["left_eye_landmark"]["true_eye_score"] <= kMinEyeScore:
        color = unnormal_color
    text_eye_info = str(round(face_info["left_eye_landmark"]["true_eye_score"], 2))
    if face_info["left_eye_landmark"]["eye_score"] < 0:
        text_eye_info = str(face_info["left_eye_landmark"]["eye_score"])
        color = unnormal_color
    text_info.append((text_eye_info, (110, 55), color))
    color = normal_color
    
    if face_info["left_eye_landmark"]["true_iris_score"] <= kMinIrisScore:
        color = unnormal_color
    text_eye_info = str(round(face_info["left_eye_landmark"]["true_iris_score"], 2))
    if face_info["left_eye_landmark"]["iris_score"] < 0:
        text_eye_info = str(face_info["left_eye_landmark"]["iris_score"])
        color = unnormal_color
    text_info.append((text_eye_info, (110, 70), color))
    color = normal_color
    
    if face_info["left_eye_landmark"]["true_pupil_score"] <= kMinPupilScore:
        color = unnormal_color
    text_eye_info = str(round(face_info["left_eye_landmark"]["true_pupil_score"], 2))
    if face_info["left_eye_landmark"]["pupil_score"] < 0:
        text_eye_info = str(face_info["left_eye_landmark"]["pupil_score"])
        color = unnormal_color
    text_info.append((text_eye_info, (110, 85), color))
    color = normal_color

    if face_info["right_eye_landmark"]["true_eye_score"] <= kMinEyeScore:
        color = unnormal_color
    text_eye_info = str(round(face_info["right_eye_landmark"]["true_eye_score"], 2))
    if face_info["right_eye_landmark"]["eye_score"] < 0:
        text_eye_info = str(face_info["right_eye_landmark"]["eye_score"])
        color = unnormal_color
    text_info.append((text_eye_info, (200, 55), color))
    color = normal_color
    
    if face_info["right_eye_landmark"]["true_iris_score"] <= kMinIrisScore:
        color = unnormal_color
    text_eye_info = str(round(face_info["right_eye_landmark"]["true_iris_score"], 2))
    if face_info["right_eye_landmark"]["iris_score"] < 0:
        text_eye_info = str(face_info["right_eye_landmark"]["iris_score"])
        color = unnormal_color
    text_info.append((text_eye_info, (200, 70), color))
    color = normal_color
    
    if face_info["right_eye_landmark"]["true_pupil_score"] <= kMinPupilScore:
        color = unnormal_color
    text_eye_info = str(round(face_info["right_eye_landmark"]["true_pupil_score"], 2))
    if face_info["right_eye_landmark"]["pupil_score"] < 0:
        text_eye_info = str(face_info["right_eye_landmark"]["pupil_score"])
        color = unnormal_color
    text_info.append((text_eye_info, (200, 85), color))
    color = normal_color

    if face_info["left_close_eye_score"] > 0.7:
        color = unnormal_color
    text_info.append((str(round(face_info["left_close_eye_score"], 2)), (110, 100), color))
    color = normal_color
    if face_info["right_close_eye_score"] > 0.7:
        color = unnormal_color
    text_info.append((str(round(face_info["right_close_eye_score"], 2)), (200, 100), color))
    color = normal_color
    
    text_info.append((str(round(message["dms_result"]["left_up_down_proportion"], 2)), (110, 115), color))
    text_info.append((str(round(message["dms_result"]["right_up_down_proportion"], 2)), (200, 115), color))
    
    text_info.append((str(round((face_info["left_eye_landmark"]["yaw"]), 2)), (110, 130), color))
    text_info.append((str(round((face_info["left_eye_landmark"]["pitch"]), 2)), (110, 145), color))
    text_info.append((str(round((face_info["right_eye_landmark"]["yaw"]), 2)), (200, 130), color))
    text_info.append((str(round((face_info["right_eye_landmark"]["pitch"]), 2)), (200, 145), color))
    
    text_info.append((str(round((face_info["left_eye_landmark"]["opening"]), 2)), (110, 160), color))
    text_info.append((str(round((face_info["right_eye_landmark"]["opening"]), 2)), (200, 160), color))
    if face_info["left_eye_landmark"]["is_curve"] is False:
        color = unnormal_color
    text_info.append((str(round((face_info["left_eye_landmark"]["curve_score"]), 5)), (110, 175), color))
    color = normal_color

    if face_info["right_eye_landmark"]["is_curve"] is False:
        color = unnormal_color
    text_info.append((str(round((face_info["right_eye_landmark"]["curve_score"]), 5)), (200, 175), color))
    color = normal_color
    
    if face_info["mouth_opening"] >= 0.5:
        color = unnormal_color
    text_info.append(("mouthopen: " + str(round(face_info["mouth_opening"], 2)), (5, 220), color))
    color = normal_color
    
    text_info.append(("face_score: " + str(round(face_score, 2)), (5, 235), color))

    # calibration
    cv2.putText(show, "CALIBRATION", (270, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.8, color, 1, cv2.LINE_AA)
    calibrate_status = message["dms_result"]["calibrate_status"]
    if calibrate_status == 2:
        color = warning_color
    else:
        if calibrate_status == 1:
            color = unnormal_color  
    text_info.append(("status: " + str(calibrate_status), (270, 35), color))
    color = normal_color
    
    if calibrate_status == 0:
        cali_head_yaw = round(distraction_params["headpose_yaw_"], 2)
        cali_head_pitch = round(distraction_params["headpose_pitch_"], 2)
        cali_head_roll = round(distraction_params["headpose_roll_"], 2)
        cali_eye_yaw = round(distraction_params["gaze_yaw_mean"], 2)
        cali_eye_pitch = round(distraction_params["gaze_pitch_mean"], 2)
        cali_leye_upper_curve = round(distraction_params["leye_uper_curve_score_mean_"], 5)
        cali_reye_upper_curve = round(distraction_params["reye_uper_curve_score_mean_"], 5)
        if last_cali_headangle != [cali_head_yaw, cali_head_pitch, cali_head_roll]:
            last_cali_headangle = [cali_head_yaw, cali_head_pitch, cali_head_roll]
            color = unnormal_color
            
        if last_cali_eyeangle != [cali_eye_yaw, cali_eye_pitch]:
            last_cali_eyeangle = [cali_eye_yaw, cali_eye_pitch]
            color = unnormal_color
        
        text_info.append(("head:["+str(cali_head_yaw)+"," +
                        str(cali_head_pitch) + "," + 
                        str(cali_head_roll) + "]", (270, 55), color))
        
        text_info.append(("eye:["+str(cali_eye_yaw)+"," +
                        str(cali_eye_pitch) + "]" , (270, 75), color))
        
        text_info.append(("eyeupcurve:["+str(cali_leye_upper_curve)+"," +
                        str(cali_reye_upper_curve) + "]" , (270, 95), color))        
        color = normal_color  
    
    # warning: distraction
    cv2.putText(show, "DISTRACTION", (550, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.8, color, 1, cv2.LINE_AA)
    if message["dms_result"]["distraction_type"] != 0:
        color = warning_color
    text_info.append(("type: " + str(message["dms_result"]["distraction_type"]), (550, 40), color))
    color = normal_color
    
    show = display_distraction_params(show, distraction_params, face_info, color)
    if distraction_params != {}:
        show = mapping_point(show, distraction_params)  
              
    if str(message["dms_result"]["distraction_reason"]) != "no_distraction":
        color = warning_color
    text_info.append(("reason:"+str(message["dms_result"]["distraction_reason"]), (550, 60), color))
    color = normal_color

    text_info.append(("headyawbias:"+str(round(distraction_params["current_head_yaw_bias"], 2)), (550, 80), color))
    show = show_progress_bar(show, message["distractionInfo"]["time_gap"], 3150, (550, 100))
    
    # fatigue
    cv2.putText(show, "FATIGUE", (5, 470), cv2.FONT_HERSHEY_SIMPLEX, 0.8, color, 1, cv2.LINE_AA)
    if message["dms_result"]["drowsiness_type"] != 0:
        color = warning_color
    text_info.append(("type: " + str(message["dms_result"]["drowsiness_type"]), (5, 490), color))
    color = normal_color
    show = showinfo_linebyline(show, tired_params, color, 5, 510)

    
    text_info.append(("distractionInfo:" , (790, 30), color)) #+ str(message["distractionInfo"])
    text_info.append(("continue_percent:" + str(message["distractionInfo"]["distraction_continue_percent"]) , (810, 45), color))
    text_info.append(("continue_time:"+ str(message["distractionInfo"]["distraction_continue_time"]) , (810, 60), color))
    text_info.append(("sum_time:" + str(message["distractionInfo"]["distraction_sum_time"]), (810, 75), color))
    text_info.append(("front_continue_time:" + str(message["distractionInfo"]["distraction_front_continue_time"]), (810, 90), color))
    text_info.append(("time_gap:" + str(message["distractionInfo"]["time_gap"]), (810, 105), color))
    
    for text, position, color in text_info:
        show = add_text(show, text, position, color)
        
    return show

def assemble_video_worker(json_dir, folder_name, video_output_path, desired_fps, total_frames_dict, lock, extraction_done_event):
    global IMAGE_ROI_REGION
    global VIDEO_SIZE
    
    time.sleep(5)
    # print(f"{VIDEO_SIZE}=")
    # VIDEO_SIZE = [1920,1080]
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(video_output_path, fourcc, desired_fps, (VIDEO_SIZE[0],VIDEO_SIZE[1]))

    processed_frames = {}
    next_frame_id = None  
    start_time = time.time()

    processed_frame_ids = set()
    while True:
        try:
            json_files = [f for f in os.listdir(json_dir) if f.endswith('.json')]
        except FileNotFoundError:
            json_files = []

        for json_file in json_files:
            frame_id_str = os.path.splitext(json_file)[0]
            try:
                frame_id = int(frame_id_str)
            except ValueError:
                print(f"Invalid jsonname: {json_file}, jump over...")
                continue

            if frame_id in processed_frame_ids:
                continue  
            json_path = os.path.join(json_dir, json_file)
            image_filename = f"{frame_id}.jpg"
            image_path = os.path.join(folder_name, image_filename)

            if os.path.exists(json_path) and os.path.exists(image_path):
                draw_image = cv2.imread(image_path)
                # 读取raw image
                jpg_index = image_path.find('.jpg')
                raw_image_path = image_path[:jpg_index] + '_raw' + image_path[jpg_index:]
                raw_image = cv2.imread(raw_image_path)
                
                with open(json_path, 'r') as json_file:
                    try:
                        draw_json = json.load(json_file)
                    except json.decoder.JSONDecodeError:
                        print(f"Invalid json file: {json_file}, jump over...")
                        # continue
                    processed_image = process_frame(draw_image, draw_json)
                    if processed_image is not None:
                        # 获取处理后图像的尺寸
                        h, w = IMAGE_ROI_REGION[3],IMAGE_ROI_REGION[2]
                        # print(f"{h, w}=")
                        x_offset = IMAGE_ROI_REGION[0]
                        y_offset = IMAGE_ROI_REGION[1]
                        
                        # 检查叠加区域是否有效
                        if y_offset >= 0 and w <= raw_image.shape[1]:
                            raw_image[y_offset:y_offset+h, x_offset:x_offset+w] = processed_image
                        processed_frames[frame_id] = raw_image#processed_image
                        # processed_frames[frame_id] = processed_image
                        processed_frame_ids.add(frame_id) # 记录已处理的帧
        # print(f"assemble_video_worker running...")
        # 同步总帧数
        with lock:
            total_frames = total_frames_dict['count']

        if total_frames > 0:
            if next_frame_id is None:
                next_frame_id = min(processed_frames.keys(), default=None)
            
            # 按顺序写入视频
            while next_frame_id in processed_frames:
                out.write(processed_frames[next_frame_id])
                if next_frame_id % 50 == 0:
                    print(f"Write frame {next_frame_id} into vedio...")
                del processed_frames[next_frame_id]
                next_frame_id += 1

            # 检查是否所有帧都已处理
            if extraction_done_event.is_set() and len(processed_frame_ids) >= total_frames:
                print("All frames have been assembled...")
                break

        # 降低cpu占用率
        time.sleep(1)
        # if (total_frames % 10 == 0):
        #     print(f"{len(processed_frame_ids)=}, {total_frames}")

    out.release()
    print(f"Finally video saved in {video_output_path}.")

def call_cpp_program(txt_file, outfile_name, print_cpp_info = True, process_container=None):
    try:
        time.sleep(2)
        print(f"excute C++ program: ./test_dms_internal_postmortem {txt_file}")
        
        output_file = f"{outfile_name}.txt"
        process = subprocess.Popen(
            ["./test_dms_internal_postmortem", txt_file],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=print_cpp_info
        )
        
        # 将 process 对象添加到容器中
        if process_container is not None:
            process_container.append(process)
            
        # 将输出写入文件
        with open(output_file, 'w') as f:
            if print_cpp_info:
                # 实时读取 stdout 和 stderr 并写入文件
                for line in process.stdout:
                    f.write(line)
                    # sys.stdout.write(line)
                for line in process.stderr:
                    f.write(line)
                    # sys.stderr.write(line)

        process.wait()
        print(f"C++ program output saved to {output_file}")

    except subprocess.CalledProcessError as e:
        print(f"excute C++ program error:{e}")
        print(f"error info:{e.stderr.decode()}")
    except IOError as e:
        print(f"Failed to write output file: {e}")

def process_video(video_infos, desired_fps=10, release_tmp_source=True, image_roi=[0,0,1280,800], expected_size=[1280,800]):
    global IMAGE_ROI_REGION
    global ALGO_EXPECTED_FRAME_SIZE
    IMAGE_ROI_REGION = image_roi
    ALGO_EXPECTED_FRAME_SIZE = expected_size
    
    start_time = time.time()
    # 生成带时间戳的输出文件名
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    output_filename = "_".join([os.path.splitext(os.path.basename(info[0]))[0] for info in video_infos if info[0]])
    output_filename = f"{output_filename}_{timestamp}"
    folder_name = output_filename
    print(f"{folder_name=}")
    os.makedirs(folder_name, exist_ok=True)

    text_file_path = os.path.join(folder_name, 'frames_info.txt')
    print(f"Frame image will saved in {text_file_path}.")

    json_dir = os.path.join("output_json", folder_name)
    os.makedirs(json_dir, exist_ok=True)
    print(f"Frame's json will saved in {json_dir}.")

    video_dir = os.path.join("output_video", folder_name)
    # os.makedirs(video_dir, exist_ok=True)
    video_output_filename = video_dir+"_out.mp4"
    print(f"Result video will be named {video_output_filename}.")

    # 初始化共享变量和同步原语
    total_frames_dict = {'count': 0}
    lock = threading.Lock()
    extraction_done_event = threading.Event()

    extraction_thread = threading.Thread(
        target=frame_extraction_worker,
        args=(video_infos, folder_name, text_file_path, desired_fps, total_frames_dict, lock, extraction_done_event),
        name="FrameExtractionThread"
    )
    extraction_thread.start()

    print("call cpp program...")
    process_container = []

    cpp_thread = threading.Thread(
        target=call_cpp_program,
        args=(text_file_path, video_dir, True, process_container),
        name="CppCallerThread"
    )
    cpp_thread.start()
    
    assembly_thread = threading.Thread(
        target=assemble_video_worker,
        args=(json_dir, folder_name, video_output_filename, desired_fps, total_frames_dict, lock, extraction_done_event),
        name="VideoAssemblyThread"
    )
    assembly_thread.start()

    extraction_thread.join()
    assembly_thread.join()

    end_time = time.time()
    print(f"generate {video_output_filename} used {end_time - start_time:.3f} s.")
    
    if process_container:
        cpp_process = process_container[0]
        print("Send end signal to C++ program...")
        cpp_process.send_signal(signal.SIGINT)
    cpp_thread.join()
    
    # 默认清理临时文件
    if release_tmp_source:
        try:
            subprocess.run(["rm", "-rf", folder_name], check=True)
            subprocess.run(["rm", "-rf", json_dir], check=True)
            print("Clean tmp source files successfully.")
        except subprocess.CalledProcessError as e:
            print(f"Clean tmp source files error: {e}")

def main():
    parse_file = sys.argv[1]  
    video_infos = []
    with open(parse_file, 'r') as f:
        lines = f.readlines()
        for line in lines:
            input_str = line.strip()
            try:
                video_info = parse_input(input_str)
                video_infos.append(video_info)  
            except ValueError as e:
                print(f"输入解析错误: {e}")
                return
        if not video_infos:
            print("没有有效的视频信息需要处理。")
            return

        print(f"{video_infos=}")
        
    # scale = float(1280)/float(1920)
    x = 0
    y = 0
    width = 1280#int(1280 * scale)
    height = 800#int(800 * scale)
    image_roi_ = [x,y,width,height]
    print(f"{x} {y} {width} {height}")
    for video_info in video_infos:
        process_video(video_info, desired_fps=10, release_tmp_source=True, image_roi=image_roi_)

if __name__ == "__main__":
    main()
