import os
import json
import matplotlib.pyplot as plt

def calculate_yaw_diff(json_data):
    """计算 yaw 差值"""
    face_yaw = json_data["dms_result"]["face_info"]["yaw"]
    calibrated_yaw = json.loads(json_data["dms_result"]["distraction_params"])["headpose_yaw_"]
    diff_result = 0
    if calibrated_yaw != 0:
        diff_result = face_yaw - calibrated_yaw
    return diff_result

def calculate_pitch_diff(json_data):
    """计算 pitch 差值"""
    face_pitch = json_data["dms_result"]["face_info"]["pitch"]
    calibrated_pitch = json.loads(json_data["dms_result"]["distraction_params"])["headpose_pitch_"]
    diff_result = 0
    if calibrated_pitch != 0:
        diff_result = face_pitch - calibrated_pitch
    return diff_result

def calculate_eye_yaw_diff(json_data):
    """计算 yaw 差值"""
    face_yaw = json_data["dms_result"]["face_info"]["right_eye_landmark"]["yaw"]
    calibrated_yaw = json.loads(json_data["dms_result"]["distraction_params"])["gaze_yaw_mean"]
    diff_result = 0
    if calibrated_yaw != 0:
        diff_result = face_yaw - calibrated_yaw
    return diff_result

def calculate_eye_pitch_diff(json_data):
    """计算 pitch 差值"""
    face_pitch = json_data["dms_result"]["face_info"]["right_eye_landmark"]["pitch"]
    calibrated_pitch = json.loads(json_data["dms_result"]["distraction_params"])["gaze_pitch_mean"]
    diff_result = 0
    if calibrated_pitch != 0:
        diff_result = face_pitch - calibrated_pitch
    return diff_result

if __name__ == "__main__":
    json_dir = "/home/<USER>/tool_kit/dms_postmortem/output_json/4_000000_000100_1280_800_0_0_4_005450_005810_1280_800_0_0"
    try:
        json_files = [f for f in os.listdir(json_dir) if f.endswith('.json')]
    except FileNotFoundError:
        json_files = []

    frame_ids = []
    yaw_diffs = []
    pitch_diffs = []
    eye_yaw_diffs = []
    eye_pitch_diffs = []
    
    for json_file in json_files:
        frame_id_str = os.path.splitext(json_file)[0]
        try:
            frame_id = int(frame_id_str)
        except ValueError:
            print(f"Invalid jsonname: {json_file}, jump over...")
            continue
        
        json_path = os.path.join(json_dir, json_file)
        if os.path.exists(json_path):
            with open(json_path, 'r') as f:
                try:
                    draw_json = json.load(f)
                    yaw_diff = calculate_yaw_diff(draw_json)
                    pitch_diff = calculate_pitch_diff(draw_json)
                    eye_yaw_diff = calculate_eye_yaw_diff(draw_json)
                    eye_pitch_diff = calculate_eye_pitch_diff(draw_json)
                    frame_ids.append(frame_id)
                    yaw_diffs.append(yaw_diff)
                    pitch_diffs.append(pitch_diff)
                    eye_yaw_diffs.append(eye_yaw_diff)
                    eye_pitch_diffs.append(eye_pitch_diff)
                except json.decoder.JSONDecodeError:
                    print(f"Invalid json file: {json_file}, jump over...")
                    continue

    if frame_ids:
        # 将数据按照 frame id 排序
        data = sorted(zip(frame_ids, yaw_diffs, pitch_diffs, eye_yaw_diffs, eye_pitch_diffs), key=lambda x: x[0])
        frame_ids_sorted, yaw_diffs_sorted, pitch_values_sorted, eye_yaw_diffs_sorted, eye_pitch_values_sorted = zip(*data)

        # 截取指定 frame id 范围内的数据，例如从 1650 到 1950
        start, end = 1650, 1950
        try:
            start_index = frame_ids_sorted.index(start)
            end_index = frame_ids_sorted.index(end) + 1  # 包含 end 对应的数据
        except ValueError:
            print("指定的 frame id 范围内未找到匹配数据！")
            exit()

        selected_frame_ids = frame_ids_sorted[start_index:end_index]
        selected_yaw_diffs = yaw_diffs_sorted[start_index:end_index]
        selected_pitch_values = pitch_values_sorted[start_index:end_index]
        selected_eye_yaw_diffs = eye_yaw_diffs_sorted[start_index:end_index]
        selected_eye_pitch_values = eye_pitch_values_sorted[start_index:end_index]
        
        plt.figure(figsize=(12, 6))
        plt.plot(selected_frame_ids, selected_yaw_diffs, marker='o', label="Yaw Diff")
        plt.plot(selected_frame_ids, selected_pitch_values, marker='o', label="Pitch Diff")
        plt.plot(selected_frame_ids, selected_eye_yaw_diffs, marker='o', label="Eye Yaw Diff")
        plt.plot(selected_frame_ids, selected_eye_pitch_values, marker='o', label="Eye Pitch Diff")
        plt.title("Yaw Difference and Pitch Difference for Frame IDs {} to {}".format(start, end))
        plt.xlabel("Frame Number")
        plt.ylabel("Degrees")
        plt.grid(True)
        plt.legend()
        plt.show()
