#!/usr/bin/env python3
"""
一键式DMS视频分析工具
整合视频裁剪和DMS分析到一个命令中
"""

import os
import sys
import subprocess
import argparse
import tempfile
import shutil
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import time
import json

# 添加当前目录到路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 导入现有的DMS分析功能
from dms_postmortem_optimised import process_video as dms_process_video

class OneClickProgressManager:
    def __init__(self, total_segments):
        self.segments = {}
        self.total_segments = total_segments
        self.lock = threading.Lock()
        self.running = True
        self.display_thread = None
        self.current_phase = "准备中"
        
    def start_display(self):
        self.display_thread = threading.Thread(target=self._display_loop, daemon=True)
        self.display_thread.start()
        
    def stop_display(self):
        self.running = False
        if self.display_thread:
            self.display_thread.join()
        
    def set_phase(self, phase):
        with self.lock:
            self.current_phase = phase
            
    def update_segment(self, index, status, progress=0, message="", phase="处理中"):
        with self.lock:
            self.segments[index] = {
                'status': status,
                'progress': progress,
                'message': message,
                'phase': phase
            }
    
    def _display_loop(self):
        while self.running:
            self._print_status()
            time.sleep(1)
    
    def _print_status(self):
        with self.lock:
            print("\033[2J\033[H", end="")
            print("=" * 80)
            print(f"🚀 一键式DMS视频分析工具 - {self.current_phase}")
            print("=" * 80)
            
            for i in range(self.total_segments):
                if i in self.segments:
                    seg = self.segments[i]
                    status_symbol = {
                        'waiting': '⏳',
                        'cutting': '✂️',
                        'analyzing': '🔍',
                        'completed': '✅',
                        'failed': '❌'
                    }.get(seg['status'], '❓')
                    
                    phase_indicator = f"[{seg['phase']}]"
                    
                    if seg['status'] in ['cutting', 'analyzing'] and seg['progress'] > 0:
                        progress_bar = "█" * int(seg['progress'] / 5) + "░" * (20 - int(seg['progress'] / 5))
                        print(f"{status_symbol} Segment {i+1:2d}: {phase_indicator} [{progress_bar}] {seg['progress']:5.1f}% {seg['message']}")
                    else:
                        print(f"{status_symbol} Segment {i+1:2d}: {phase_indicator} {seg['message']}")
                else:
                    print(f"⏳ Segment {i+1:2d}: 等待开始...")
            print("=" * 80)


class VideoProcessor:
    def __init__(self, input_file, output_dir, roi=None, progress_manager=None):
        self.input_file = input_file
        self.output_dir = output_dir
        self.roi = roi
        self.progress_manager = progress_manager
        self.video_info = self._get_video_info()

    def _get_video_info(self):
        cmd = ['ffprobe', '-v', 'error', '-select_streams', 'v:0',
               '-show_entries', 'stream=width,height', '-of', 'csv=p=0', 
               self.input_file]
        result = subprocess.run(cmd, capture_output=True, text=True)
        width, height = map(int, result.stdout.strip().split(','))
        return {'width': width, 'height': height}

    def _clamp_roi(self, w, h, x, y):
        vw, vh = self.video_info['width'], self.video_info['height']
        w = min(w, vw - x) if x + w > vw else w
        h = min(h, vh - y) if y + h > vh else h
        x = max(0, min(x, vw - w))
        y = max(0, min(y, vh - h))
        return w, h, x, y

    def process_segment(self, start, end, index, frame_range=None):
        base_name = os.path.splitext(os.path.basename(self.input_file))[0]
        time_range = f"{start.replace(':', '')}_{end.replace(':', '')}"
        roi_part = f"_{self.roi.replace(':', '_')}" if self.roi else ""
        output_file = os.path.join(self.output_dir, f"{base_name}_{time_range}{roi_part}.mp4")
        
        if self.progress_manager:
            self.progress_manager.update_segment(index, 'cutting', 0, f"{start} - {end}", "视频裁剪")
        
        ffmpeg_cmd = ['ffmpeg', '-y', '-i', self.input_file,
                     '-ss', start, '-to', end]
        
        filters = []
        if self.roi:
            w, h, x, y = map(int, self.roi.split(':'))
            w, h, x, y = self._clamp_roi(w, h, x, y)
            filters.append(f'crop={w}:{h}:{x}:{y}')
        
        if filters:
            ffmpeg_cmd.extend(['-vf', ','.join(filters)])
        
        ffmpeg_cmd.extend(['-c:v', 'libx264', output_file])
        ffmpeg_cmd.extend(['-progress', 'pipe:1'])
        
        try:
            process = subprocess.Popen(ffmpeg_cmd, stdout=subprocess.PIPE,
                                     stderr=subprocess.PIPE, text=True)
            
            progress = {}
            total_time = (self._time_to_seconds(end) - self._time_to_seconds(start))
            
            for line in process.stdout:
                if '=' in line:
                    key, value = line.strip().split('=')
                    progress[key] = value
                
                if 'out_time_ms' in progress and self.progress_manager:
                    current_time = int(progress['out_time_ms']) / 1000000
                    progress_percent = (current_time / total_time) * 100 if total_time > 0 else 0
                    
                    speed_str = progress.get('speed', '1x')
                    if speed_str.endswith('x'):
                        speed_str = speed_str[:-1]
                    try:
                        speed = float(speed_str)
                    except ValueError:
                        speed = 1.0
                    
                    message = f"{start} - {end} (速度: {speed:.1f}x)"
                    self.progress_manager.update_segment(index, 'cutting', progress_percent, message, "视频裁剪")
            
            process.wait()
            
            if process.returncode == 0:
                if self.progress_manager:
                    self.progress_manager.update_segment(index, 'completed', 100, f"{start} - {end} 裁剪完成", "视频裁剪")
                
                # 返回视频文件路径和帧范围信息，用于DMS分析
                return {
                    'video_path': output_file,
                    'frame_range': frame_range if frame_range else []
                }
            else:
                stderr_output = process.stderr.read()
                if self.progress_manager:
                    self.progress_manager.update_segment(index, 'failed', 0, f"裁剪失败: {stderr_output[:30]}...", "视频裁剪")
                return None
        except Exception as e:
            if self.progress_manager:
                self.progress_manager.update_segment(index, 'failed', 0, f"裁剪异常: {str(e)[:30]}...", "视频裁剪")
            return None

    def _time_to_seconds(self, time_str):
        h, m, s = map(float, time_str.split(':'))
        return h * 3600 + m * 60 + s


def analyze_video_segment(video_info, index, progress_manager, output_dir, image_roi, expected_size):
    """分析单个视频片段"""
    if progress_manager:
        progress_manager.update_segment(index, 'analyzing', 0, "开始DMS分析", "DMS分析")
    
    try:
        # 调用现有的DMS分析功能
        video_path = video_info['video_path']
        frame_range = video_info['frame_range']
        
        # 构建video_infos格式 [(video_path, frame_range), ...]
        dms_video_infos = [(video_path, frame_range)]
        
        if progress_manager:
            progress_manager.update_segment(index, 'analyzing', 50, "DMS分析中...", "DMS分析")
        
        # 调用DMS分析
        dms_process_video(
            video_infos=dms_video_infos,
            desired_fps=10,
            release_tmp_source=False,  # 先不删除临时文件，便于调试
            image_roi=image_roi,
            expected_size=expected_size
        )
        
        if progress_manager:
            progress_manager.update_segment(index, 'completed', 100, "DMS分析完成", "DMS分析")
        
        return True
        
    except Exception as e:
        if progress_manager:
            progress_manager.update_segment(index, 'failed', 0, f"DMS分析失败: {str(e)[:30]}...", "DMS分析")
        return False


def parse_time_ranges(time_ranges):
    return [tuple(tr.split('-')) for tr in time_ranges.split(';')]


def parse_frame_ranges(frame_ranges):
    """解析帧范围参数"""
    if not frame_ranges:
        return None
    return [tuple(map(int, fr.split('-'))) for fr in frame_ranges.split(';')]


def main():
    parser = argparse.ArgumentParser(description='🚀 一键式DMS视频分析工具')
    parser.add_argument('-i', '--input', required=True, help='输入视频文件')
    parser.add_argument('-o', '--output', required=True, help='输出目录')
    parser.add_argument('-t', '--time', required=True, 
                       help='时间范围，格式: start1-end1;start2-end2')
    parser.add_argument('-f', '--frames', 
                       help='帧范围，格式: start1-end1;start2-end2（可选，默认处理所有帧）')
    parser.add_argument('--roi', default='1280:800:0:0',
                       help='ROI区域，格式: width:height:x:y (默认: 1280:800:0:0)')
    parser.add_argument('--threads', type=int, default=min(4, os.cpu_count()),
                       help='线程数 (默认: 4)')
    parser.add_argument('--keep-temp', action='store_true',
                       help='保留临时视频文件')
    parser.add_argument('--temp-dir', help='临时文件目录（默认系统临时目录）')
    parser.add_argument('--image-roi', default='0,0,1280,800',
                       help='图像ROI，格式: x,y,width,height (默认: 0,0,1280,800)')
    parser.add_argument('--expected-size', default='1280,800',
                       help='期望尺寸，格式: width,height (默认: 1280,800)')
    
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output, exist_ok=True)
    
    # 创建临时目录
    if args.temp_dir:
        temp_dir = args.temp_dir
        os.makedirs(temp_dir, exist_ok=True)
    else:
        temp_dir = tempfile.mkdtemp(prefix='dms_oneclick_')
    
    try:
        time_ranges = parse_time_ranges(args.time)
        frame_ranges = parse_frame_ranges(args.frames) if args.frames else None
        
        # 验证参数匹配
        if frame_ranges and len(frame_ranges) != len(time_ranges):
            raise ValueError("时间范围和帧范围的数量必须一致")
        
        # 解析图像处理参数
        image_roi = list(map(int, args.image_roi.split(',')))
        expected_size = list(map(int, args.expected_size.split(',')))
        
        progress_manager = OneClickProgressManager(len(time_ranges))
        progress_manager.start_display()
        
        # 第一阶段：视频裁剪
        progress_manager.set_phase("视频裁剪阶段")
        processor = VideoProcessor(args.input, temp_dir, args.roi, progress_manager)
        
        video_segments = []
        with ThreadPoolExecutor(max_workers=args.threads) as executor:
            futures = []
            for i, (start, end) in enumerate(time_ranges):
                frame_range = frame_ranges[i] if frame_ranges else None
                future = executor.submit(processor.process_segment, start, end, i, frame_range)
                futures.append(future)
            
            for future in as_completed(futures):
                result = future.result()
                if result:
                    video_segments.append(result)
        
        if not video_segments:
            raise Exception("没有成功裁剪的视频片段")
        
        # 第二阶段：DMS分析
        progress_manager.set_phase("DMS分析阶段")
        
        analysis_results = []
        with ThreadPoolExecutor(max_workers=1) as executor:  # DMS分析使用单线程避免冲突
            futures = []
            for i, video_info in enumerate(video_segments):
                future = executor.submit(analyze_video_segment, video_info, i, progress_manager, 
                                       args.output, image_roi, expected_size)
                futures.append(future)
            
            for future in as_completed(futures):
                result = future.result()
                analysis_results.append(result)
        
        # 停止进度显示
        progress_manager.stop_display()
        
        # 显示最终结果
        print("\033[2J\033[H", end="")
        print("=" * 80)
        print("🎉 一键式DMS视频分析完成！")
        print("=" * 80)
        print(f"原始视频: {args.input}")
        print(f"处理片段: {len(time_ranges)}")
        print(f"成功裁剪: {len(video_segments)}")
        print(f"成功分析: {sum(analysis_results)}")
        print(f"结果保存: {args.output}")
        print("=" * 80)
        
        # 生成使用说明
        print("\n📋 结果说明:")
        print("- 分析结果保存在 output_video/ 目录下")
        print("- 可视化视频文件以 '_out.mp4' 结尾")
        print("- JSON数据保存在 output_json/ 目录下")
        
        # 清理临时文件
        if not args.keep_temp:
            shutil.rmtree(temp_dir)
            print(f"\n🗑️  临时文件已清理")
        else:
            print(f"\n📁 临时文件保留在: {temp_dir}")
        
        print(f"\n✨ 分析完成！查看 output_video/ 目录下的可视化结果")
        
    except KeyboardInterrupt:
        if 'progress_manager' in locals():
            progress_manager.stop_display()
        print("\n\n⚠️  用户中断处理...")
        if not args.keep_temp and 'temp_dir' in locals():
            shutil.rmtree(temp_dir, ignore_errors=True)
    except Exception as e:
        if 'progress_manager' in locals():
            progress_manager.stop_display()
        print(f"\n\n❌ 处理过程中发生错误: {e}")
        if not args.keep_temp and 'temp_dir' in locals():
            shutil.rmtree(temp_dir, ignore_errors=True)
        raise


if __name__ == '__main__':
    main()

"""
🚀 使用示例：

# 基本用法：分析视频的指定时间段
python3 dms_one_click_analyzer.py -i input.mp4 -o output_dir -t "00:01:00-00:01:30;00:02:00-00:02:30"

# 指定帧范围和ROI
python3 dms_one_click_analyzer.py -i input.mp4 -o output_dir -t "00:01:00-00:01:30" -f "0-100" --roi 1920:1080:0:0

# 保留临时文件用于调试
python3 dms_one_click_analyzer.py -i input.mp4 -o output_dir -t "00:01:00-00:01:30" --keep-temp

# 自定义图像处理参数
python3 dms_one_click_analyzer.py -i input.mp4 -o output_dir -t "00:01:00-00:01:30" --image-roi 0,0,1280,800 --expected-size 1280,800
""" 