### Develop

### Test

### Debug

#### debug tool

* http://192.168.3.138:8880/root/tool_kit.git仓库的tool/dms_postmortem分支
  dms_postmortem_optimised.py脚本，用于将问题视频制作成有详细报警信息渲染的分析视频。
  cut_video_fromwhatyouwant.py脚本，用于裁剪问题视频片段

#### debug大概流程

* 先找到问题视频的时间点，问题是否和描述一致？是否真的是误报或者漏报了？可以先通过录制的log信息快速查看下原因。
  主要是[DMS Distraction]，[DMS Fatigue]，[DMS CALIBRATION]这几个模块的重要信息。
* 如果不能快速定位，那么先对齐版本，主要是算法库和模型，因为我们复现是在x86上进行的，模型和算法库是拆开的。

  * 对齐算法库：将编译的libtx_dms.so x86算法库，和test_dms_internal_postmortem可执行程序，拷贝到dms_postmortem_optimised.py脚本同一路径下。
  * 对齐模型：比如你现在要用***********机器进行测试，那么将你使用的模型拷贝到***********上,然后重新启动机器上的tx_dms_oax_test_tool_update服务。以及在当前路径下使用ip_port.json指定ip和port。
    * scp resourc/oax4600/model/*ovm root@***********:/tmp/model
    * ![1748254280982](images/BYD_DDAW_ADDW_QUICKSTART_GUIDE/1748254280982.png)
* 对齐实车环境，因为车机信息是缺失的，所以我们现在只能先对齐标定信息。在当前路径下使用calidata.json快速对齐标定信息。这个标定信息可以通过log中的[DMS CALIBRATION]来查看。

  * ![1748254465522](images/BYD_DDAW_ADDW_QUICKSTART_GUIDE/1748254465522.png)
* 版本和实车信息对齐后就可以先通过cut_video_fromwhatyouwant.py脚本裁剪出问题时间点的视频（最好能前后+5s~10s），再使用dms_postmortem_optimised.py脚本来渲染生成分析视频了。具体使用可以看tool/dms_postmortem分支中dms_postmortem目录下的readme.md.
* debug问题分类

  * 是否真的存在？
  * 已知还是新增？
  * 已知的（能解决？如何解决，解决进度怎样，解决预期。还是不能解决？不能解决原因？
  * 新增的（为什么不是已知的，分析具体原因，解决计划，解决预期
  * 具体原因？（模型还是后处理算法？算法的鲁棒性不够？逻辑缺陷？标定不准确？测试方法的更新？
