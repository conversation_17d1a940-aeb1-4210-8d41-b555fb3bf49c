#!/usr/bin/env python3
"""
测试优化版视频切割工具
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from cut_video_fromwhatyouwant_optimized import (
    Config, InputValidator, ResourceMonitor, 
    parse_time_ranges, FFmpegProcessor
)

def test_config():
    """测试配置管理"""
    print("测试配置管理...")
    config = Config()
    assert config.get('ffmpeg_preset') == 'medium'
    assert config.get('max_memory_usage') == 80
    print("✅ 配置管理测试通过")

def test_input_validator():
    """测试输入验证"""
    print("测试输入验证...")
    
    # 测试时间格式验证
    assert InputValidator.validate_time_format("12:34:56") == True
    assert InputValidator.validate_time_format("25:00:00") == False
    assert InputValidator.validate_time_format("12:60:00") == False
    assert InputValidator.validate_time_format("12:34") == False
    
    # 测试ROI验证
    assert InputValidator.validate_roi("1280:720:0:0", 1920, 1080) == True
    assert InputValidator.validate_roi("2000:720:0:0", 1920, 1080) == False
    assert InputValidator.validate_roi("1280:720:1000:0", 1920, 1080) == False
    
    print("✅ 输入验证测试通过")

def test_time_ranges():
    """测试时间范围解析"""
    print("测试时间范围解析...")
    
    ranges = parse_time_ranges("00:01:00-00:01:30;00:02:00-00:02:30")
    assert len(ranges) == 2
    assert ranges[0] == ("00:01:00", "00:01:30")
    assert ranges[1] == ("00:02:00", "00:02:30")
    
    print("✅ 时间范围解析测试通过")

def test_resource_monitor():
    """测试资源监控"""
    print("测试资源监控...")
    
    monitor = ResourceMonitor()
    thread_count = monitor.get_optimal_thread_count()
    assert thread_count > 0
    assert thread_count <= 16
    
    print(f"✅ 资源监控测试通过，推荐线程数: {thread_count}")

def main():
    """运行所有测试"""
    print("开始测试优化版视频切割工具...")
    print("=" * 50)
    
    try:
        test_config()
        test_input_validator()
        test_time_ranges()
        test_resource_monitor()
        
        print("=" * 50)
        print("🎉 所有测试通过！优化版本工作正常")
        return 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
