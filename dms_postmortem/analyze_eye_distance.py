import os
import json
import matplotlib.pyplot as plt
from collections import defaultdict

def calculate_eye_distance(json_dir):
    """
    计算时间序列下眼睛横向距离的变化情况
    :param json_dir: 包含JSON文件的目录路径
    :return: 包含时间戳和距离的字典列表
    """
    results = []
    
    # 遍历目录下所有JSON文件
    for filename in sorted(os.listdir(json_dir)):
        if not filename.endswith('.json'):
            continue
            
        filepath = os.path.join(json_dir, filename)
        try:
            with open(filepath, 'r') as f:
                data = json.load(f)
                
                # 提取左右眼信息
                left_eye = data["dms_result"]["face_info"]["left_eye_landmark"]
                right_eye = data["dms_result"]["face_info"]["right_eye_landmark"]
                
                # 定义边界值范围
                MIN_DISTANCE = 32
                MAX_DISTANCE = 64
                
                # 计算原始距离并应用边界值
                left_raw = abs(2*left_eye["eye_coutours"][0]["x"] - 2*left_eye["eye_coutours"][4]["x"])
                right_raw = abs(2*right_eye["eye_coutours"][0]["x"] - 2*right_eye["eye_coutours"][4]["x"])
                
                left_clamped = max(MIN_DISTANCE, min(MAX_DISTANCE, left_raw))
                right_clamped = max(MIN_DISTANCE, min(MAX_DISTANCE, right_raw))
                
                # 计算大值/小值比例
                # max_val = max(left_raw, right_raw)
                # min_val = min(left_raw, right_raw)
                max_val = max(left_clamped, right_clamped)
                min_val = min(left_clamped, right_clamped)
                ratio = max_val / min_val if min_val > 0 else None
                
                # 存储结果
                results.append({
                    "frame_id": data["dms_result"]["result_frame_id"],
                    "timestamp": data["ts"],
                    "left_raw_distance": left_raw,
                    "right_raw_distance": right_raw,
                    "left_clamped_distance": left_clamped,
                    "right_clamped_distance": right_clamped,
                    "max_min_ratio": ratio,
                    "filename": filename
                })
                
        except Exception as e:
            print(f"Error processing {filename}: {str(e)}")
    
    # 按照frame_id排序结果
    return sorted(results, key=lambda x: x["frame_id"])

def plot_eye_distance(data, output_dir):
    """
    绘制眼睛横向距离变化曲线图
    :param data: 计算结果数据
    :param output_dir: 输出目录
    """
    # timestamps = [d["timestamp"] for d in data]
    frameid = [d["frame_id"] for d in data]
    
    # left_distances = [d["left_eye_distance"] for d in data]
    # right_distances = [d["right_eye_distance"] for d in data]
    
    # 创建共享x轴的两个子图
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), sharex=True)
    
    # 子图1: 显示距离
    ax1.plot(frameid, [d["left_raw_distance"] for d in data], 'b-', alpha=0.5, label='Left Raw')
    ax1.plot(frameid, [d["left_clamped_distance"] for d in data], 'b-', linewidth=2, label='Left Clamped')
    ax1.plot(frameid, [d["right_raw_distance"] for d in data], 'g-', alpha=0.5, label='Right Raw')
    ax1.plot(frameid, [d["right_clamped_distance"] for d in data], 'g-', linewidth=2, label='Right Clamped')
    ax1.axhline(y=32, color='r', linestyle='--', label='Boundary (32)')
    ax1.axhline(y=64, color='r', linestyle='--', label='Boundary (64)')
    ax1.set_ylabel('Distance (pixels)')
    ax1.set_title('Eye Distance with Boundary Constraints')
    ax1.legend()
    ax1.grid(True)
    
    # 子图2: 显示比例
    ratios = [d["max_min_ratio"] for d in data]
    valid_indices = [i for i, ratio in enumerate(ratios) if ratio is not None]
    if valid_indices:
        valid_frameid = [frameid[i] for i in valid_indices]
        valid_ratios = [ratios[i] for i in valid_indices]
        ax2.plot(valid_frameid, valid_ratios, 'r-', label='Max/Min Ratio')
    ax2.set_ylabel('Ratio')
    ax2.set_xlabel('Frame ID')
    ax2.set_title('Max/Min Value Ratio Over Time')
    ax2.legend()
    ax2.grid(True)
    
    # 调整子图间距
    plt.tight_layout()
    
    # 保存图表
    output_path = os.path.join(output_dir, 'eye_distance_plot.png')
    plt.savefig(output_path)
    print(f"Plot saved to {output_path}")
    # plt.show()

def save_to_csv(data, output_dir):
    """
    将结果保存为CSV文件
    :param data: 计算结果数据
    :param output_dir: 输出目录
    """
    import csv
    
    output_path = os.path.join(output_dir, 'eye_distance_results.csv')
    with open(output_path, 'w', newline='') as csvfile:
        fieldnames = [
            'frame_id', 'timestamp',
            'left_raw_distance', 'right_raw_distance',
            'left_clamped_distance', 'right_clamped_distance',
            'max_min_ratio',
            'filename'
        ]
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for row in data:
            writer.writerow(row)
    
    print(f"CSV results saved to {output_path}")

if __name__ == "__main__":
    # 配置输入输出目录
    base_dir = os.path.dirname(os.path.abspath(__file__))
    json_dir = os.path.join(base_dir, "output_json/front_视频4_002000_002020_1920_1080_0_0_20250421_155911")
    output_dir = os.path.join(base_dir, "output_json/analysis_results")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 计算眼睛距离
    results = calculate_eye_distance(json_dir)
    
    # 保存结果
    save_to_csv(results, output_dir)
    plot_eye_distance(results, output_dir)