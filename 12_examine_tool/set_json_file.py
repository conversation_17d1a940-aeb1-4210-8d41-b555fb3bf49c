# 2017 by <PERSON> , MIT License

import json
import sys

from PyQt5.QtCore import QAbstractItemModel, QModelIndex, Qt, QAbstractListModel, QMimeData, \
    QDataStream, QByteArray, QJsonDocument, QVariant, QJsonValue, QJsonParseError
from PyQt5.QtWidgets import QApplication, QTreeView, QPushButton, QVBoxLayout, QWidget, QInputDialog, QFileDialog

class QJsonTreeItem(object):
    def __init__(self, parent=None):
        # 初始化父节点和子节点列表
        self.mParent = parent
        self.mChilds = []
        self.mKey = ''
        self.mType = None
        self.mValue = None

    def appendChild(self, item):
        # 添加子节点
        self.mChilds.append(item)

    def child(self, row: int):
        # 获取指定索引的子节点
        return self.mChilds[row]

    def parent(self):
        # 获取父节点
        return self.mParent

    def childCount(self):
        # 返回子节点的数量
        return len(self.mChilds)

    def row(self):
        # 返回当前节点在其父节点中的索引
        if self.mParent is not None:
            return self.mParent.mChilds.index(self)
        return 0

    def setKey(self, key: str):
        # 设置键
        self.mKey = key

    def setValue(self, value: str):
        # 设置值
        self.mValue = value

    def getValue(self):
        # 返回节点的值
        return self.mValue

    def setType(self, type: QJsonValue.Type):
        # 设置类型
        self.mType = type

    def key(self):
        # 获取键
        return self.mKey

    def value(self):
        # 获取值
        return self.mValue

    def type(self):
        # 获取类型
        return self.mType

    def setData(self, column, value):
        # 设置数据
        if column == 0:
            self.setKey(value)
        elif column == 1:
            self.setValue(value)
        elif column == 2:
            self.setType(value)

    def toDict(self):
        if self.mType == QJsonValue.Object or isinstance(self.mType, dict):
            result = {}
            for child in self.mChilds:
                result[child.key()] = child.toDict()
            return result
        elif self.mType == QJsonValue.Array :
            result = []
            for child in self.mChilds:
                result.append(child.toDict())
            return result
        elif  self.mType == list:
            result = []
            for child in self.mChilds:
                result.append(child.toDict())
            return result
        elif self.mType == dict:
            result = {}
            for child in self.mChilds:
                result[child.key()] = child.toDict()
            return result
        else:
            return self.mValue
    def load(self, value, parent=None):
        # 递归加载 JSON 数据到树节点中
        rootItem = QJsonTreeItem(parent)
        rootItem.setKey("root")
        jsonType = None
        # print('1111111111111111111111111111')
        try:
            value = value.toVariant()
            jsonType = value.type()
        except AttributeError:
            pass

        try:
            value = value.toObject()
            jsonType = value.type()
        except AttributeError:
            pass
            
        if isinstance(value, dict):
            # 处理键值对
            for key in value:
                v = value[key]
                child = self.load(v, rootItem)
                child.setKey(key)
                try:
                    child.setType(v.type())
                except AttributeError:
                    child.setType(v.__class__)
                rootItem.appendChild(child)

        elif isinstance(value, list):
            # 处理列表中的值
            for i, v in enumerate(value):
                child = self.load(v, rootItem)
                child.setKey(str(i))
                child.setType(v.__class__)
                rootItem.appendChild(child)

        else:
            # 处理单个值
            rootItem.setValue(value)
            try:
                rootItem.setType(value.type())
            except AttributeError:
                if jsonType is not None:
                    rootItem.setType(jsonType)
                else:
                    rootItem.setType(value.__class__)

        return rootItem


class QJsonModel(QAbstractItemModel):
    def __init__(self, parent=None):
        # 初始化模型
        super().__init__(parent)
        self.mRootItem = QJsonTreeItem()
        self.mHeaders = ["key", "value", "type"]

    def load(self, fileName):
        # 从文件加载 JSON 数据
        if fileName is None or fileName is False:
            return False

        with open(fileName, "rb") as file:
            if file is None:
                return False
            self.loadJson(file.read())

    def loadJson(self, json_data, data_type=None):
        # 从字节数据加载 JSON 数据
        if data_type is not None:
            json_str = json.dumps(json_data)
            json_data = QByteArray(json_str.encode('utf-8'))
        error = QJsonParseError()
        self.mDocument = QJsonDocument.fromJson(json_data, error)
        if self.mDocument is not None:
            self.beginResetModel()
            if self.mDocument.isArray():
                self.mRootItem = QJsonTreeItem()  # 重新创建根节点
                self.mRootItem.load(list(self.mDocument.array()))
                self.mRootItem.setType(QJsonValue.Array)  # 设置根节点类型
            else:
                self.mRootItem = QJsonTreeItem()  # 重新创建根节点
                self.mRootItem = self.mRootItem.load(self.mDocument.object())
                self.mRootItem.setType(QJsonValue.Object)  # 设置根节点类型
            self.endResetModel()
              # 调试信息
            return True

        print("QJsonModel: error loading Json")
        return False

    def data(self, index: QModelIndex, role: int = ...):
        # 返回指定索引和角色的数据
        if not index.isValid():
            return QVariant()

        item = index.internalPointer()
        col = index.column()

        if role == Qt.DisplayRole:
            if col == 0:
                return str(item.key())
            elif col == 1:
                return str(item.value())
            elif col == 2:
                return str(item.type())

        return QVariant()

    def headerData(self, section: int, orientation: Qt.Orientation, role: int = ...):
        # 返回表头数据
        if role != Qt.DisplayRole:
            return QVariant()

        if orientation == Qt.Horizontal:
            return self.mHeaders[section]

        return QVariant()

    def index(self, row: int, column: int, parent: QModelIndex = ...):
        # 创建并返回指定位置的索引
        if not self.hasIndex(row, column, parent):
            return QModelIndex()

        if not parent.isValid():
            parentItem = self.mRootItem
        else:
            parentItem = parent.internalPointer()
        try:
            childItem = parentItem.child(row)
            return self.createIndex(row, column, childItem)
        except IndexError:
            return QModelIndex()

    def parent(self, index: QModelIndex):
        # 返回指定索引的父索引
        if not index.isValid():
            return QModelIndex()

        childItem = index.internalPointer()
        parentItem = childItem.parent()

        if parentItem == self.mRootItem:
            return QModelIndex()

        return self.createIndex(parentItem.row(), 0, parentItem)

    def rowCount(self, parent: QModelIndex = ...):
        # 返回指定父索引的子节点数量
        if parent.column() > 0:
            return 0
        if not parent.isValid():
            parentItem = self.mRootItem
        else:
            parentItem = parent.internalPointer()

        return parentItem.childCount()

    def columnCount(self, parent: QModelIndex = ...):
        # 返回列数
        return 3
    
    
    
    def setData(self, index: QModelIndex, value, role: int = ...):
        # 设置指定索引和角色的数据
        if not index.isValid():
            return False
        item = index.internalPointer()
        # temp_data =  item.getValue()
        col = index.column()
        # print("setData", col, value)
        
        if value !='' and role == Qt.EditRole :
            try:
                if '.' in value:
                    value = float(value)
                else:
                    value = int(value)
            except ValueError:
                # 如果转换失败，保持原值
                value = str(value)  
            if col == 0:
                item.setKey(value)
            elif col == 1:
                item.setValue(value)
            # elif col == 2:
            #     item.setType(value)

            self.dataChanged.emit(index, index, [Qt.EditRole])
            return True

        return False
    def saveToJson(self, fileName):
        # 将模型数据保存为 JSON 文件
        data = self.mRootItem.toDict()



        # 将数据转换为 Python 字典
        python_data = QJsonDocument.fromVariant(data).toVariant()

        with open(fileName, "w", encoding="utf-8") as file:
            json.dump(python_data, file, indent=4)
        # print(data)
        # json_data = QJsonDocument.fromVariant(data).toJson(QJsonDocument.Compact)

        # with open(fileName, "wb") as file:
        #     # json.dump(json_data, file, indent=4)
        #     file.write(json_data)
    
    def flags(self, index: QModelIndex):
        # 返回指定索引的标志
        if not index.isValid():
            return Qt.NoItemFlags

        return super().flags(index) | Qt.ItemIsEditable
    
    def insertRow(self, row, parent=QModelIndex()):
        # 插入新行
        if not parent.isValid():
            parentItem = self.mRootItem
        else:
            parentItem = parent.internalPointer()

        self.beginInsertRows(parent, row, row)
        new_item = QJsonTreeItem(parentItem)
        parentItem.appendChild(new_item)
        self.endInsertRows()
        return True

if __name__ == '__main__':
    app = QApplication(sys.argv)

    view = QTreeView()
    view.setDragEnabled(True)
    view.setEditTriggers(QTreeView.DoubleClicked | QTreeView.SelectedClicked)
    model = QJsonModel()

    view.setModel(model)
    def addNewItem():
        model.saveToJson("temp.json")
    button = QPushButton("Add New Item")
    button.clicked.connect(addNewItem)
    layout = QVBoxLayout()
    layout.addWidget(view)
    layout.addWidget(button)
    widget = QWidget()
    widget.setLayout(layout)
    widget.show()

    model.load("./c66_dms_config.json")
    view.show()
    view.resize(520, 435)

    sys.exit(app.exec_())