import json
import sys
from PyQt5.QtWidgets import QApp<PERSON>, QMainWindow, QWidget, QShortcut, QLabel, QLineEdit, QPushButton, QTreeView, QFileDialog,QRadioButton
from PyQt5.uic import loadUi
from PyQt5.QtGui import QPixmap ,QIntValidator, QPainter, QPen, QColor, QKeySequence, QFont,QImage, QPolygon
from PyQt5.QtCore import Qt, QPoint
import numpy as np
import pandas as pd
from set_json_file import QJsonModel


def convert_linux_to_windows_path(linux_path, linux_root, windows_root):

    linux_path = linux_path.replace(linux_root, windows_root)
    # 将Linux路径中的正斜杠替换为Windows路径中的反斜杠
    windows_path = linux_path.replace('/', '\\')
    
    # 替换根目录
    
    
    return windows_path


class DispWindow(QMainWindow):

    # image_signal = pyqtSignal(QPixmap)
    def __init__(self):
        super().__init__()
        loadUi('ui/untitled.ui', self)

        self.json_model = QJsonModel()
        self.path1 = None
        self.list_data = []
        self.read_data = False
        self.index_data = 0
        self.ui_checked = True
        self.region_hulls = None
        self.kb_img = None
        self.is_drawregion = False
        
        # 设置窗口标题
        self.setWindowTitle('展示画面')
        self.imgLabel = self.findChild(QLabel, 'label')
        self.maneLabel = self.findChild(QLabel, 'label_3')
        self.tree_view = self.findChild(QTreeView, 'treeView')

        self.pathButton = self.findChild(QPushButton, 'pushButton')
        self.pathButton.clicked.connect(self.on_path_button_clicked)
        self.okButton = self.findChild(QPushButton, 'pushButton_2')
        self.okButton.clicked.connect(self.on_OK_button_clicked)

        self.prePageButton = self.findChild(QPushButton, 'pushButton_3')
        self.nextPageButton = self.findChild(QPushButton, 'pushButton_4')
        self.skipButton = self.findChild(QPushButton, 'pushButton_5')
        self.nextPageButton.clicked.connect(self.on_next_page_button_clicked)
        self.prePageButton.clicked.connect(self.on_pre_page_button_clicked)
        self.skipButton.clicked.connect(self.on_skip_button_clicked)
         # 连接按钮的点击信号到槽函数
        # 创建快捷键
        self.shortcut_pre = QShortcut(QKeySequence(Qt.Key_Left), self)
        self.shortcut_next = QShortcut(QKeySequence(Qt.Key_Right), self)
          # 连接快捷键到槽函数
        self.shortcut_pre.activated.connect(self.on_pre_page_button_clicked)
        self.shortcut_next.activated.connect(self.on_next_page_button_clicked)
        

        self.UIButton = self.findChild(QRadioButton, 'radioButton')
        self.UIButton.toggled.connect(self.on_ui_button_toggled)
        self.UIButton.setChecked(True)





        self.pathLabel = self.findChild(QLineEdit, 'lineEdit')
        self.indexLabel = self.findChild(QLineEdit, 'lineEdit_2')
        # self.indexLabel.setValidator(QIntValidator())  # 限制输入为整数

        self.tree_view.setModel(self.json_model)
        self.tree_view.setColumnWidth(0, 350)  # key 列宽度
        self.tree_view.setColumnWidth(1, 350)  # value 列宽度
        self.tree_view.setColumnWidth(2, 50)  # type 列宽度
        



        # pixmap = QPixmap('image_1177.jpg')
        # self.imgLabel.setPixmap(pixmap)
    
    def closeEvent(self, event):
        # 发送关闭信号
        pass

    def update_image(self, image):
        # 更新图片
        pixmap = QPixmap.fromImage(image)
        self.imgLabel.setPixmap(pixmap)

    def on_path_button_clicked(self):
        self.path1, _ = QFileDialog.getOpenFileName(self, "请选择文件", "", "xlsx Files (*.xlsx)")
        self.pathLabel.setText(self.path1)
        if self.path1 != None:
            self.read_data = True
            self.list_data.clear()
            df = pd.read_excel(self.path1)
            # print(df.head())
            for index, row in df.iterrows():
                case_name_frame = row['case_withframe']
                image_path = row['image_path']
                result_path = row['json_path']
                self.list_data.append([index, case_name_frame, image_path, result_path])
                if self.read_data :
                    self.read_data = False
                    self.index_data =0
                    self.updata_image_json(self.list_data[self.index_data])
                    # self.region_hulls = None
                    # self.kb_img = None
                    # print(index, case_name_frame, image_path, result_path)


    def on_OK_button_clicked(self):
        self.path1 = self.pathLabel.text()
        # print(self.path1)
        # if self.path1 :
        df = pd.read_excel(self.path1)
        # print(df.head())
        self.read_data = True
        for index, row in df.iterrows():
               
                case_name_frame = row['case_withframe']
                image_path = row['image_path']
                result_path = row['json_path']
                print(index, case_name_frame, image_path, result_path)
                self.list_data.append([index, case_name_frame, image_path, result_path])
                if self.read_data :
                    self.read_data = False
                    self.index_data =0
                    self.updata_image_json(self.list_data[self.index_data])
                    self.region_hulls = None
                    self.kb_img = None

    def on_next_page_button_clicked(self):
        self.index_data += 1
        if self.index_data > len(self.list_data)-1: 
           self.index_data -= 1 
        
        self.updata_image_json(self.list_data[self.index_data])
        pass

    def on_pre_page_button_clicked(self):
        self.index_data -= 1
        if self.index_data < 0: 
           self.index_data += 1
        self.updata_image_json(self.list_data[self.index_data])

    def on_skip_button_clicked(self):

        index_ = int(self.indexLabel.text())
        if index_ < 0 or index_ > len(self.list_data)-1: 
            pass
        else:
            self.index_data = index_

        self.updata_image_json(self.list_data[self.index_data])

    def on_ui_button_toggled(self,checked):
        self.ui_checked = checked



    def updata_image_json(self, data):
        self.maneLabel.setText(data[1])


        img_path = data[2] #convert_linux_to_windows_path(data[2], '/home/<USER>/', 'Y:\\') 
        jons_path = data[3] #convert_linux_to_windows_path(data[3], '/home/<USER>/', 'V:\\') 
 
    
        with open(jons_path, "rb") as file:
            if file is None:
                return False
            self.data_json = file.read()
        message = json.loads(self.data_json)
      

        pixmap = QPixmap(img_path)
        pixmap = pixmap.scaled(1280, 720)
        

        if self.ui_checked:
            self.draw_point(pixmap, message['dms_result']['face_info']['landmarks'])
            self.process_frame_txt(pixmap,message)

        message['dms_result']['distraction_params'] = json.loads(message['dms_result']['distraction_params'])  
        self.imgLabel.setPixmap(pixmap)
        self.json_model.loadJson(message, data_type=dict)
        self.tree_view.expandAll()
    
    def draw_point(self, pixmap, point_list):
        painter = QPainter(pixmap)
        pen = QPen(Qt.red, 3)
        painter.setPen(pen)
        for point in point_list:
            # print(point)
            painter.drawPoint(2*point['x'], 2*point['y'])
        painter.end()

    # def draw_line(self, point_list1, point_list2 ):
    #     pass

    def draw_box(self, line_list ):
        pass

    def draw_text_on_pixmap(self, pixmap, text, position, color_=QColor(Qt.red), font=QFont("Arial", 10)):
        """
        在给定的QPixmap上绘制文本。
        
        :param pixmap: 要在其上绘制文本的QPixmap对象
        :param text: 要绘制的文本字符串
        :param position: 文本左上角的位置，元组形式 (x, y)
        :param font: 文本使用的字体
        :param color: 文本的颜色
        """
        painter = QPainter(pixmap)
        painter.setFont(font)
        painter.setPen(color_)
        painter.drawText(position[0], position[1], text)
        painter.end()

    def display_distraction_params(self, pixmap, params, start_x=10, start_y=200, line_height=20):
        y = start_y
        for key, value in params.items():
            if key != "region_hulls":
                text = f"{key}: {value}"
                # cv2.putText(image, text, (start_x, y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
                self.draw_text_on_pixmap(pixmap,text,(start_x, y))
                y += line_height
    def process_frame_txt(self, pixmap, message):
      
        face_info = message["dms_result"]["face_info"]        
        normal_color = QColor(Qt.blue), # Red
        unnormal_color = QColor(Qt.green)
        warning_color = QColor(Qt.red)
        
        text_info = []
        
        color = normal_color
        text_info.append(("is_mask: " + str(face_info["isMask"]), (5, 10), color))
        text_info.append(("is_glass: " + str(face_info["isGlass"]), (5, 25), color))
        text_info.append(("is_irblock: " + str(face_info["isIRBlock"]), (5, 40), color))
        if message["dms_result"]["camera_status"] != 0:
            color = warning_color
        text_info.append(("cam_status: " + str(message["dms_result"]["camera_status"]), (5, 55), color))
        color = normal_color
        
        if face_info["left_eye_landmark"]["eye_score"] < 0.1:
            color = unnormal_color
        text_info.append(("left_eye_score: " + str(face_info["left_eye_landmark"]["eye_score"]), (150, 10), color))
        color = normal_color
        
        if face_info["left_eye_landmark"]["iris_score"] < 0.1:
            color = unnormal_color
        text_info.append(("left_iris_score: " + str(face_info["left_eye_landmark"]["iris_score"]), (150, 25), color))
        color = normal_color
        
        if face_info["left_eye_landmark"]["pupil_score"] < 0.1:
            color = unnormal_color
        text_info.append(("left_pupli_score: " + str(face_info["left_eye_landmark"]["pupil_score"]), (150, 40), color))
        color = normal_color

        if face_info["right_eye_landmark"]["eye_score"] < 0.1:
            color = unnormal_color
        text_info.append(("right_eye_score: " + str(face_info["right_eye_landmark"]["eye_score"]), (150, 60), color))
        color = normal_color
        
        if face_info["right_eye_landmark"]["iris_score"] < 0.1:
            color = unnormal_color
        text_info.append(("right_iris_score: " + str(face_info["right_eye_landmark"]["iris_score"]), (150, 80), color))
        color = normal_color
        
        if face_info["right_eye_landmark"]["pupil_score"] < 0.1:
            color = unnormal_color
        text_info.append(("right_pupli_score: " + str(face_info["right_eye_landmark"]["pupil_score"]), (150, 100), color))
        color = normal_color

        if face_info["left_close_eye_score"] > 0.7:
            color = unnormal_color
        text_info.append(("left_eye_close: " + str(round(face_info["left_close_eye_score"], 2)), (550, 10), color))
        color = normal_color
        if face_info["right_close_eye_score"] > 0.7:
            color = unnormal_color
        text_info.append(("right_eye_close: " + str(round(face_info["right_close_eye_score"], 2)), (550, 25), color))
        color = normal_color
        
        if face_info["mouth_opening"] >= 0.5:
            color = unnormal_color
        text_info.append(("mouth: " + str(round(face_info["mouth_opening"], 2)), (550, 40), color))
        color = normal_color
        
        if message["dms_result"]["drowsiness_type"] != 0:
            color = warning_color
        text_info.append(("drowsiness_type: " + str(message["dms_result"]["drowsiness_type"]), (550, 55), color))
        color = normal_color
        
        if message["dms_result"]["distraction_type"] != 0:
            color = warning_color
        text_info.append(("distraction_type: " + str(message["dms_result"]["distraction_type"]), (550, 75), color))
        color = normal_color
        
        if message["dms_result"]["calibrate_status"] == 2:
            color = warning_color
        else:
            if message["dms_result"]["calibrate_status"] == 1:
                color = unnormal_color  
        text_info.append(("calibrate_status: " + str(message["dms_result"]["calibrate_status"]), (350, 10), color))
        color = normal_color

        text_info.append(("face_yaw: " + str(int(face_info["yaw"])), (550, 90), color))
        text_info.append(("face_pitch: " + str(int(face_info["pitch"])), (550, 105), color))
        text_info.append(("right_eye_yaw: " + str(int(face_info["right_eye_landmark"]["yaw"])), (550, 120), color))
        text_info.append(("right_eye_pitch: " + str(int(face_info["right_eye_landmark"]["pitch"])), (550, 135), color))
        text_info.append(("left_eye_yaw: " + str(int(face_info["left_eye_landmark"]["yaw"])), (550, 150), color))
        text_info.append(("left_eye_pitch: " + str(int(face_info["left_eye_landmark"]["pitch"])), (550, 165), color))
        text_info.append(("face_roll: " + str(int(face_info["roll"])), (550, 180), color))

        text_info.append(("car_gear: " + str(message["dms_result"]["car_gear"]), (550, 250), color))
        text_info.append(("car_speed: " + str(message["dms_result"]["car_speed"]), (550, 270), color))
        text_info.append(("steer_rad: " + str(message["dms_result"]["car_steer_whl_snsr_rad"])[:6], (550, 290), color))
        text_info.append(("turn_light: " + str(message["dms_result"]["turn_light"]), (550, 310), color))

        text_info.append(("distraction_reason: ", (550, 200), color))
        if str(message["dms_result"]["distraction_reason"]) != "no_distraction":
            color = warning_color
        text_info.append((str(message["dms_result"]["distraction_reason"]), (550, 230), color))
        color = normal_color

      
        distraction_params_str = message["dms_result"].get("distraction_params", "{}")
        distraction_params = {}
        try:
            distraction_params = json.loads(distraction_params_str)
        except json.JSONDecodeError as e:
            print(f"Error parsing distraction_params: {e}")
        self.display_distraction_params(pixmap, distraction_params)

        for text, position, color in text_info:
            self.draw_text_on_pixmap(pixmap, text, position)
        
        if distraction_params != {}:
            self.mapping_point(pixmap, distraction_params)


       
        


    def mapping_point(self, pixmap, distraction_params):
        width, height = 800, 800

        if self.region_hulls is None:
            hulls = distraction_params["region_hulls"]
            self.region_hulls = hulls
            print("set_region_hulls...")

        if self.kb_img is None:
            self.kb_img = QImage(width, height, QImage.Format_RGB32)
            self.kb_img.fill(Qt.white)  # 填充背景色为白色

        if self.kb_img is not None and self.region_hulls is not None :
            painter = QPainter(self.kb_img)
            for hull in self.region_hulls:
                points = [QPoint(coord["x"], coord["y"]) for coord in hull]
                polygon = QPolygon(points)
                painter.setPen(Qt.green)
                painter.setBrush(Qt.NoBrush)
                painter.drawPolygon(polygon)
            
              # 绘制虚线
            painter.setPen(Qt.black)
            dash_length = 10
            gap_length = 10
            for y in range(0, height, dash_length + gap_length):
                painter.drawLine(400, y, 400, y + dash_length)
            for x in range(0, width, dash_length + gap_length):
                painter.drawLine(x, 400, x + dash_length, 400)

        if self.kb_img is not None:
            mapping_x = int(distraction_params["mapping_x"])
            mapping_y = int(distraction_params["mapping_y"])
            if mapping_x > 0 and mapping_y > 0:
                color = Qt.yellow
                if int(distraction_params["predict_result"]) != 0:
                    color = Qt.red
                painter.setPen(color)
                painter.setBrush(color)
                painter.drawEllipse(QPoint(mapping_x, mapping_y), 2, 2)
            
            painter.end()
             # 裁剪图像
        crop_x, crop_width, crop_y, crop_height = 200, 400, 200, 400
        cropped_region = self.kb_img.copy(crop_x, crop_y, crop_width, crop_height)


        target_x = 1280 - 400  # 右上角 x 坐标
        target_y = 0  # 右上角 y 坐标

        target_pixmap = QPixmap.fromImage(cropped_region)
        painter_b = QPainter(pixmap)
        painter_b.drawPixmap(target_x, target_y, target_pixmap)
        painter_b.end()


        





if __name__ == '__main__':
    app = QApplication(sys.argv)
    form = DispWindow()
    form.show()
    sys.exit(app.exec_())
